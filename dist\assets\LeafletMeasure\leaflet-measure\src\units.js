// units.js
// Unit configurations
// Factor is with respect to meters/sqmeters

export default {
  acres: {
    factor: 0.00024711,
    display: 'acres',
    decimals: 2
  },
  feet: {
    factor: 3.2808,
    display: 'feet',
    decimals: 0
  },
  kilometers: {
    factor: 0.001,
    display: 'kilometers',
    decimals: 2
  },
  hectares: {
    factor: 0.0001,
    display: 'hectares',
    decimals: 2
  },
  meters: {
    factor: 1,
    display: 'meters',
    decimals: 0
  },
  miles: {
    factor: 3.2808 / 5280,
    display: 'miles',
    decimals: 2
  },
  sqfeet: {
    factor: 10.7639,
    display: 'sqfeet',
    decimals: 0
  },
  sqmeters: {
    factor: 1,
    display: 'sqmeters',
    decimals: 0
  },
  sqmiles: {
    factor: 0.000000386102,
    display: 'sqmiles',
    decimals: 2
  }
};
