<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"> 
<html xmlns="https://www.w3.org/1999/xhtml"> 
<head> 
<title>Leaflet.Control.Compass</title> 
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" /> 
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=yes">

<link rel="stylesheet" href="examples/style.css" />
</head>

<body id="home">
<h2>Leaflet.Control.Compass</h2>
<p>
	A <b>Leaflet Control</b> to make simple <b>rotating compass</b>
	<iframe style="vertical-align:bottom" src="https://ghbtns.com/github-btn.html?user=ste<PERSON><PERSON><PERSON><PERSON>&amp;repo=leaflet-compass&amp;type=watch&amp;count=true" allowtransparency="true" frameborder="0" scrolling="0" width="104px" height="20px"></iframe>
	<br />
	Other useful stuff for <a href="https://opengeo.tech/maps/">Web Mapping...</a>
</p>

<div style="float:left">
	<img class="screenshot" src="images/leaflet-compass.png" alt="leaflet compass" height="300" width="500" />
</div>

<div class="box">
	<h4>Requirements</h4>
	<ul>
		<li>HTML5 API</li>
		<li>
			Tested on: <i>Safari, iOS7</i>
		</li>
	</ul>

	<h4>Features</h4>
	<ul id="ff">
		<li>Auto Rotation</li>
		<li>Custom Events</li>
		<li>De/Active methods</li>
		<li>Custom angle offset</li>
	</ul>

	<h4>Code repositories</h4>
	<a target="_blank" href="https://github.com/stefanocudini/leaflet-compass">Github.com</a>
	<br />	
	<a target="_blank" href="https://bitbucket.org/stefanocudini/leaflet-compass">Bitbucket.org</a>
	<br />
	<a target="_blank" href="https://npmjs.org/package/leaflet-compass">NPM</a>
	<br />	
	<a target="_blank" href="https://atmospherejs.com/package/leaflet-compass">Atmosphere Meteor JS</a>
	<br />	
</div>

<div class="box">
	<h4>Examples</h4>
	<ul id="exmaples">
		<li><a target="_blank" href="examples/simple.html">Simple</a></li>
	</ul>

	<h4>Website</h4>
	<a href="https://opengeo.tech/maps/leaflet-compass/">opengeo.tech/maps/leaflet-compass</a>
	<br />

	<h4>Download</h4>
	<ul>
		<li><a href="https://github.com/stefanocudini/leaflet-compass/archive/master.zip">Dev Last Version (.zip)</a></li>
		<li><a href="dist/leaflet-compass.src.js">Source Code (.js)</a></li>
		<li><a href="dist/leaflet-compass.min.js">Compressed (.min.js)</a></li>		
	</ul>
</div>

<div id="copy"><a href="https://opengeo.tech/">Opengeo.tech</a> &bull; <a rel="author" href="https://opengeo.tech/stefano-cudini/">Stefano Cudini</a></div>
<a href="https://github.com/stefanocudini/leaflet-compass"><img id="ribbon" src="https://s3.amazonaws.com/github/ribbons/forkme_right_darkblue_121621.png" alt="Fork me on GitHub"></a>

<hr />

<div id="comments">
	<div id="disqus_thread"></div>
</div>

<script type="text/javascript" src="/labs-common.js"></script>

</body>
</html>
