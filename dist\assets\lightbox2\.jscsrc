{"requireSpaceAfterLineComment": true, "requireSpaceAfterKeywords": ["do", "for", "if", "else", "switch", "case", "try", "catch", "void", "while", "with", "return", "typeof"], "requireSpaceBeforeBlockStatements": true, "requireParenthesesAroundIIFE": true, "requireSpacesInConditionalExpression": true, "disallowMultipleVarDecl": true, "requireBlocksOnNewline": true, "disallowEmptyBlocks": true, "disallowSpacesInsideParentheses": true, "disallowSpaceAfterObjectKeys": true, "requireSpaceBeforeObjectValues": true, "requireCommaBeforeLineBreak": true, "requireOperatorBeforeLineBreak": ["?", "=", "+", "-", "/", "*", "==", "===", "!=", "!==", ">", ">=", "<", "<="], "disallowSpaceAfterPrefixUnaryOperators": ["++", "--", "+", "-", "~", "!"], "disallowSpaceBeforePostfixUnaryOperators": ["++", "--"], "requireSpaceBeforeBinaryOperators": ["=", "+", "-", "/", "*", "==", "===", "!=", "!=="], "requireSpaceAfterBinaryOperators": ["=", ",", "+", "-", "/", "*", "==", "===", "!=", "!=="], "disallowMixedSpacesAndTabs": true, "disallowTrailingWhitespace": true, "disallowTrailingComma": true, "requireLineFeedAtFileEnd": true, "requireCapitalizedConstructors": true}