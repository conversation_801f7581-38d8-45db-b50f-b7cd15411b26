{"name": "leaflet-toolbar", "version": "0.4.0-alpha.2", "description": "Flexible, extensible toolbars for Leaflet maps.", "scripts": {"test": "grunt travis", "coveralls": "PHANTOM=`ls ./coverage | grep PhantomJS` && cat \"coverage/$PHANTOM/lcov.info\" | coveralls"}, "main": "dist/leaflet.toolbar.js", "peerDependencies": {"leaflet": "*"}, "devDependencies": {"autoprefixer": "~7.1.4", "chai": "~4.1.2", "grunt": "~1.0.1", "grunt-autoprefixer": "~3.0.4", "grunt-bump": "~0.8.0", "grunt-contrib-clean": "~1.1.0", "grunt-contrib-coffee": "~1.0.0", "grunt-contrib-concat": "~1.0.1", "grunt-contrib-cssmin": "~2.2.1", "grunt-contrib-jshint": "~1.1.0", "grunt-contrib-less": "~1.4.1", "grunt-contrib-nodeunit": "~1.0.0", "grunt-contrib-uglify": "~3.0.1", "grunt-contrib-watch": "~1.0.0", "grunt-contrib-connect": "~1.0.2", "grunt-gh-pages": "~2.0.0", "grunt-mocha": "~1.0.4", "grunt-karma": "~2.0.0", "jshint": "~2.9.5", "karma": "~1.7.1", "karma-chrome-launcher": "~2.2.0", "karma-coverage": "~1.1.1", "karma-firefox-launcher": "~1.0.1", "karma-mocha": "~1.3.0", "karma-mocha-reporter": "~2.2.4", "karma-phantomjs-launcher": "~1.0.4", "karma-safari-launcher": "~1.0.0", "leaflet": "~1.2.0", "matchdep": "~2.0.0", "mocha": "~3.5.2", "sinon": "~3.2.1", "uglify-js": "~3.1.0"}, "directories": {"examples": "./examples", "distribution": "./dist", "source": "./src", "tests": "./test"}, "repository": {"type": "git", "url": "https://github.com/Leaflet/Leaflet.toolbar.git"}, "keywords": ["maps", "leaflet", "toolbar", "user-interface"], "author": "<PERSON>", "license": "MIT", "readmeFilename": "README.md"}