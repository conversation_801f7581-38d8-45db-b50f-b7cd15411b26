{"version": 3, "file": "index.mjs", "sources": ["../src/string.js", "../src/storage/global.js", "../src/storage/localstorage.js", "../src/storage.js", "../src/formpopup.js", "../src/leaflet.delegate.js", "../src/bookmarks.js", "../index.js"], "sourcesContent": ["/**\n * Substitutes {{ obj.field }} in strings\n *\n * @param  {String}  str\n * @param  {Object}  object\n * @param  {RegExp=} regexp\n * @return {String}\n */\nexport function substitute(str, object, regexp) {\n  return str.replace(regexp || /{{([\\s\\S]+?)}}/g, function (match, name) {\n    name = trim(name);\n\n    if (name.indexOf(\".\") === -1) {\n      if (match.charAt(0) == \"\\\\\") return match.slice(1);\n      return object[name] != null ? object[name] : \"\";\n    } else {\n      // nested\n      let result = object;\n      name = name.split(\".\");\n      for (var i = 0, len = name.length; i < len; i++) {\n        if (name[i] in result) result = result[name[i]];\n        else return \"\";\n      }\n      return result;\n    }\n  });\n}\n\nconst alpha = \"abcdefghijklmnopqrstuvwxyz\";\n/**\n * Unique string from date. Puts character at the beginning,\n * for the sake of good manners\n *\n * @return {String}\n */\nexport function unique(prefix) {\n  return (\n    (prefix || alpha[Math.floor(Math.random() * alpha.length)]) +\n    new Date().getTime().toString(16)\n  );\n}\n\n/**\n * Trim whitespace\n * @param  {String} str\n * @return {String}\n */\nexport function trim(str) {\n  return str.replace(/^\\s+|\\s+$/g, \"\");\n}\n\n/**\n * Clean and trim\n * @param  {String} str\n * @return {String}\n */\nexport function clean(str) {\n  return trim(str.replace(/\\s+/g, \" \"));\n}\n", "/**\n * @type {Object}\n */\nconst data = {};\n\n/**\n * Object based storage\n * @class Storage.Engine.Global\n * @constructor\n */\nexport default class GlobalStorage {\n  constructor(prefix) {\n    /**\n     * @type {String}\n     */\n    this._prefix = prefix;\n  }\n\n  /**\n   * @param  {String}   key\n   * @param  {Function} callback\n   */\n  getItem(key, callback) {\n    callback(data[this._prefix + key]);\n  }\n\n  /**\n   * @param {String}   key\n   * @param {*}        item\n   * @param {Function} callback\n   */\n  setItem(key, item, callback) {\n    data[this._prefix + key] = item;\n    callback(item);\n  }\n\n  /**\n   * @param  {Function} callback\n   */\n  getAllItems(callback) {\n    const items = [];\n    for (const key in data) {\n      if (data.hasOwnProperty(key) && key.indexOf(this_prefix) === 0) {\n        items.push(data[key]);\n      }\n    }\n    callback(items);\n  }\n\n  /**\n   * @param  {String}   key\n   * @param  {Function} callback\n   */\n  removeItem(key, callback) {\n    this.getItem(key, (item) => {\n      if (item) {\n        delete data[this._prefix + key];\n      } else {\n        item = null;\n      }\n      if (callback) callback(item);\n    });\n  }\n}\n", "/**\n * @const\n * @type {RegExp}\n */\nconst JSON_RE = /^[\\{\\[](.)*[\\]\\}]$/;\n\n/**\n * LocalStoarge based storage\n */\nexport default class LocalStorage {\n  constructor(prefix) {\n    /**\n     * @type {String}\n     */\n    this._prefix = prefix;\n\n    /**\n     * @type {LocalStorage}\n     */\n    this._storage = window.localStorage;\n  }\n\n  /**\n   * @param  {String}   key\n   * @param  {Function} callback\n   */\n  getItem(key, callback) {\n    let item = this._storage.getItem(this._prefix + key);\n    if (item && JSON_RE.test(item)) {\n      item = JSON.parse(item);\n    }\n    callback(item);\n  }\n\n  /**\n   * @param  {Function} callback\n   */\n  getAllItems(callback) {\n    const items = [];\n    const prefixLength = this._prefix.length;\n    for (const key in this._storage) {\n      if (\n        this._storage.getItem(key) !== null &&\n        key.indexOf(this._prefix) === 0\n      ) {\n        this.getItem(key.substring(prefixLength), (item) => items.push(item));\n      }\n    }\n    callback(items);\n  }\n\n  /**\n   * @param  {String}   key\n   * @param  {Function} callback\n   */\n  removeItem(key, callback) {\n    const self = this;\n    this.getItem(key, (item) => {\n      this._storage.removeItem(self._prefix + key);\n      if (callback) callback(item);\n    });\n  }\n\n  /**\n   * @param  {String}   key\n   * @param  {*}        item\n   * @param  {Function} callback\n   */\n  setItem(key, item, callback) {\n    let itemStr = item.toString();\n    if (itemStr === \"[object Object]\") {\n      itemStr = JSON.stringify(item);\n    }\n    this._storage.setItem(this._prefix + key, itemStr);\n    callback(item);\n  }\n}\n", "import { unique } from \"./string\";\n\nimport XHR from \"./storage/xhr\";\nimport GlobalStorage from \"./storage/global\";\nimport LocalStorage from \"./storage/localstorage\";\n\n/**\n * @const\n * @enum {Number}\n */\nexport const EngineType = {\n  // XHR: 1, // we don't have it included, it's a stub\n  GLOBALSTORAGE: 2,\n  LOCALSTORAGE: 3,\n};\n\n/**\n * Persistent storage, depends on engine choice: localStorage/ajax\n * @param {String} name\n */\nexport default class Storage {\n  constructor(name, engineType) {\n    if (typeof name !== \"string\") {\n      engineType = name;\n      name = unique();\n    }\n\n    /**\n     * @type {String}\n     */\n    this._name = name;\n\n    /**\n     * @type {Storage.Engine}\n     */\n    this._engine = Storage.createEngine(\n      engineType,\n      this._name,\n      Array.prototype.slice.call(arguments, 2)\n    );\n  }\n\n  /**\n   * Engine factory\n   * @param  {Number} type\n   * @param  {String} prefix\n   * @return {Storage.Engine}\n   */\n  static createEngine(type, prefix, args) {\n    if (type === EngineType.GLOBALSTORAGE) {\n      return new GlobalStorage(prefix);\n    }\n    if (type === EngineType.LOCALSTORAGE) {\n      return new LocalStorage(prefix);\n    }\n  }\n\n  /**\n   * @param {String}   key\n   * @param {*}        item\n   * @param {Function} callback\n   */\n  setItem(key, item, callback) {\n    this._engine.setItem(key, item, callback);\n    return this;\n  }\n\n  /**\n   * @param  {String}   key\n   * @param  {Function} callback\n   */\n  getItem(key, callback) {\n    this._engine.getItem(key, callback);\n    return this;\n  }\n\n  /**\n   * @param  {Function} callback\n   */\n  getAllItems(callback) {\n    this._engine.getAllItems(callback);\n  }\n\n  /**\n   * @param  {String}   key\n   * @param  {Function} callback\n   */\n  removeItem(key, callback) {\n    this._engine.removeItem(key, callback);\n  }\n}\n", "import L from \"leaflet\";\nimport { unique, substitute } from \"./string\";\n\nconst modes = {\n  CREATE: 1,\n  UPDATE: 2,\n  SHOW: 3,\n  OPTIONS: 4,\n};\n\n/**\n * New bookmark form popup\n *\n * @class  FormPopup\n * @extends {L.Popup}\n */\nexport default L.Popup.extend(\n  /** @lends FormPopup.prototype */ {\n    statics: { modes },\n\n    /**\n     * @type {Object}\n     */\n    options: {\n      mode: modes.CREATE,\n      className: \"leaflet-bookmarks-form-popup\",\n      templateOptions: {\n        formClass: \"leaflet-bookmarks-form\",\n        inputClass: \"leaflet-bookmarks-form-input\",\n        inputErrorClass: \"has-error\",\n        idInputClass: \"leaflet-bookmarks-form-id\",\n        coordsClass: \"leaflet-bookmarks-form-coords\",\n        submitClass: \"leaflet-bookmarks-form-submit\",\n        inputPlaceholder: \"Bookmark name\",\n        removeClass: \"leaflet-bookmarks-form-remove\",\n        editClass: \"leaflet-bookmarks-form-edit\",\n        cancelClass: \"leaflet-bookmarks-form-cancel\",\n        editableClass: \"editable\",\n        removableClass: \"removable\",\n        menuItemClass: \"nav-item\",\n        editMenuText: \"Edit\",\n        removeMenuText: \"Remove\",\n        cancelMenuText: \"Cancel\",\n        submitTextCreate: \"+\",\n        submitTextEdit: '<span class=\"icon-checkmark\"></span>',\n      },\n      generateNames: false,\n      minWidth: 160,\n      generateNamesPrefix: \"Bookmark \",\n      template:\n        '<form class=\"{{ formClass }}\">' +\n        '<div class=\"input-group\"><input type=\"text\" name=\"bookmark-name\" ' +\n        'placeholder=\"{{ inputPlaceholder }}\" class=\"form-control {{ inputClass }}\" value=\"{{ name }}\">' +\n        '<input type=\"hidden\" class={{ idInputClass }} value=\"{{ id }}\">' +\n        '<button type=\"submit\" class=\"input-group-addon {{ submitClass }}\">' +\n        \"{{ submitText }}</button></div>\" +\n        '<div class=\"{{ coordsClass }}\">{{ coords }}</div>' +\n        \"</form>\",\n      menuTemplate:\n        '<ul class=\"nav {{ mode }}\" role=\"menu\">' +\n        '<li class=\"{{ editClass }}\"><a href=\"#\" class=\"{{ menuItemClass }}\">{{ editMenuText }}</a></li>' +\n        '<li class=\"{{ removeClass }}\"><a href=\"#\" class=\"{{ menuItemClass }}\">{{ removeMenuText }}</a></li>' +\n        '<li><a href=\"#\" class=\"{{ menuItemClass }} {{ cancelClass }}\">{{ cancelMenuText }}</a></li>' +\n        \"</ul>\",\n    },\n\n    /**\n     * @param  {Object}  options\n     * @param  {L.Layer} source\n     * @param  {Object=} bookmark\n     *\n     * @constructor\n     */\n    initialize: function (options, source, control, bookmark) {\n      /**\n       * @type {Object}\n       */\n      this._bookmark = bookmark;\n\n      /**\n       * @type {L.Control.Bookmarks}\n       */\n      this._control = control;\n\n      /**\n       * @type {L.LatLng}\n       */\n      this._latlng = source.getLatLng();\n\n      /**\n       * For dragging purposes we're not maintaining the usual\n       * link between the marker and Popup, otherwise it will simply be destroyed\n       */\n      source._popup_ = this;\n\n      L.Popup.prototype.initialize.call(this, options, source);\n    },\n\n    /**\n     * Add menu button\n     */\n    _initLayout: function () {\n      L.Popup.prototype._initLayout.call(this);\n\n      if (\n        this.options.mode === modes.SHOW &&\n        (this._bookmark.editable || this._bookmark.removable)\n      ) {\n        const menuButton = (this._menuButton = L.DomUtil.create(\n          \"a\",\n          \"leaflet-popup-menu-button\"\n        ));\n        this._container.insertBefore(menuButton, this._closeButton);\n        menuButton.href = \"#menu\";\n        menuButton.innerHTML = '<span class=\"menu-icon\"></span>';\n        L.DomEvent.disableClickPropagation(menuButton);\n        L.DomEvent.on(menuButton, \"click\", this._onMenuButtonClick, this);\n      }\n    },\n\n    /**\n     * Show options menu\n     */\n    _showMenu: function () {\n      this._map.fire(\"bookmark:options\", { data: this._bookmark });\n    },\n\n    /**\n     * @param  {MouseEvent} evt\n     */\n    _onMenuButtonClick: function (evt) {\n      L.DomEvent.preventDefault(evt);\n      this._showMenu();\n      this.close();\n    },\n\n    /**\n     * Renders template only\n     * @override\n     */\n    _updateContent: function () {\n      let content;\n      if (this.options.mode === modes.SHOW) {\n        content = this._control._getPopupContent(this._bookmark);\n      } else {\n        let template = this.options.template;\n        let submitText = this.options.templateOptions.submitTextCreate;\n        if (this.options.mode === modes.OPTIONS) {\n          template = this.options.menuTemplate;\n        }\n        if (this.options.mode === modes.UPDATE) {\n          submitText = this.options.templateOptions.submitTextEdit;\n        }\n        const modeClass = [];\n        if (this._bookmark.editable) {\n          modeClass.push(this.options.templateOptions.editableClass);\n        }\n        if (this._bookmark.removable) {\n          modeClass.push(this.options.templateOptions.removableClass);\n        }\n        content = substitute(\n          template,\n          L.Util.extend(\n            {},\n            this._bookmark || {},\n            this.options.templateOptions,\n            {\n              submitText: submitText,\n              coords: this.formatCoords(\n                this._source.getLatLng(),\n                this._map.getZoom()\n              ),\n              mode: modeClass.join(\" \"),\n            }\n          )\n        );\n      }\n      this._content = content;\n      L.Popup.prototype._updateContent.call(this);\n      this._onRendered();\n    },\n\n    /**\n     * Form rendered, set up create or edit\n     */\n    _onRendered: function () {\n      if (\n        this.options.mode === modes.CREATE ||\n        this.options.mode === modes.UPDATE\n      ) {\n        const form = this._contentNode.querySelector(\n          \".\" + this.options.templateOptions.formClass\n        );\n        const input = form.querySelector(\n          \".\" + this.options.templateOptions.inputClass\n        );\n\n        L.DomEvent.on(form, \"submit\", this._onSubmit, this);\n        setTimeout(this._setFocus.bind(this), 250);\n      } else if (this.options.mode === modes.OPTIONS) {\n        L.DomEvent.delegate(\n          this._container,\n          \".\" + this.options.templateOptions.editClass,\n          \"click\",\n          this._onEditClick,\n          this\n        );\n        L.DomEvent.delegate(\n          this._container,\n          \".\" + this.options.templateOptions.removeClass,\n          \"click\",\n          this._onRemoveClick,\n          this\n        );\n        L.DomEvent.delegate(\n          this._container,\n          \".\" + this.options.templateOptions.cancelClass,\n          \"click\",\n          this._onCancelClick,\n          this\n        );\n      }\n    },\n\n    /**\n     * Set focus at the end of input\n     */\n    _setFocus: function () {\n      const input = this._contentNode.querySelector(\n        \".\" + this.options.templateOptions.inputClass\n      );\n      // Multiply by 2 to ensure the cursor always ends up at the end;\n      // Opera sometimes sees a carriage return as 2 characters.\n      const strLength = input.value.length * 2;\n      input.focus();\n      input.setSelectionRange(strLength, strLength);\n    },\n\n    /**\n     * Edit button clicked\n     * @param  {Event} evt\n     */\n    _onEditClick: function (evt) {\n      L.DomEvent.preventDefault(evt);\n      this._map.fire(\"bookmark:edit\", { data: this._bookmark });\n      this.close();\n    },\n\n    /**\n     * Remove button clicked\n     * @param  {Event} evt\n     */\n    _onRemoveClick: function (evt) {\n      L.DomEvent.preventDefault(evt);\n      this._map.fire(\"bookmark:remove\", { data: this._bookmark });\n      this.close();\n    },\n\n    /**\n     * Back from options view\n     * @param  {Event} evt\n     */\n    _onCancelClick: function (evt) {\n      L.DomEvent.preventDefault(evt);\n      this._map.fire(\"bookmark:show\", { data: this._bookmark });\n      this.close();\n    },\n\n    /**\n     * Creates bookmark object from form data\n     * @return {Object}\n     */\n    _getBookmarkData: function () {\n      const options = this.options;\n      if (options.getBookmarkData) {\n        return options.getBookmarkData.call(this);\n      }\n      const input = this._contentNode.querySelector(\n        \".\" + options.templateOptions.inputClass\n      );\n      const idInput = this._contentNode.querySelector(\n        \".\" + options.templateOptions.idInputClass\n      );\n      return {\n        latlng: this._source.getLatLng(),\n        zoom: this._map.getZoom(),\n        name: input.value,\n        id: idInput.value || unique(),\n      };\n    },\n\n    /**\n     * Form submit, dispatch eventm close popup\n     * @param {Event} evt\n     */\n    _onSubmit: function (evt) {\n      L.DomEvent.stop(evt);\n\n      const input = this._contentNode.querySelector(\n        \".\" + this.options.templateOptions.inputClass\n      );\n      input.classList.remove(this.options.templateOptions.inputErrorClass);\n\n      if (input.value === \"\" && this.options.generateNames) {\n        input.value = unique(this.options.generateNamesPrefix);\n      }\n\n      const validate = this.options.validateInput || (() => true);\n\n      if (input.value !== \"\" && validate.call(this, input.value)) {\n        const bookmark = L.Util.extend(\n          {},\n          this._bookmark,\n          this._getBookmarkData()\n        );\n        const map = this._map;\n\n        this.close();\n        if (this.options.mode === modes.CREATE) {\n          map.fire(\"bookmark:add\", { data: bookmark });\n        } else {\n          map.fire(\"bookmark:edited\", { data: bookmark });\n        }\n      } else {\n        input.classList.add(this.options.templateOptions.inputErrorClass);\n      }\n    },\n\n    /**\n     * @param  {L.LatLng} coords\n     * @param  {Number=}  zoom\n     * @return {String}\n     */\n    formatCoords: function (coords, zoom) {\n      if (this.options.formatCoords) {\n        return this.options.formatCoords.call(this, coords, zoom);\n      }\n      return [coords.lat.toFixed(4), coords.lng.toFixed(4), zoom].join(\n        \",&nbsp;\"\n      );\n    },\n\n    /**\n     * Hook to source movements\n     * @param  {L.Map} map\n     * @return {Element}\n     */\n    onAdd: function (map) {\n      this._source.on(\"dragend\", this._onSourceMoved, this);\n      this._source.on(\"dragstart\", this._onSourceMoveStart, this);\n      return L.Popup.prototype.onAdd.call(this, map);\n    },\n\n    /**\n     * @param  {L.Map} map\n     */\n    onRemove: function (map) {\n      this._source.off(\"dragend\", this._onSourceMoved, this);\n      L.Popup.prototype.onRemove.call(this, map);\n    },\n\n    /**\n     * Marker drag\n     */\n    _onSourceMoveStart: function () {\n      // store\n      this._bookmark = L.Util.extend(\n        this._bookmark || {},\n        this._getBookmarkData()\n      );\n      this._container.style.display = \"none\";\n    },\n\n    /**\n     * Marker moved\n     * @param  {Event} e\n     */\n    _onSourceMoved: function (e) {\n      this._latlng = this._source.getLatLng();\n      this._container.style.display = \"\";\n      this._source.openPopup();\n      this.update();\n    },\n  }\n);\n", "import L from \"leaflet\";\n\n/**\n * Courtesy of https://github.com/component/matches-selector\n */\nconst matchesSelector = ((ElementPrototype) => {\n  const matches =\n    ElementPrototype.matches ||\n    ElementPrototype.webkitMatchesSelector ||\n    ElementPrototype.mozMatchesSelector ||\n    ElementPrototype.msMatchesSelector ||\n    ElementPrototype.oMatchesSelector ||\n    // hello IE\n    function (selector) {\n      var node = this,\n        parent = node.parentNode || node.document,\n        nodes = parent.querySelectorAll(selector);\n\n      for (var i = 0, len = nodes.length; i < len; ++i) {\n        if (nodes[i] == node) return true;\n      }\n      return false;\n    };\n\n  /**\n   * @param  {Element} element\n   * @param  {String} selector\n   * @return {Boolean}\n   */\n  return function (element, selector) {\n    return matches.call(element, selector);\n  };\n})(Element.prototype);\n\n/**\n * Courtesy of https://github.com/component/closest\n *\n * @param  {Element} element\n * @param  {String}  selector\n * @param  {Boolean} checkSelf\n * @param  {Element} root\n *\n * @return {Element|Null}\n */\nfunction closest(element, selector, checkSelf, root) {\n  element = checkSelf\n    ? {\n        parentNode: element,\n      }\n    : element;\n\n  root = root || document;\n\n  // Make sure `element !== document` and `element != null`\n  // otherwise we get an illegal invocation\n  while ((element = element.parentNode) && element !== document) {\n    if (matchesSelector(element, selector)) return element;\n    // After `matches` on the edge case that\n    // the selector matches the root\n    // (when the root is not the document)\n    if (element === root) return null;\n  }\n}\n\n/**\n * Based on https://github.com/component/delegate\n *\n * @param  {Element}  el\n * @param  {String}   selector\n * @param  {String}   type\n * @param  {Function} fn\n *\n * @return {Function}\n */\nL.DomEvent.delegate = function (el, selector, type, fn, bind) {\n  return L.DomEvent.on(el, type, (evt) => {\n    const target = evt.target || evt.srcElement;\n    evt.delegateTarget = closest(target, selector, true, el);\n    if (evt.delegateTarget && !evt.propagationStopped) {\n      fn.call(bind || el, evt);\n    }\n  });\n};\n", "import L from \"leaflet\";\nimport Storage, { EngineType } from \"./storage\";\nimport FormPopup from \"./formpopup\";\nimport { substitute } from \"./string\";\nimport \"./leaflet.delegate\";\n\n// expose\nL.Util._template = L.Util._template || substitute;\n\n/**\n * Bookmarks control\n * @class  L.Control.Bookmarks\n * @extends {L.Control}\n */\nexport default L.Control.extend(\n  /**  @lends Bookmarks.prototype */ {\n    statics: {\n      Storage,\n      FormPopup,\n    },\n\n    /**\n     * @type {Object}\n     */\n    options: {\n      localStorage: true,\n\n      /* you can provide access to your own storage,\n       * xhr for example, but make sure it has all\n       * required endpoints:\n       *\n       * .getItem(id, callback)\n       * .setItem(id, callback)\n       * .getAllItems(callback)\n       * .removeItem(id, callback)\n       */\n      storage: null,\n      name: \"leaflet-bookmarks\",\n      position: \"topright\", // chose your own if you want\n\n      containerClass: \"leaflet-bar leaflet-bookmarks-control\",\n      expandedClass: \"expanded\",\n      headerClass: \"bookmarks-header\",\n      listClass: \"bookmarks-list\",\n      iconClass: \"bookmarks-icon\",\n      iconWrapperClass: \"bookmarks-icon-wrapper\",\n      listWrapperClass: \"bookmarks-list-wrapper\",\n      listWrapperClassAdd: \"list-with-button\",\n      wrapperClass: \"bookmarks-container\",\n      addBookmarkButtonCss: \"add-bookmark-button\",\n\n      animateClass: \"bookmark-added-anim\",\n      animateDuration: 150,\n\n      formPopup: {\n        popupClass: \"bookmarks-popup\",\n      },\n\n      bookmarkTemplate:\n        '<li class=\"{{ itemClass }}\" data-id=\"{{ data.id }}\">' +\n        '<span class=\"{{ removeClass }}\">&times;</span>' +\n        '<span class=\"{{ nameClass }}\">{{ data.name }}</span>' +\n        '<span class=\"{{ coordsClass }}\">{{ data.coords }}</span>' +\n        \"</li>\",\n\n      emptyTemplate:\n        '<li class=\"{{ itemClass }} {{ emptyClass }}\">' +\n        \"{{ data.emptyMessage }}</li>\",\n\n      dividerTemplate: '<li class=\"divider\"></li>',\n\n      bookmarkTemplateOptions: {\n        itemClass: \"bookmark-item\",\n        nameClass: \"bookmark-name\",\n        coordsClass: \"bookmark-coords\",\n        removeClass: \"bookmark-remove\",\n        emptyClass: \"bookmarks-empty\",\n      },\n\n      defaultBookmarkOptions: {\n        editable: true,\n        removable: true,\n      },\n\n      title: \"Bookmarks\",\n      emptyMessage: \"No bookmarks yet\",\n      addBookmarkMessage: \"Add new bookmark\",\n      collapseOnClick: true,\n      scrollOnAdd: true,\n      scrollDuration: 1000,\n      popupOnShow: true,\n      addNewOption: true,\n\n      /**\n       * This you can change easily to output\n       * whatever you have stored in bookmark\n       *\n       * @type {String}\n       */\n      popupTemplate:\n        \"<div><h3>{{ name }}</h3><p>{{ latlng }}, {{ zoom }}</p></div>\",\n\n      /**\n       * Prepare your bookmark data for template.\n       * If you don't change it, the context of this\n       * function will be bookmarks control, so you can\n       * access the map or other things from here\n       *\n       * @param  {Object} bookmark\n       * @return {Object}\n       */\n      getPopupContent: function (bookmark) {\n        return substitute(this.options.popupTemplate, {\n          latlng: this.formatCoords(bookmark.latlng),\n          name: bookmark.name,\n          zoom: this._map.getZoom(),\n        });\n      },\n    },\n\n    /**\n     * @param  {Object} options\n     * @constructor\n     */\n    initialize: function (options) {\n      options = options || {};\n\n      /**\n       * Bookmarks array\n       * @type {Array}\n       */\n      this._data = [];\n\n      /**\n       * @type {Element}\n       */\n      this._list = null;\n\n      /**\n       * @type {L.Marker}\n       */\n      this._marker = null;\n\n      /**\n       * @type {HTMLElement}\n       */\n      this._addButton = null;\n\n      /**\n       * @type {Element}\n       */\n      this._icon = null;\n\n      /**\n       * @type {Boolean}\n       */\n      this._isCollapsed = true;\n\n      L.Util.setOptions(this, options);\n\n      /**\n       * @type {Storage}\n       */\n      this._storage =\n        options.storage ||\n        (this.options.localStorage\n          ? new Storage(this.options.name, EngineType.LOCALSTORAGE)\n          : new Storage(this.options.name, EngineType.GLOBALSTORAGE));\n\n      L.Control.prototype.initialize.call(this, this.options);\n    },\n\n    /**\n     * @param {L.Map} map\n     */\n    onAdd: function (map) {\n      const container = (this._container = L.DomUtil.create(\n        \"div\",\n        this.options.containerClass\n      ));\n\n      L.DomEvent.disableClickPropagation(container).disableScrollPropagation(\n        container\n      );\n      container.innerHTML =\n        '<div class=\"' +\n        this.options.headerClass +\n        '\"><span class=\"' +\n        this.options.iconWrapperClass +\n        '\">' +\n        '<span class=\"' +\n        this.options.iconClass +\n        '\"></span></span>';\n\n      this._icon = container.querySelector(\".\" + this.options.iconClass);\n      this._icon.title = this.options.title;\n\n      this._createList(this.options.bookmarks);\n\n      const wrapper = L.DomUtil.create(\n        \"div\",\n        this.options.wrapperClass,\n        this._container\n      );\n      wrapper.appendChild(this._listwrapper);\n\n      this._initLayout();\n\n      L.DomEvent.on(container, \"click\", this._onClick, this).on(\n        container,\n        \"contextmenu\",\n        L.DomEvent.stopPropagation\n      );\n\n      map\n        .on(\"bookmark:new\", this._onBookmarkAddStart, this)\n        .on(\"bookmark:add\", this._onBookmarkAdd, this)\n        .on(\"bookmark:edited\", this._onBookmarkEdited, this)\n        .on(\"bookmark:show\", this._onBookmarkShow, this)\n        .on(\"bookmark:edit\", this._onBookmarkEdit, this)\n        .on(\"bookmark:options\", this._onBookmarkOptions, this)\n        .on(\"bookmark:remove\", this._onBookmarkRemove, this)\n        .on(\"resize\", this._initLayout, this);\n\n      return container;\n    },\n\n    /**\n     * @param  {L.Map} map\n     */\n    onRemove: function (map) {\n      map\n        .off(\"bookmark:new\", this._onBookmarkAddStart, this)\n        .off(\"bookmark:add\", this._onBookmarkAdd, this)\n        .off(\"bookmark:edited\", this._onBookmarkEdited, this)\n        .off(\"bookmark:show\", this._onBookmarkShow, this)\n        .off(\"bookmark:edit\", this._onBookmarkEdit, this)\n        .off(\"bookmark:options\", this._onBookmarkOptions, this)\n        .off(\"bookmark:remove\", this._onBookmarkRemove, this)\n        .off(\"resize\", this._initLayout, this);\n\n      if (this._marker) this._marker._popup_.close();\n\n      if (this.options.addNewOption) {\n        L.DomEvent.off(\n          this._container.querySelector(\n            \".\" + this.options.addBookmarkButtonCss\n          ),\n          \"click\",\n          this._onAddButtonPressed,\n          this\n        );\n      }\n\n      this._marker = null;\n      this._popup = null;\n      this._container = null;\n    },\n\n    /**\n     * @return {Array.<Object>}\n     */\n    getData: function () {\n      return this._filterBookmarksOutput(this._data);\n    },\n\n    /**\n     * @param  {Array.<Number>|Function|null} bookmarks\n     */\n    _createList: function (bookmarks) {\n      this._listwrapper = L.DomUtil.create(\n        \"div\",\n        this.options.listWrapperClass,\n        this._container\n      );\n      this._list = L.DomUtil.create(\n        \"ul\",\n        this.options.listClass,\n        this._listwrapper\n      );\n\n      // select bookmark\n      L.DomEvent.delegate(\n        this._list,\n        \".\" + this.options.bookmarkTemplateOptions.itemClass,\n        \"click\",\n        this._onBookmarkClick,\n        this\n      );\n\n      this._setEmptyListContent();\n\n      if (L.Util.isArray(bookmarks)) {\n        this._appendItems(bookmarks);\n      } else if (typeof bookmarks === \"function\") {\n        this._appendItems(bookmarks());\n      } else {\n        this._storage.getAllItems((bookmarks) => this._appendItems(bookmarks));\n      }\n    },\n\n    /**\n     * Empty list\n     */\n    _setEmptyListContent: function () {\n      this._list.innerHTML = substitute(\n        this.options.emptyTemplate,\n        L.Util.extend(this.options.bookmarkTemplateOptions, {\n          data: {\n            emptyMessage: this.options.emptyMessage,\n          },\n        })\n      );\n    },\n\n    /**\n     * Sees that the list size is not too big\n     */\n    _initLayout: function () {\n      const size = this._map.getSize();\n      this._listwrapper.style.maxHeight =\n        Math.min(size.y * 0.6, size.y - 100) + \"px\";\n\n      if (this.options.position === \"topleft\") {\n        L.DomUtil.addClass(this._container, \"leaflet-bookmarks-to-right\");\n      }\n      if (this.options.addNewOption) {\n        const addButton = L.DomUtil.create(\n          \"div\",\n          this.options.addBookmarkButtonCss\n        );\n        if (this._addButton === null) {\n          this._listwrapper.parentNode.appendChild(addButton);\n          this._addButton = addButton;\n          this._listwrapper.parentNode.classList.add(\n            this.options.listWrapperClassAdd\n          );\n          addButton.innerHTML =\n            '<span class=\"plus\">+</span>' +\n            '<span class=\"content\">' +\n            this.options.addBookmarkMessage +\n            \"</span>\";\n          L.DomEvent.on(addButton, \"click\", this._onAddButtonPressed, this);\n        }\n      }\n    },\n\n    /**\n     * @param  {MouseEvent} evt\n     */\n    _onAddButtonPressed: function (evt) {\n      L.DomEvent.stop(evt);\n      this.collapse();\n      this._map.fire(\"bookmark:new\", {\n        latlng: this._map.getCenter(),\n      });\n    },\n\n    /**\n     * I don't care if they're unique or not,\n     * if you do - handle this\n     *\n     * @param {Array.<Object>} bookmarks\n     * @return {Array.<Object>}\n     */\n    _filterBookmarks: function (bookmarks) {\n      if (this.options.filterBookmarks) {\n        return this.options.filterBookmarks.call(this, bookmarks);\n      }\n      return bookmarks;\n    },\n\n    /**\n     * Filter bookmarks for output. This one allows you to save dividers as well\n     *\n     * @param {Array.<Object>} bookmarks\n     * @return {Array.<Object>}\n     */\n    _filterBookmarksOutput: function (bookmarks) {\n      if (this.options.filterBookmarksOutput) {\n        return this.options.filterBookmarksOutput.call(this, bookmarks);\n      }\n      return bookmarks;\n    },\n\n    /**\n     * Append list items(render)\n     * @param  {Array.<Object>} bookmarks\n     */\n    _appendItems: function (bookmarks) {\n      let html = \"\";\n      let wasEmpty = this._data.length === 0;\n      let bookmark;\n\n      // maybe you have something in mind?\n      bookmarks = this._filterBookmarks(bookmarks);\n\n      // store\n      this._data = this._data.concat(bookmarks);\n\n      for (let i = 0, len = bookmarks.length; i < len; i++) {\n        html += this._renderBookmarkItem(bookmarks[i]);\n      }\n\n      if (html !== \"\") {\n        // replace `empty` message if needed\n        if (wasEmpty) {\n          this._list.innerHTML = html;\n        } else {\n          this._list.innerHTML += html;\n        }\n      }\n\n      if (this._isCollapsed) {\n        const container = this._container;\n        const className = this.options.animateClass;\n        container.classList.add(className);\n        window.setTimeout(function () {\n          container.classList.remove(className);\n        }, this.options.animateDuration);\n      } else {\n        this._scrollToLast();\n      }\n    },\n\n    /**\n     * Scrolls to last element of the list\n     */\n    _scrollToLast: function () {\n      const listwrapper = this._listwrapper;\n      let pos = this._listwrapper.scrollTop;\n      const targetVal = this._list.lastChild.offsetTop;\n      let start = 0;\n\n      const step =\n        (targetVal - pos) / (this.options.scrollDuration / (1000 / 16));\n\n      function scroll(timestamp) {\n        if (!start) start = timestamp;\n        //var progress = timestamp - start;\n\n        pos = Math.min(pos + step, targetVal);\n        listwrapper.scrollTop = pos;\n        if (pos !== targetVal) {\n          L.Util.requestAnimFrame(scroll);\n        }\n      }\n      L.Util.requestAnimFrame(scroll);\n    },\n\n    /**\n     * Render single bookmark item\n     * @param  {Object} bookmark\n     * @return {String}\n     */\n    _renderBookmarkItem: function (bookmark) {\n      if (bookmark.divider) {\n        return substitute(this.options.dividerTemplate, bookmark);\n      }\n\n      this.options.bookmarkTemplateOptions.data =\n        this._getBookmarkDataForTemplate(bookmark);\n\n      return substitute(\n        this.options.bookmarkTemplate,\n        this.options.bookmarkTemplateOptions\n      );\n    },\n\n    /**\n     * Extracts data and style expressions for item template\n     * @param  {Object} bookmark\n     * @return {Object}\n     */\n    _getBookmarkDataForTemplate: function (bookmark) {\n      if (this.options.getBookmarkDataForTemplate) {\n        return this.options.getBookmarkDataForTemplate.call(this, bookmark);\n      }\n      return {\n        coords: this.formatCoords(bookmark.latlng),\n        name: this.formatName(bookmark.name),\n        zoom: bookmark.zoom,\n        id: bookmark.id,\n      };\n    },\n\n    /**\n     * @param  {L.LatLng} latlng\n     * @return {String}\n     */\n    formatCoords: function (latlng) {\n      if (this.options.formatCoords) {\n        return this.options.formatCoords.call(this, latlng);\n      }\n      return latlng[0].toFixed(4) + \",&nbsp;\" + latlng[1].toFixed(4);\n    },\n\n    /**\n     * @param  {String} name\n     * @return {String}\n     */\n    formatName: function (name) {\n      if (this.options.formatName) {\n        return this.options.formatName.call(this, name);\n      }\n      return name;\n    },\n\n    /**\n     * Shows bookmarks list\n     */\n    expand: function () {\n      L.DomUtil.addClass(this._container, this.options.expandedClass);\n      this._isCollapsed = false;\n    },\n\n    /**\n     * Hides bookmarks list and the form\n     */\n    collapse: function () {\n      L.DomUtil.removeClass(this._container, this.options.expandedClass);\n      this._isCollapsed = true;\n    },\n\n    /**\n     * @param  {Event} evt\n     */\n    _onClick: function (evt) {\n      const expanded = L.DomUtil.hasClass(\n        this._container,\n        this.options.expandedClass\n      );\n      let target = evt.target || evt.srcElement;\n\n      if (expanded) {\n        if (target === this._container) {\n          return this.collapse();\n        }\n        // check if it's inside the header\n        while (target !== this._container) {\n          if (\n            L.DomUtil.hasClass(target, this.options.headerClass) ||\n            L.DomUtil.hasClass(target, this.options.listWrapperClass)\n          ) {\n            this.collapse();\n            break;\n          }\n          target = target.parentNode;\n        }\n      } else this.expand();\n    },\n\n    /**\n     * @param  {Object} evt\n     */\n    _onBookmarkAddStart: function (evt) {\n      if (this._marker) this._popup.close();\n\n      this._marker = new L.Marker(evt.latlng, {\n        icon: this.options.icon || new L.Icon.Default(),\n        draggable: true,\n        riseOnHover: true,\n      }).addTo(this._map);\n      this._marker.on(\"popupclose\", this._onPopupClosed, this);\n\n      // open form\n      this._popup = new L.Control.Bookmarks.FormPopup(\n        L.Util.extend(this.options.formPopup, {\n          mode: L.Control.Bookmarks.FormPopup.modes.CREATE,\n        }),\n        this._marker,\n        this,\n        L.Util.extend({}, evt.data, this.options.defaultBookmarkOptions)\n      ).addTo(this._map);\n    },\n\n    /**\n     * Bookmark added\n     * @param  {Object} bookmark\n     */\n    _onBookmarkAdd: function (bookmark) {\n      const map = this._map;\n      bookmark = this._cleanBookmark(bookmark.data);\n      this._storage.setItem(bookmark.id, bookmark, (item) => {\n        map.fire(\"bookmark:saved\", {\n          data: item,\n        });\n        this._appendItems([item]);\n      });\n      this._showBookmark(bookmark);\n    },\n\n    /**\n     * Update done\n     * @param  {Event} evt\n     */\n    _onBookmarkEdited: function (evt) {\n      const map = this._map;\n      const bookmark = this._cleanBookmark(evt.data);\n      this._storage.setItem(bookmark.id, bookmark, (item) => {\n        map.fire(\"bookmark:saved\", { data: item });\n        const data = this._data;\n        this._data = [];\n        for (var i = 0, len = data.length; i < len; i++) {\n          if (data[i].id === bookmark.id) {\n            data.splice(i, 1, bookmark);\n          }\n        }\n        this._appendItems(data);\n      });\n      this._showBookmark(bookmark);\n    },\n\n    /**\n     * Cleans circular reference for JSON\n     * @param  {Object} bookmark\n     * @return {Object}\n     */\n    _cleanBookmark: function (bookmark) {\n      if (!L.Util.isArray(bookmark.latlng)) {\n        bookmark.latlng = [bookmark.latlng.lat, bookmark.latlng.lng];\n      }\n      return bookmark;\n    },\n\n    /**\n     * Form closed\n     * @param  {Object} evt\n     */\n    _onPopupClosed: function (evt) {\n      this._map.removeLayer(this._marker);\n      this._marker = null;\n      this._popup = null;\n    },\n\n    /**\n     * @param  {String} id\n     * @return {Object|Null}\n     */\n    _getBookmark: function (id) {\n      for (let i = 0, len = this._data.length; i < len; i++) {\n        if (this._data[i].id === id) return this._data[i];\n      }\n      return null;\n    },\n\n    /**\n     * @param  {Object} evt\n     */\n    _onBookmarkShow: function (evt) {\n      this._gotoBookmark(evt.data);\n    },\n\n    /**\n     * Event handler for edit\n     * @param  {Object} evt\n     */\n    _onBookmarkEdit: function (evt) {\n      this._editBookmark(evt.data);\n    },\n\n    /**\n     * Remove bookmark triggered\n     * @param  {Event} evt\n     */\n    _onBookmarkRemove: function (evt) {\n      this._removeBookmark(evt.data);\n    },\n\n    /**\n     * Bookmark options called\n     * @param  {Event} evt\n     */\n    _onBookmarkOptions: function (evt) {\n      this._bookmarkOptions(evt.data);\n    },\n\n    /**\n     * Show menu popup\n     * @param  {Object} bookmark\n     */\n    _bookmarkOptions: function (bookmark) {\n      const coords = L.latLng(bookmark.latlng);\n      const marker = (this._marker = this._createMarker(coords, bookmark));\n      // open form\n      this._popup = new L.Control.Bookmarks.FormPopup(\n        L.Util.extend(this.options.formPopup, {\n          mode: L.Control.Bookmarks.FormPopup.modes.OPTIONS,\n        }),\n        marker,\n        this,\n        bookmark\n      ).addTo(this._map);\n    },\n\n    /**\n     * Call edit popup\n     * @param  {Object} bookmark\n     */\n    _editBookmark: function (bookmark) {\n      const coords = L.latLng(bookmark.latlng);\n      const marker = (this._marker = this._createMarker(coords, bookmark));\n      marker.dragging.enable();\n      // open form\n      this._popup = new L.Control.Bookmarks.FormPopup(\n        L.Util.extend(this.options.formPopup, {\n          mode: L.Control.Bookmarks.FormPopup.modes.UPDATE,\n        }),\n        marker,\n        this,\n        bookmark\n      ).addTo(this._map);\n    },\n\n    /**\n     * Returns a handler that will remove the bookmark from map\n     * in case it got removed from the list\n     * @param  {Object}   bookmark\n     * @param  {L.Marker} marker\n     * @return {Function}\n     */\n    _getOnRemoveHandler: function (bookmark, marker) {\n      return function (evt) {\n        if (evt.data.id === bookmark.id) {\n          marker.clearAllEventListeners();\n          if (marker._popup_) marker._popup_.close();\n          this.removeLayer(marker);\n        }\n      };\n    },\n\n    /**\n     * Creates bookmark marker\n     * @param  {L.LatLng} coords\n     * @param  {Object}   bookmark\n     * @return {L.Marker}\n     */\n    _createMarker: function (coords, bookmark) {\n      const marker = new L.Marker(coords, {\n        icon: this.options.icon || new L.Icon.Default(),\n        riseOnHover: true,\n      }).addTo(this._map);\n      const removeIfRemoved = this._getOnRemoveHandler(bookmark, marker);\n      this._map.on(\"bookmark:removed\", removeIfRemoved, this._map);\n      marker\n        .on(\"popupclose\", () => this._map.removeLayer(this))\n        .on(\"remove\", () => this._map.off(\"bookmark:removed\", removeIfRemoved));\n      return marker;\n    },\n\n    /**\n     * Shows bookmark, nothing else\n     * @param  {Object} bookmark\n     */\n    _showBookmark: function (bookmark) {\n      if (this._marker) this._marker._popup_.close();\n      const coords = L.latLng(bookmark.latlng);\n      const marker = this._createMarker(coords, bookmark);\n      const popup = new L.Control.Bookmarks.FormPopup(\n        L.Util.extend(this.options.formPopup, {\n          mode: L.Control.Bookmarks.FormPopup.modes.SHOW,\n        }),\n        marker,\n        this,\n        bookmark\n      );\n      if (this.options.popupOnShow) popup.addTo(this._map);\n      this._popup = popup;\n      this._marker = marker;\n    },\n\n    /**\n     * @param  {Object} bookmark\n     */\n    _gotoBookmark: function (bookmark) {\n      this._map.setView(bookmark.latlng, bookmark.zoom);\n      this._showBookmark(bookmark);\n    },\n\n    /**\n     * @param  {Object} bookmark\n     */\n    _removeBookmark: function (bookmark) {\n      const remove = (proceed) => {\n        if (!proceed) return this._showBookmark(bookmark);\n\n        this._data.splice(this._data.indexOf(bookmark), 1);\n        this._storage.removeItem(bookmark.id, (bookmark) => {\n          this._onBookmarkRemoved(bookmark);\n        });\n      };\n\n      if (typeof this.options.onRemove === \"function\") {\n        this.options.onRemove(bookmark, remove);\n      } else {\n        remove(true);\n      }\n    },\n\n    /**\n     * @param  {Object} bookmark\n     */\n    _onBookmarkRemoved: function (bookmark) {\n      const li = this._list.querySelector(\n        \".\" +\n          this.options.bookmarkTemplateOptions.itemClass +\n          \"[data-id='\" +\n          bookmark.id +\n          \"']\"\n      );\n\n      this._map.fire(\"bookmark:removed\", { data: bookmark });\n\n      if (li) {\n        L.DomUtil.setOpacity(li, 0);\n        setTimeout(() => {\n          if (li.parentNode) li.parentNode.removeChild(li);\n          if (this._data.length === 0) this._setEmptyListContent();\n        }, 250);\n      }\n    },\n\n    /**\n     * Gets popup content\n     * @param  {Object} bookmark\n     * @return {String}\n     */\n    _getPopupContent: function (bookmark) {\n      if (this.options.getPopupContent) {\n        return this.options.getPopupContent.call(this, bookmark);\n      }\n      return JSON.stringify(bookmark);\n    },\n\n    /**\n     * @param  {Event} e\n     */\n    _onBookmarkClick: function (evt) {\n      const bookmark = this._getBookmarkFromListItem(evt.delegateTarget);\n      if (!bookmark) return;\n      L.DomEvent.stopPropagation(evt);\n\n      // remove button hit\n      if (\n        L.DomUtil.hasClass(\n          evt.target || evt.srcElement,\n          this.options.bookmarkTemplateOptions.removeClass\n        )\n      ) {\n        this._removeBookmark(bookmark);\n      } else {\n        this._map.fire(\"bookmark:show\", { data: bookmark });\n        if (this.options.collapseOnClick) this.collapse();\n      }\n    },\n\n    /**\n     * In case you've decided to play with ids - we've got you covered\n     * @param  {Element} li\n     * @return {Object|Null}\n     */\n    _getBookmarkFromListItem: function (li) {\n      if (this.options.getBookmarkFromListItem) {\n        return this.options.getBookmarkFromListItem.call(this, li);\n      }\n      return this._getBookmark(li.getAttribute(\"data-id\"));\n    },\n\n    /**\n     * GeoJSON feature out of a bookmark\n     * @param  {Object} bookmark\n     * @return {Object}\n     */\n    bookmarkToFeature: function (bookmark) {\n      const coords = this._getBookmarkCoords(bookmark);\n      bookmark = JSON.parse(JSON.stringify(bookmark)); // quick copy\n      delete bookmark.latlng;\n\n      return L.GeoJSON.getFeature(\n        {\n          feature: {\n            type: \"Feature\",\n            id: bookmark.id,\n            properties: bookmark,\n          },\n        },\n        {\n          type: \"Point\",\n          coordinates: coords,\n        }\n      );\n    },\n\n    /**\n     * @param  {Object} bookmark\n     * @return {Array.<Number>}\n     */\n    _getBookmarkCoords: function (bookmark) {\n      if (bookmark.latlng instanceof L.LatLng) {\n        return [bookmark.latlng.lat, bookmark.latlng.lng];\n      }\n      return bookmark.latlng.reverse();\n    },\n\n    /**\n     * Read bookmarks from GeoJSON FeatureCollectio\n     * @param  {Object} geojson\n     * @return {Object}\n     */\n    fromGeoJSON: function (geojson) {\n      const bookmarks = [];\n      for (let i = 0, len = geojson.features.length; i < len; i++) {\n        const bookmark = geojson.features[i];\n        if (!bookmark.properties.divider) {\n          bookmark.properties.latlng = bookmark.geometry.coordinates\n            .concat()\n            .reverse();\n        }\n        bookmarks.push(bookmark.properties);\n      }\n      return bookmarks;\n    },\n\n    /**\n     * @return {Object}\n     */\n    toGeoJSON: function () {\n      return {\n        type: \"FeatureCollection\",\n        features: ((data) => {\n          const result = [];\n          for (let i = 0, len = data.length; i < len; i++) {\n            if (!data[i].divider) {\n              result.push(this.bookmarkToFeature(data[i]));\n            }\n          }\n          return result;\n        })(this._data),\n      };\n    },\n  }\n);\n", "import L from \"leaflet\";\nimport Bookmarks from \"./src/bookmarks\";\n\nL.Control.Bookmarks = Bookmarks;\n\nexport default Bookmarks;\n"], "names": ["let", "const", "this"], "mappings": ";;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,UAAU,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE;AAChD,EAAE,OAAO,GAAG,CAAC,OAAO,CAAC,MAAM,IAAI,iBAAiB,EAAE,UAAU,KAAK,EAAE,IAAI,EAAE;AACzE,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;AACtB;AACA,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;AAClC,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,IAAI,EAAA,EAAE,OAAO,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAA;AACzD,MAAM,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;AACtD,KAAK,MAAM;AACX;AACA,MAAMA,IAAI,MAAM,GAAG,MAAM,CAAC;AAC1B,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAC7B,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;AACvD,QAAQ,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,MAAM,EAAA,EAAE,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAA;AACxD,aAAa,EAAA,OAAO,EAAE,CAAC,EAAA;AACvB,OAAO;AACP,MAAM,OAAO,MAAM,CAAC;AACpB,KAAK;AACL,GAAG,CAAC,CAAC;AACL,CAAC;AACD;AACAC,IAAM,KAAK,GAAG,4BAA4B,CAAC;AAC3C;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,MAAM,CAAC,MAAM,EAAE;AAC/B,EAAE;AACF,IAAI,CAAC,MAAM,IAAI,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;AAC9D,IAAI,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC;AACrC,IAAI;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,IAAI,CAAC,GAAG,EAAE;AAC1B,EAAE,OAAO,GAAG,CAAC,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC;AACvC;;ACjDA;AACA;AACA;AACAA,IAAM,IAAI,GAAG,EAAE,CAAC;AAChB;AACA;AACA;AACA;AACA;AACA;AACe,IAAM,aAAa,GAChC,SAAW,aAAA,CAAC,MAAM,EAAE;AACtB;AACA;AACA;AACA,EAAI,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;AACxB,CAAC,CAAA;AACH;AACE;AACF;AACA;AACA;AACE,aAAA,CAAA,SAAA,CAAA,OAAA,GAAA,SAAA,OAAA,EAAQ,GAAG,EAAE,QAAQ,EAAE;AACzB,EAAI,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,GAAG,CAAC,CAAC,CAAC;AACrC,CAAC,CAAA;AACH;AACE;AACF;AACA;AACA;AACA;AACE,aAAA,CAAA,SAAA,CAAA,OAAA,GAAA,SAAA,OAAA,EAAQ,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE;AAC/B,EAAI,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC;AACpC,EAAI,QAAQ,CAAC,IAAI,CAAC,CAAC;AACjB,CAAC,CAAA;AACH;AACE;AACF;AACA;wBACE,WAAW,GAAA,SAAA,WAAA,EAAC,QAAQ,EAAE;AACxB,EAAIA,IAAM,KAAK,GAAG,EAAE,CAAC;AACrB,EAAI,KAAKA,IAAM,GAAG,IAAI,IAAI,EAAE;AAC5B,IAAM,IAAI,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE;AACtE,MAAQ,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;AAC9B,KAAO;AACP,GAAK;AACL,EAAI,QAAQ,CAAC,KAAK,CAAC,CAAC;AAClB,CAAC,CAAA;AACH;AACE;AACF;AACA;AACA;AACE,aAAA,CAAA,SAAA,CAAA,UAAA,GAAA,SAAA,UAAA,EAAW,GAAG,EAAE,QAAQ,EAAE;;AAAC;AAC7B,EAAI,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,UAAC,IAAI,EAAK;AAChC,IAAM,IAAI,IAAI,EAAE;AAChB,MAAQ,OAAO,IAAI,CAACC,QAAI,CAAC,OAAO,GAAG,GAAG,CAAC,CAAC;AACxC,KAAO,MAAM;AACb,MAAQ,IAAI,GAAG,IAAI,CAAC;AACpB,KAAO;AACP,IAAM,IAAI,QAAQ,EAAA,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAA;AACnC,GAAK,CAAC,CAAC;AACL,CAAA;;AC9DF;AACA;AACA;AACA;AACAD,IAAM,OAAO,GAAG,oBAAoB,CAAC;AACrC;AACA;AACA;AACA;AACe,IAAM,YAAY,GAC/B,SAAW,YAAA,CAAC,MAAM,EAAE;AACtB;AACA;AACA;AACA,EAAI,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;AAC1B;AACA;AACA;AACA;AACA,EAAI,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,YAAY,CAAC;AACtC,CAAC,CAAA;AACH;AACE;AACF;AACA;AACA;AACE,YAAA,CAAA,SAAA,CAAA,OAAA,GAAA,SAAA,OAAA,EAAQ,GAAG,EAAE,QAAQ,EAAE;AACzB,EAAID,IAAI,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,GAAG,GAAG,CAAC,CAAC;AACzD,EAAI,IAAI,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;AACpC,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AAC9B,GAAK;AACL,EAAI,QAAQ,CAAC,IAAI,CAAC,CAAC;AACjB,CAAC,CAAA;AACH;AACE;AACF;AACA;uBACE,WAAW,GAAA,SAAA,WAAA,EAAC,QAAQ,EAAE;AACxB,EAAIC,IAAM,KAAK,GAAG,EAAE,CAAC;AACrB,EAAIA,IAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;AAC7C,EAAI,KAAKA,IAAM,GAAG,IAAI,IAAI,CAAC,QAAQ,EAAE;AACrC,IAAM;AACN,MAAQ,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,IAAI;AAC3C,MAAQ,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;AACvC,MAAQ;AACR,MAAQ,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,YAAY,CAAC,YAAG,IAAI,WAAK,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA,EAAA,CAAC,CAAC;AAC9E,KAAO;AACP,GAAK;AACL,EAAI,QAAQ,CAAC,KAAK,CAAC,CAAC;AAClB,CAAC,CAAA;AACH;AACE;AACF;AACA;AACA;AACE,YAAA,CAAA,SAAA,CAAA,UAAA,GAAA,SAAA,UAAA,EAAW,GAAG,EAAE,QAAQ,EAAE;;AAAC;AAC7B,EAAIA,IAAM,IAAI,GAAG,IAAI,CAAC;AACtB,EAAI,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,UAAC,IAAI,EAAK;AAChC,IAAMC,QAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,GAAG,GAAG,CAAC,CAAC;AACnD,IAAM,IAAI,QAAQ,EAAA,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAA;AACnC,GAAK,CAAC,CAAC;AACL,CAAC,CAAA;AACH;AACE;AACF;AACA;AACA;AACA;AACE,YAAA,CAAA,SAAA,CAAA,OAAA,GAAA,SAAA,OAAA,EAAQ,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE;AAC/B,EAAIF,IAAI,OAAO,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;AAClC,EAAI,IAAI,OAAO,KAAK,iBAAiB,EAAE;AACvC,IAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;AACrC,GAAK;AACL,EAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,GAAG,GAAG,EAAE,OAAO,CAAC,CAAC;AACvD,EAAI,QAAQ,CAAC,IAAI,CAAC,CAAC;AACjB,CAAA;;ACrEF;AACA;AACA;AACA;AACOC,IAAM,UAAU,GAAG;AAC1B;AACA,EAAE,aAAa,EAAE,CAAC;AAClB,EAAE,YAAY,EAAE,CAAC;AACjB,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACe,IAAM,OAAO,GAC1B,SAAA,OAAW,CAAC,IAAI,EAAE,UAAU,EAAE;AAChC,EAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;AAClC,IAAM,UAAU,GAAG,IAAI,CAAC;AACxB,IAAM,IAAI,GAAG,MAAM,EAAE,CAAC;AACtB,GAAK;AACL;AACA;AACA;AACA;AACA,EAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AACtB;AACA;AACA;AACA;AACA,EAAI,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,YAAY;AACvC,IAAM,UAAU;AAChB,IAAM,IAAI,CAAC,KAAK;AAChB,IAAM,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;AAC9C,GAAK,CAAC;AACJ,CAAC,CAAA;AACH;AACE;AACF;AACA;AACA;AACA;AACA;AACE,OAAA,CAAO,sCAAa,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE;AAC1C,EAAI,IAAI,IAAI,KAAK,UAAU,CAAC,aAAa,EAAE;AAC3C,IAAM,OAAO,IAAI,aAAa,CAAC,MAAM,CAAC,CAAC;AACvC,GAAK;AACL,EAAI,IAAI,IAAI,KAAK,UAAU,CAAC,YAAY,EAAE;AAC1C,IAAM,OAAO,IAAI,YAAY,CAAC,MAAM,CAAC,CAAC;AACtC,GAAK;AACH,CAAC,CAAA;AACH;AACE;AACF;AACA;AACA;AACA;AACE,OAAA,CAAA,SAAA,CAAA,OAAA,GAAA,SAAA,OAAA,EAAQ,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE;AAC/B,EAAI,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;AAC9C,EAAI,OAAO,IAAI,CAAC;AACd,CAAC,CAAA;AACH;AACE;AACF;AACA;AACA;AACE,OAAA,CAAA,SAAA,CAAA,OAAA,GAAA,SAAA,OAAA,EAAQ,GAAG,EAAE,QAAQ,EAAE;AACzB,EAAI,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;AACxC,EAAI,OAAO,IAAI,CAAC;AACd,CAAC,CAAA;AACH;AACE;AACF;AACA;kBACE,WAAW,GAAA,SAAA,WAAA,EAAC,QAAQ,EAAE;AACxB,EAAI,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AACrC,CAAC,CAAA;AACH;AACE;AACF;AACA;AACA;AACE,OAAA,CAAA,SAAA,CAAA,UAAA,GAAA,SAAA,UAAA,EAAW,GAAG,EAAE,QAAQ,EAAE;AAC5B,EAAI,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;AACzC,CAAA;;ACtFFA,IAAM,KAAK,GAAG;AACd,EAAE,MAAM,EAAE,CAAC;AACX,EAAE,MAAM,EAAE,CAAC;AACX,EAAE,IAAI,EAAE,CAAC;AACT,EAAE,OAAO,EAAE,CAAC;AACZ,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAe,CAAC,CAAC,KAAK,CAAC,MAAM;AAC7B,oCAAoC;AACpC,IAAI,OAAO,EAAE,EAAE,KAAA,EAAA,KAAK,EAAE;AACtB;AACA;AACA;AACA;AACA,IAAI,OAAO,EAAE;AACb,MAAM,IAAI,EAAE,KAAK,CAAC,MAAM;AACxB,MAAM,SAAS,EAAE,8BAA8B;AAC/C,MAAM,eAAe,EAAE;AACvB,QAAQ,SAAS,EAAE,wBAAwB;AAC3C,QAAQ,UAAU,EAAE,8BAA8B;AAClD,QAAQ,eAAe,EAAE,WAAW;AACpC,QAAQ,YAAY,EAAE,2BAA2B;AACjD,QAAQ,WAAW,EAAE,+BAA+B;AACpD,QAAQ,WAAW,EAAE,+BAA+B;AACpD,QAAQ,gBAAgB,EAAE,eAAe;AACzC,QAAQ,WAAW,EAAE,+BAA+B;AACpD,QAAQ,SAAS,EAAE,6BAA6B;AAChD,QAAQ,WAAW,EAAE,+BAA+B;AACpD,QAAQ,aAAa,EAAE,UAAU;AACjC,QAAQ,cAAc,EAAE,WAAW;AACnC,QAAQ,aAAa,EAAE,UAAU;AACjC,QAAQ,YAAY,EAAE,MAAM;AAC5B,QAAQ,cAAc,EAAE,QAAQ;AAChC,QAAQ,cAAc,EAAE,QAAQ;AAChC,QAAQ,gBAAgB,EAAE,GAAG;AAC7B,QAAQ,cAAc,EAAE,sCAAsC;AAC9D,OAAO;AACP,MAAM,aAAa,EAAE,KAAK;AAC1B,MAAM,QAAQ,EAAE,GAAG;AACnB,MAAM,mBAAmB,EAAE,WAAW;AACtC,MAAM,QAAQ;AACd,QAAQ,gCAAgC;AACxC,QAAQ,mEAAmE;AAC3E,QAAQ,gGAAgG;AACxG,QAAQ,iEAAiE;AACzE,QAAQ,oEAAoE;AAC5E,QAAQ,iCAAiC;AACzC,QAAQ,mDAAmD;AAC3D,QAAQ,SAAS;AACjB,MAAM,YAAY;AAClB,QAAQ,yCAAyC;AACjD,QAAQ,iGAAiG;AACzG,QAAQ,qGAAqG;AAC7G,QAAQ,6FAA6F;AACrG,QAAQ,OAAO;AACf,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,UAAU,EAAE,UAAU,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE;AAC9D;AACA;AACA;AACA,MAAM,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;AAChC;AACA;AACA;AACA;AACA,MAAM,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;AAC9B;AACA;AACA;AACA;AACA,MAAM,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;AACxC;AACA;AACA;AACA;AACA;AACA,MAAM,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC;AAC5B;AACA,MAAM,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;AAC/D,KAAK;AACL;AACA;AACA;AACA;AACA,IAAI,WAAW,EAAE,YAAY;AAC7B,MAAM,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC/C;AACA,MAAM;AACN,QAAQ,IAAI,CAAC,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI;AACxC,SAAS,IAAI,CAAC,SAAS,CAAC,QAAQ,IAAI,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;AAC7D,QAAQ;AACR,QAAQA,IAAM,UAAU,IAAI,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,OAAO,CAAC,MAAM;AAC/D,UAAU,GAAG;AACb,UAAU,2BAA2B;AACrC,SAAS,CAAC,CAAC;AACX,QAAQ,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,UAAU,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;AACpE,QAAQ,UAAU,CAAC,IAAI,GAAG,OAAO,CAAC;AAClC,QAAQ,UAAU,CAAC,SAAS,GAAG,iCAAiC,CAAC;AACjE,QAAQ,CAAC,CAAC,QAAQ,CAAC,uBAAuB,CAAC,UAAU,CAAC,CAAC;AACvD,QAAQ,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,UAAU,EAAE,OAAO,EAAE,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,CAAC;AAC1E,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;AACA,IAAI,SAAS,EAAE,YAAY;AAC3B,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;AACnE,KAAK;AACL;AACA;AACA;AACA;AACA,IAAI,kBAAkB,EAAE,UAAU,GAAG,EAAE;AACvC,MAAM,CAAC,CAAC,QAAQ,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;AACrC,MAAM,IAAI,CAAC,SAAS,EAAE,CAAC;AACvB,MAAM,IAAI,CAAC,KAAK,EAAE,CAAC;AACnB,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,IAAI,cAAc,EAAE,YAAY;AAChC,MAAMD,IAAI,OAAO,CAAC;AAClB,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,EAAE;AAC5C,QAAQ,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AACjE,OAAO,MAAM;AACb,QAAQA,IAAI,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;AAC7C,QAAQA,IAAI,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,gBAAgB,CAAC;AACvE,QAAQ,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,OAAO,EAAE;AACjD,UAAU,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC;AAC/C,SAAS;AACT,QAAQ,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,MAAM,EAAE;AAChD,UAAU,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,cAAc,CAAC;AACnE,SAAS;AACT,QAAQC,IAAM,SAAS,GAAG,EAAE,CAAC;AAC7B,QAAQ,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE;AACrC,UAAU,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;AACrE,SAAS;AACT,QAAQ,IAAI,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;AACtC,UAAU,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC;AACtE,SAAS;AACT,QAAQ,OAAO,GAAG,UAAU;AAC5B,UAAU,QAAQ;AAClB,UAAU,CAAC,CAAC,IAAI,CAAC,MAAM;AACvB,YAAY,EAAE;AACd,YAAY,IAAI,CAAC,SAAS,IAAI,EAAE;AAChC,YAAY,IAAI,CAAC,OAAO,CAAC,eAAe;AACxC,YAAY;AACZ,cAAc,UAAU,EAAE,UAAU;AACpC,cAAc,MAAM,EAAE,IAAI,CAAC,YAAY;AACvC,gBAAgB,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;AACxC,gBAAgB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AACnC,eAAe;AACf,cAAc,IAAI,EAAE,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC;AACvC,aAAa;AACb,WAAW;AACX,SAAS,CAAC;AACV,OAAO;AACP,MAAM,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;AAC9B,MAAM,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAClD,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;AACzB,KAAK;AACL;AACA;AACA;AACA;AACA,IAAI,WAAW,EAAE,YAAY;AAC7B,MAAM;AACN,QAAQ,IAAI,CAAC,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,MAAM;AAC1C,QAAQ,IAAI,CAAC,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,MAAM;AAC1C,QAAQ;AACR,QAAQA,IAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,aAAa;AACpD,UAAU,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,SAAS;AACtD,SAAS,CAAC;AACV,QAAsB,IAAI,CAAC,aAAa;AACxC,UAAU,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,UAAU;AACvD,UAAU;AACV;AACA,QAAQ,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;AAC5D,QAAQ,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;AACnD,OAAO,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,OAAO,EAAE;AACtD,QAAQ,CAAC,CAAC,QAAQ,CAAC,QAAQ;AAC3B,UAAU,IAAI,CAAC,UAAU;AACzB,UAAU,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,SAAS;AACtD,UAAU,OAAO;AACjB,UAAU,IAAI,CAAC,YAAY;AAC3B,UAAU,IAAI;AACd,SAAS,CAAC;AACV,QAAQ,CAAC,CAAC,QAAQ,CAAC,QAAQ;AAC3B,UAAU,IAAI,CAAC,UAAU;AACzB,UAAU,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,WAAW;AACxD,UAAU,OAAO;AACjB,UAAU,IAAI,CAAC,cAAc;AAC7B,UAAU,IAAI;AACd,SAAS,CAAC;AACV,QAAQ,CAAC,CAAC,QAAQ,CAAC,QAAQ;AAC3B,UAAU,IAAI,CAAC,UAAU;AACzB,UAAU,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,WAAW;AACxD,UAAU,OAAO;AACjB,UAAU,IAAI,CAAC,cAAc;AAC7B,UAAU,IAAI;AACd,SAAS,CAAC;AACV,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;AACA,IAAI,SAAS,EAAE,YAAY;AAC3B,MAAMA,IAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,aAAa;AACnD,QAAQ,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,UAAU;AACrD,OAAO,CAAC;AACR;AACA;AACA,MAAMA,IAAM,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;AAC/C,MAAM,KAAK,CAAC,KAAK,EAAE,CAAC;AACpB,MAAM,KAAK,CAAC,iBAAiB,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;AACpD,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,IAAI,YAAY,EAAE,UAAU,GAAG,EAAE;AACjC,MAAM,CAAC,CAAC,QAAQ,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;AACrC,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;AAChE,MAAM,IAAI,CAAC,KAAK,EAAE,CAAC;AACnB,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,IAAI,cAAc,EAAE,UAAU,GAAG,EAAE;AACnC,MAAM,CAAC,CAAC,QAAQ,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;AACrC,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;AAClE,MAAM,IAAI,CAAC,KAAK,EAAE,CAAC;AACnB,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,IAAI,cAAc,EAAE,UAAU,GAAG,EAAE;AACnC,MAAM,CAAC,CAAC,QAAQ,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;AACrC,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;AAChE,MAAM,IAAI,CAAC,KAAK,EAAE,CAAC;AACnB,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,IAAI,gBAAgB,EAAE,YAAY;AAClC,MAAMA,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;AACnC,MAAM,IAAI,OAAO,CAAC,eAAe,EAAE;AACnC,QAAQ,OAAO,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAClD,OAAO;AACP,MAAMA,IAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,aAAa;AACnD,QAAQ,GAAG,GAAG,OAAO,CAAC,eAAe,CAAC,UAAU;AAChD,OAAO,CAAC;AACR,MAAMA,IAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,aAAa;AACrD,QAAQ,GAAG,GAAG,OAAO,CAAC,eAAe,CAAC,YAAY;AAClD,OAAO,CAAC;AACR,MAAM,OAAO;AACb,QAAQ,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;AACxC,QAAQ,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AACjC,QAAQ,IAAI,EAAE,KAAK,CAAC,KAAK;AACzB,QAAQ,EAAE,EAAE,OAAO,CAAC,KAAK,IAAI,MAAM,EAAE;AACrC,OAAO,CAAC;AACR,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,IAAI,SAAS,EAAE,UAAU,GAAG,EAAE;AAC9B,MAAM,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC3B;AACA,MAAMA,IAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,aAAa;AACnD,QAAQ,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,UAAU;AACrD,OAAO,CAAC;AACR,MAAM,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;AAC3E;AACA,MAAM,IAAI,KAAK,CAAC,KAAK,KAAK,EAAE,IAAI,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE;AAC5D,QAAQ,KAAK,CAAC,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC;AAC/D,OAAO;AACP;AACA,MAAMA,IAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,KAAI,YAAO,EAAA,OAAA,IAAA,CAAA,EAAI,CAAC,CAAC;AAClE;AACA,MAAM,IAAI,KAAK,CAAC,KAAK,KAAK,EAAE,IAAI,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC,EAAE;AAClE,QAAQA,IAAM,QAAQ,GAAG,CAAC,CAAC,IAAI,CAAC,MAAM;AACtC,UAAU,EAAE;AACZ,UAAU,IAAI,CAAC,SAAS;AACxB,UAAU,IAAI,CAAC,gBAAgB,EAAE;AACjC,SAAS,CAAC;AACV,QAAQA,IAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC;AAC9B;AACA,QAAQ,IAAI,CAAC,KAAK,EAAE,CAAC;AACrB,QAAQ,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,MAAM,EAAE;AAChD,UAAU,GAAG,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC,CAAC;AACvD,SAAS,MAAM;AACf,UAAU,GAAG,CAAC,IAAI,CAAC,iBAAiB,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC,CAAC;AAC1D,SAAS;AACT,OAAO,MAAM;AACb,QAAQ,KAAK,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;AAC1E,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,YAAY,EAAE,UAAU,MAAM,EAAE,IAAI,EAAE;AAC1C,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE;AACrC,QAAQ,OAAO,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;AAClE,OAAO;AACP,MAAM,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,IAAI;AACtE,QAAQ,SAAS;AACjB,OAAO,CAAC;AACR,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,KAAK,EAAE,UAAU,GAAG,EAAE;AAC1B,MAAM,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,SAAS,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;AAC5D,MAAM,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,CAAC;AAClE,MAAM,OAAO,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;AACrD,KAAK;AACL;AACA;AACA;AACA;AACA,IAAI,QAAQ,EAAE,UAAU,GAAG,EAAE;AAC7B,MAAM,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;AAC7D,MAAM,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;AACjD,KAAK;AACL;AACA;AACA;AACA;AACA,IAAI,kBAAkB,EAAE,YAAY;AACpC;AACA,MAAM,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,IAAI,CAAC,MAAM;AACpC,QAAQ,IAAI,CAAC,SAAS,IAAI,EAAE;AAC5B,QAAQ,IAAI,CAAC,gBAAgB,EAAE;AAC/B,OAAO,CAAC;AACR,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;AAC7C,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,IAAI,cAAc,EAAE,UAAU,CAAC,EAAE;AACjC,MAAM,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;AAC9C,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,OAAO,GAAG,EAAE,CAAC;AACzC,MAAM,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;AAC/B,MAAM,IAAI,CAAC,MAAM,EAAE,CAAC;AACpB,KAAK;AACL,GAAG;AACH,CAAC;;AC9XD;AACA;AACA;AACAA,IAAM,eAAe,GAAG,CAAC,UAAC,gBAAgB,EAAK;AAC/C,EAAEA,IAAM,OAAO;AACf,IAAI,gBAAgB,CAAC,OAAO;AAC5B,IAAI,gBAAgB,CAAC,qBAAqB;AAC1C,IAAI,gBAAgB,CAAC,kBAAkB;AACvC,IAAI,gBAAgB,CAAC,iBAAiB;AACtC,IAAI,gBAAgB,CAAC,gBAAgB;AACrC;AACA,IAAI,UAAU,QAAQ,EAAE;AACxB,MAAM,IAAI,IAAI,GAAG,IAAI;AACrB,QAAQ,MAAM,GAAG,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,QAAQ;AACjD,QAAQ,KAAK,GAAG,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;AAClD;AACA,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,EAAE,CAAC,EAAE;AACxD,QAAQ,IAAI,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,EAAA,EAAE,OAAO,IAAI,CAAC,EAAA;AAC1C,OAAO;AACP,MAAM,OAAO,KAAK,CAAC;AACnB,KAAK,CAAC;AACN;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,UAAU,OAAO,EAAE,QAAQ,EAAE;AACtC,IAAI,OAAO,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;AAC3C,GAAG,CAAC;AACJ,CAAC,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,OAAO,CAAC,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,IAAI,EAAE;AACrD,EAAE,OAAO,GAAG,SAAS;AACrB,MAAM;AACN,QAAQ,UAAU,EAAE,OAAO;AAC3B,OAAO;AACP,MAAM,OAAO,CAAC;AACd;AACA,EAAE,IAAI,GAAG,IAAI,IAAI,QAAQ,CAAC;AAC1B;AACA;AACA;AACA,EAAE,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC,UAAU,KAAK,OAAO,KAAK,QAAQ,EAAE;AACjE,IAAI,IAAI,eAAe,CAAC,OAAO,EAAE,QAAQ,CAAC,EAAA,EAAE,OAAO,OAAO,CAAC,EAAA;AAC3D;AACA;AACA;AACA,IAAI,IAAI,OAAO,KAAK,IAAI,EAAE,EAAA,OAAO,IAAI,CAAC,EAAA;AACtC,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAAC,QAAQ,CAAC,QAAQ,GAAG,UAAU,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE;AAC9D,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,EAAE,IAAI,EAAE,UAAC,GAAG,EAAK;AAC1C,IAAIA,IAAM,MAAM,GAAG,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,UAAU,CAAC;AAChD,IAAI,GAAG,CAAC,cAAc,GAAG,OAAO,CAAC,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;AAC7D,IAAI,IAAI,GAAG,CAAC,cAAc,IAAI,CAAC,GAAG,CAAC,kBAAkB,EAAE;AACvD,MAAM,EAAE,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE,EAAE,GAAG,CAAC,CAAC;AAC/B,KAAK;AACL,GAAG,CAAC,CAAC;AACL,CAAC;;AC5ED;AACA,CAAC,CAAC,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,IAAI,CAAC,SAAS,IAAI,UAAU,CAAC;AAClD;AACA;AACA;AACA;AACA;AACA;AACA,gBAAe,CAAC,CAAC,OAAO,CAAC,MAAM;AAC/B,qCAAqC;AACrC,IAAI,OAAO,EAAE;AACb,MAAA,OAAA,EAAM,OAAO;AACb,MAAA,SAAA,EAAM,SAAS;AACf,KAAK;AACL;AACA;AACA;AACA;AACA,IAAI,OAAO,EAAE;AACb,MAAM,YAAY,EAAE,IAAI;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,IAAI,EAAE,mBAAmB;AAC/B,MAAM,QAAQ,EAAE,UAAU;AAC1B;AACA,MAAM,cAAc,EAAE,uCAAuC;AAC7D,MAAM,aAAa,EAAE,UAAU;AAC/B,MAAM,WAAW,EAAE,kBAAkB;AACrC,MAAM,SAAS,EAAE,gBAAgB;AACjC,MAAM,SAAS,EAAE,gBAAgB;AACjC,MAAM,gBAAgB,EAAE,wBAAwB;AAChD,MAAM,gBAAgB,EAAE,wBAAwB;AAChD,MAAM,mBAAmB,EAAE,kBAAkB;AAC7C,MAAM,YAAY,EAAE,qBAAqB;AACzC,MAAM,oBAAoB,EAAE,qBAAqB;AACjD;AACA,MAAM,YAAY,EAAE,qBAAqB;AACzC,MAAM,eAAe,EAAE,GAAG;AAC1B;AACA,MAAM,SAAS,EAAE;AACjB,QAAQ,UAAU,EAAE,iBAAiB;AACrC,OAAO;AACP;AACA,MAAM,gBAAgB;AACtB,QAAQ,sDAAsD;AAC9D,QAAQ,gDAAgD;AACxD,QAAQ,sDAAsD;AAC9D,QAAQ,0DAA0D;AAClE,QAAQ,OAAO;AACf;AACA,MAAM,aAAa;AACnB,QAAQ,+CAA+C;AACvD,QAAQ,8BAA8B;AACtC;AACA,MAAM,eAAe,EAAE,2BAA2B;AAClD;AACA,MAAM,uBAAuB,EAAE;AAC/B,QAAQ,SAAS,EAAE,eAAe;AAClC,QAAQ,SAAS,EAAE,eAAe;AAClC,QAAQ,WAAW,EAAE,iBAAiB;AACtC,QAAQ,WAAW,EAAE,iBAAiB;AACtC,QAAQ,UAAU,EAAE,iBAAiB;AACrC,OAAO;AACP;AACA,MAAM,sBAAsB,EAAE;AAC9B,QAAQ,QAAQ,EAAE,IAAI;AACtB,QAAQ,SAAS,EAAE,IAAI;AACvB,OAAO;AACP;AACA,MAAM,KAAK,EAAE,WAAW;AACxB,MAAM,YAAY,EAAE,kBAAkB;AACtC,MAAM,kBAAkB,EAAE,kBAAkB;AAC5C,MAAM,eAAe,EAAE,IAAI;AAC3B,MAAM,WAAW,EAAE,IAAI;AACvB,MAAM,cAAc,EAAE,IAAI;AAC1B,MAAM,WAAW,EAAE,IAAI;AACvB,MAAM,YAAY,EAAE,IAAI;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,aAAa;AACnB,QAAQ,+DAA+D;AACvE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,eAAe,EAAE,UAAU,QAAQ,EAAE;AAC3C,QAAQ,OAAO,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE;AACtD,UAAU,MAAM,EAAE,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC;AACpD,UAAU,IAAI,EAAE,QAAQ,CAAC,IAAI;AAC7B,UAAU,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AACnC,SAAS,CAAC,CAAC;AACX,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,IAAI,UAAU,EAAE,UAAU,OAAO,EAAE;AACnC,MAAM,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;AAC9B;AACA;AACA;AACA;AACA;AACA,MAAM,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;AACtB;AACA;AACA;AACA;AACA,MAAM,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AACxB;AACA;AACA;AACA;AACA,MAAM,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AAC1B;AACA;AACA;AACA;AACA,MAAM,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;AAC7B;AACA;AACA;AACA;AACA,MAAM,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AACxB;AACA;AACA;AACA;AACA,MAAM,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;AAC/B;AACA,MAAM,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;AACvC;AACA;AACA;AACA;AACA,MAAM,IAAI,CAAC,QAAQ;AACnB,QAAQ,OAAO,CAAC,OAAO;AACvB,SAAS,IAAI,CAAC,OAAO,CAAC,YAAY;AAClC,YAAY,IAAI,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,UAAU,CAAC,YAAY,CAAC;AACnE,YAAY,IAAI,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,UAAU,CAAC,aAAa,CAAC,CAAC,CAAC;AACtE;AACA,MAAM,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;AAC9D,KAAK;AACL;AACA;AACA;AACA;AACA,IAAI,KAAK,EAAE,UAAU,GAAG,EAAE;AAC1B,MAAMA,IAAM,SAAS,IAAI,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,OAAO,CAAC,MAAM;AAC3D,QAAQ,KAAK;AACb,QAAQ,IAAI,CAAC,OAAO,CAAC,cAAc;AACnC,OAAO,CAAC,CAAC;AACT;AACA,MAAM,CAAC,CAAC,QAAQ,CAAC,uBAAuB,CAAC,SAAS,CAAC,CAAC,wBAAwB;AAC5E,QAAQ,SAAS;AACjB,OAAO,CAAC;AACR,MAAM,SAAS,CAAC,SAAS;AACzB,QAAQ,cAAc;AACtB,QAAQ,IAAI,CAAC,OAAO,CAAC,WAAW;AAChC,QAAQ,iBAAiB;AACzB,QAAQ,IAAI,CAAC,OAAO,CAAC,gBAAgB;AACrC,QAAQ,IAAI;AACZ,QAAQ,eAAe;AACvB,QAAQ,IAAI,CAAC,OAAO,CAAC,SAAS;AAC9B,QAAQ,kBAAkB,CAAC;AAC3B;AACA,MAAM,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC,aAAa,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;AACzE,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;AAC5C;AACA,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;AAC/C;AACA,MAAMA,IAAM,OAAO,GAAG,CAAC,CAAC,OAAO,CAAC,MAAM;AACtC,QAAQ,KAAK;AACb,QAAQ,IAAI,CAAC,OAAO,CAAC,YAAY;AACjC,QAAQ,IAAI,CAAC,UAAU;AACvB,OAAO,CAAC;AACR,MAAM,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;AAC7C;AACA,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;AACzB;AACA,MAAM,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,SAAS,EAAE,OAAO,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC,EAAE;AAC/D,QAAQ,SAAS;AACjB,QAAQ,aAAa;AACrB,QAAQ,CAAC,CAAC,QAAQ,CAAC,eAAe;AAClC,OAAO,CAAC;AACR;AACA,MAAM,GAAG;AACT,SAAS,EAAE,CAAC,cAAc,EAAE,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC;AAC3D,SAAS,EAAE,CAAC,cAAc,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC;AACtD,SAAS,EAAE,CAAC,iBAAiB,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC;AAC5D,SAAS,EAAE,CAAC,eAAe,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC;AACxD,SAAS,EAAE,CAAC,eAAe,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC;AACxD,SAAS,EAAE,CAAC,kBAAkB,EAAE,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC;AAC9D,SAAS,EAAE,CAAC,iBAAiB,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC;AAC5D,SAAS,EAAE,CAAC,QAAQ,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;AAC9C;AACA,MAAM,OAAO,SAAS,CAAC;AACvB,KAAK;AACL;AACA;AACA;AACA;AACA,IAAI,QAAQ,EAAE,UAAU,GAAG,EAAE;AAC7B,MAAM,GAAG;AACT,SAAS,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC;AAC5D,SAAS,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC;AACvD,SAAS,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC;AAC7D,SAAS,GAAG,CAAC,eAAe,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC;AACzD,SAAS,GAAG,CAAC,eAAe,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC;AACzD,SAAS,GAAG,CAAC,kBAAkB,EAAE,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC;AAC/D,SAAS,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC;AAC7D,SAAS,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;AAC/C;AACA,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE,EAAA,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,EAAA;AACrD;AACA,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE;AACrC,QAAQ,CAAC,CAAC,QAAQ,CAAC,GAAG;AACtB,UAAU,IAAI,CAAC,UAAU,CAAC,aAAa;AACvC,YAAY,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,oBAAoB;AACnD,WAAW;AACX,UAAU,OAAO;AACjB,UAAU,IAAI,CAAC,mBAAmB;AAClC,UAAU,IAAI;AACd,SAAS,CAAC;AACV,OAAO;AACP;AACA,MAAM,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AAC1B,MAAM,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AACzB,MAAM,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;AAC7B,KAAK;AACL;AACA;AACA;AACA;AACA,IAAI,OAAO,EAAE,YAAY;AACzB,MAAM,OAAO,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACrD,KAAK;AACL;AACA;AACA;AACA;AACA,IAAI,WAAW,EAAE,UAAU,SAAS,EAAE;;AAAC;AACvC,MAAM,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,OAAO,CAAC,MAAM;AAC1C,QAAQ,KAAK;AACb,QAAQ,IAAI,CAAC,OAAO,CAAC,gBAAgB;AACrC,QAAQ,IAAI,CAAC,UAAU;AACvB,OAAO,CAAC;AACR,MAAM,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,OAAO,CAAC,MAAM;AACnC,QAAQ,IAAI;AACZ,QAAQ,IAAI,CAAC,OAAO,CAAC,SAAS;AAC9B,QAAQ,IAAI,CAAC,YAAY;AACzB,OAAO,CAAC;AACR;AACA;AACA,MAAM,CAAC,CAAC,QAAQ,CAAC,QAAQ;AACzB,QAAQ,IAAI,CAAC,KAAK;AAClB,QAAQ,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,uBAAuB,CAAC,SAAS;AAC5D,QAAQ,OAAO;AACf,QAAQ,IAAI,CAAC,gBAAgB;AAC7B,QAAQ,IAAI;AACZ,OAAO,CAAC;AACR;AACA,MAAM,IAAI,CAAC,oBAAoB,EAAE,CAAC;AAClC;AACA,MAAM,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;AACrC,QAAQ,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;AACrC,OAAO,MAAM,IAAI,OAAO,SAAS,KAAK,UAAU,EAAE;AAClD,QAAQ,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC,CAAC;AACvC,OAAO,MAAM;AACb,QAAQ,IAAI,CAAC,QAAQ,CAAC,WAAW,WAAE,SAAS,EAAKC,EAAAA,OAAAA,QAAI,CAAC,YAAY,CAAC,SAAS,CAAA,CAAA,EAAC,CAAC,CAAC;AAC/E,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;AACA,IAAI,oBAAoB,EAAE,YAAY;AACtC,MAAM,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,UAAU;AACvC,QAAQ,IAAI,CAAC,OAAO,CAAC,aAAa;AAClC,QAAQ,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,uBAAuB,EAAE;AAC5D,UAAU,IAAI,EAAE;AAChB,YAAY,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY;AACnD,WAAW;AACX,SAAS,CAAC;AACV,OAAO,CAAC;AACR,KAAK;AACL;AACA;AACA;AACA;AACA,IAAI,WAAW,EAAE,YAAY;AAC7B,MAAMD,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;AACvC,MAAM,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,SAAS;AACvC,QAAQ,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,GAAG,EAAE,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC;AACpD;AACA,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,KAAK,SAAS,EAAE;AAC/C,QAAQ,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,EAAE,4BAA4B,CAAC,CAAC;AAC1E,OAAO;AACP,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE;AACrC,QAAQA,IAAM,SAAS,GAAG,CAAC,CAAC,OAAO,CAAC,MAAM;AAC1C,UAAU,KAAK;AACf,UAAU,IAAI,CAAC,OAAO,CAAC,oBAAoB;AAC3C,SAAS,CAAC;AACV,QAAQ,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,EAAE;AACtC,UAAU,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;AAC9D,UAAU,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;AACtC,UAAU,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,SAAS,CAAC,GAAG;AACpD,YAAY,IAAI,CAAC,OAAO,CAAC,mBAAmB;AAC5C,WAAW,CAAC;AACZ,UAAU,SAAS,CAAC,SAAS;AAC7B,YAAY,6BAA6B;AACzC,YAAY,wBAAwB;AACpC,YAAY,IAAI,CAAC,OAAO,CAAC,kBAAkB;AAC3C,YAAY,SAAS,CAAC;AACtB,UAAU,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,SAAS,EAAE,OAAO,EAAE,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAC;AAC5E,SAAS;AACT,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;AACA,IAAI,mBAAmB,EAAE,UAAU,GAAG,EAAE;AACxC,MAAM,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC3B,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC;AACtB,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;AACrC,QAAQ,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;AACrC,OAAO,CAAC,CAAC;AACT,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,gBAAgB,EAAE,UAAU,SAAS,EAAE;AAC3C,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE;AACxC,QAAQ,OAAO,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;AAClE,OAAO;AACP,MAAM,OAAO,SAAS,CAAC;AACvB,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,sBAAsB,EAAE,UAAU,SAAS,EAAE;AACjD,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,qBAAqB,EAAE;AAC9C,QAAQ,OAAO,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;AACxE,OAAO;AACP,MAAM,OAAO,SAAS,CAAC;AACvB,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,IAAI,YAAY,EAAE,UAAU,SAAS,EAAE;AACvC,MAAMD,IAAI,IAAI,GAAG,EAAE,CAAC;AACpB,MAAMA,IAAI,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,CAAC;AAE7C;AACA;AACA,MAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;AACnD;AACA;AACA,MAAM,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;AAChD;AACA,MAAM,KAAKA,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;AAC5D,QAAQ,IAAI,IAAI,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;AACvD,OAAO;AACP;AACA,MAAM,IAAI,IAAI,KAAK,EAAE,EAAE;AACvB;AACA,QAAQ,IAAI,QAAQ,EAAE;AACtB,UAAU,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC;AACtC,SAAS,MAAM;AACf,UAAU,IAAI,CAAC,KAAK,CAAC,SAAS,IAAI,IAAI,CAAC;AACvC,SAAS;AACT,OAAO;AACP;AACA,MAAM,IAAI,IAAI,CAAC,YAAY,EAAE;AAC7B,QAAQC,IAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC;AAC1C,QAAQA,IAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC;AACpD,QAAQ,SAAS,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;AAC3C,QAAQ,MAAM,CAAC,UAAU,CAAC,YAAY;AACtC,UAAU,SAAS,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;AAChD,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;AACzC,OAAO,MAAM;AACb,QAAQ,IAAI,CAAC,aAAa,EAAE,CAAC;AAC7B,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;AACA,IAAI,aAAa,EAAE,YAAY;AAC/B,MAAMA,IAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC;AAC5C,MAAMD,IAAI,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC;AAC5C,MAAMC,IAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,SAAS,CAAC;AAEvD;AACA,MAAMA,IAAM,IAAI;AAChB,QAAQ,CAAC,SAAS,GAAG,GAAG,KAAK,IAAI,CAAC,OAAO,CAAC,cAAc,IAAI,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC;AACxE;AACA,MAAM,SAAS,MAAM,CAAC,SAAS,EAAE;AAEjC;AACA;AACA,QAAQ,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,EAAE,SAAS,CAAC,CAAC;AAC9C,QAAQ,WAAW,CAAC,SAAS,GAAG,GAAG,CAAC;AACpC,QAAQ,IAAI,GAAG,KAAK,SAAS,EAAE;AAC/B,UAAU,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;AAC1C,SAAS;AACT,OAAO;AACP,MAAM,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;AACtC,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,mBAAmB,EAAE,UAAU,QAAQ,EAAE;AAC7C,MAAM,IAAI,QAAQ,CAAC,OAAO,EAAE;AAC5B,QAAQ,OAAO,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,QAAQ,CAAC,CAAC;AAClE,OAAO;AACP;AACA,MAAM,IAAI,CAAC,OAAO,CAAC,uBAAuB,CAAC,IAAI;AAC/C,QAAQ,IAAI,CAAC,2BAA2B,CAAC,QAAQ,CAAC,CAAC;AACnD;AACA,MAAM,OAAO,UAAU;AACvB,QAAQ,IAAI,CAAC,OAAO,CAAC,gBAAgB;AACrC,QAAQ,IAAI,CAAC,OAAO,CAAC,uBAAuB;AAC5C,OAAO,CAAC;AACR,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,2BAA2B,EAAE,UAAU,QAAQ,EAAE;AACrD,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,0BAA0B,EAAE;AACnD,QAAQ,OAAO,IAAI,CAAC,OAAO,CAAC,0BAA0B,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;AAC5E,OAAO;AACP,MAAM,OAAO;AACb,QAAQ,MAAM,EAAE,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC;AAClD,QAAQ,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC;AAC5C,QAAQ,IAAI,EAAE,QAAQ,CAAC,IAAI;AAC3B,QAAQ,EAAE,EAAE,QAAQ,CAAC,EAAE;AACvB,OAAO,CAAC;AACR,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,IAAI,YAAY,EAAE,UAAU,MAAM,EAAE;AACpC,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE;AACrC,QAAQ,OAAO,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;AAC5D,OAAO;AACP,MAAM,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,SAAS,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AACrE,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,IAAI,UAAU,EAAE,UAAU,IAAI,EAAE;AAChC,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE;AACnC,QAAQ,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AACxD,OAAO;AACP,MAAM,OAAO,IAAI,CAAC;AAClB,KAAK;AACL;AACA;AACA;AACA;AACA,IAAI,MAAM,EAAE,YAAY;AACxB,MAAM,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;AACtE,MAAM,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;AAChC,KAAK;AACL;AACA;AACA;AACA;AACA,IAAI,QAAQ,EAAE,YAAY;AAC1B,MAAM,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;AACzE,MAAM,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;AAC/B,KAAK;AACL;AACA;AACA;AACA;AACA,IAAI,QAAQ,EAAE,UAAU,GAAG,EAAE;AAC7B,MAAMA,IAAM,QAAQ,GAAG,CAAC,CAAC,OAAO,CAAC,QAAQ;AACzC,QAAQ,IAAI,CAAC,UAAU;AACvB,QAAQ,IAAI,CAAC,OAAO,CAAC,aAAa;AAClC,OAAO,CAAC;AACR,MAAMD,IAAI,MAAM,GAAG,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,UAAU,CAAC;AAChD;AACA,MAAM,IAAI,QAAQ,EAAE;AACpB,QAAQ,IAAI,MAAM,KAAK,IAAI,CAAC,UAAU,EAAE;AACxC,UAAU,OAAO,IAAI,CAAC,QAAQ,EAAE,CAAC;AACjC,SAAS;AACT;AACA,QAAQ,OAAO,MAAM,KAAK,IAAI,CAAC,UAAU,EAAE;AAC3C,UAAU;AACV,YAAY,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC;AAChE,YAAY,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC;AACrE,YAAY;AACZ,YAAY,IAAI,CAAC,QAAQ,EAAE,CAAC;AAC5B,YAAY,MAAM;AAClB,WAAW;AACX,UAAU,MAAM,GAAG,MAAM,CAAC,UAAU,CAAC;AACrC,SAAS;AACT,OAAO,MAAM,EAAA,IAAI,CAAC,MAAM,EAAE,CAAC,EAAA;AAC3B,KAAK;AACL;AACA;AACA;AACA;AACA,IAAI,mBAAmB,EAAE,UAAU,GAAG,EAAE;AACxC,MAAM,IAAI,IAAI,CAAC,OAAO,EAAA,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,EAAA;AAC5C;AACA,MAAM,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE;AAC9C,QAAQ,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE;AACvD,QAAQ,SAAS,EAAE,IAAI;AACvB,QAAQ,WAAW,EAAE,IAAI;AACzB,OAAO,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,MAAM,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,YAAY,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;AAC/D;AACA;AACA,MAAM,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,SAAS;AACrD,QAAQ,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;AAC9C,UAAU,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM;AAC1D,SAAS,CAAC;AACV,QAAQ,IAAI,CAAC,OAAO;AACpB,QAAQ,IAAI;AACZ,QAAQ,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,sBAAsB,CAAC;AACxE,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACzB,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,IAAI,cAAc,EAAE,UAAU,QAAQ,EAAE;;AAAC;AACzC,MAAMC,IAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC;AAC5B,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;AACpD,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,EAAE,QAAQ,EAAE,UAAC,IAAI,EAAK;AAC7D,QAAQ,GAAG,CAAC,IAAI,CAAC,gBAAgB,EAAE;AACnC,UAAU,IAAI,EAAE,IAAI;AACpB,SAAS,CAAC,CAAC;AACX,QAAQC,QAAI,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;AAClC,OAAO,CAAC,CAAC;AACT,MAAM,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;AACnC,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,IAAI,iBAAiB,EAAE,UAAU,GAAG,EAAE;;AAAC;AACvC,MAAMD,IAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC;AAC5B,MAAMA,IAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AACrD,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,EAAE,QAAQ,EAAE,UAAC,IAAI,EAAK;AAC7D,QAAQ,GAAG,CAAC,IAAI,CAAC,gBAAgB,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;AACnD,QAAQA,IAAM,IAAI,GAAGC,QAAI,CAAC,KAAK,CAAC;AAChC,QAAQA,QAAI,CAAC,KAAK,GAAG,EAAE,CAAC;AACxB,QAAQ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;AACzD,UAAU,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,QAAQ,CAAC,EAAE,EAAE;AAC1C,YAAY,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC,CAAC;AACxC,WAAW;AACX,SAAS;AACT,QAAQA,QAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;AAChC,OAAO,CAAC,CAAC;AACT,MAAM,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;AACnC,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,cAAc,EAAE,UAAU,QAAQ,EAAE;AACxC,MAAM,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;AAC5C,QAAQ,QAAQ,CAAC,MAAM,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,EAAE,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AACrE,OAAO;AACP,MAAM,OAAO,QAAQ,CAAC;AACtB,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,IAAI,cAAc,EAAE,UAAU,GAAG,EAAE;AACnC,MAAM,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAC1C,MAAM,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AAC1B,MAAM,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AACzB,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,IAAI,YAAY,EAAE,UAAU,EAAE,EAAE;AAChC,MAAM,KAAKF,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;AAC7D,QAAQ,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,EAAA,EAAE,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAA;AAC1D,OAAO;AACP,MAAM,OAAO,IAAI,CAAC;AAClB,KAAK;AACL;AACA;AACA;AACA;AACA,IAAI,eAAe,EAAE,UAAU,GAAG,EAAE;AACpC,MAAM,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AACnC,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,IAAI,eAAe,EAAE,UAAU,GAAG,EAAE;AACpC,MAAM,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AACnC,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,IAAI,iBAAiB,EAAE,UAAU,GAAG,EAAE;AACtC,MAAM,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AACrC,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,IAAI,kBAAkB,EAAE,UAAU,GAAG,EAAE;AACvC,MAAM,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AACtC,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,IAAI,gBAAgB,EAAE,UAAU,QAAQ,EAAE;AAC1C,MAAMC,IAAM,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;AAC/C,MAAMA,IAAM,MAAM,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAC;AAC3E;AACA,MAAM,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,SAAS;AACrD,QAAQ,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;AAC9C,UAAU,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,SAAS,CAAC,KAAK,CAAC,OAAO;AAC3D,SAAS,CAAC;AACV,QAAQ,MAAM;AACd,QAAQ,IAAI;AACZ,QAAQ,QAAQ;AAChB,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACzB,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,IAAI,aAAa,EAAE,UAAU,QAAQ,EAAE;AACvC,MAAMA,IAAM,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;AAC/C,MAAMA,IAAM,MAAM,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAC;AAC3E,MAAM,MAAM,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;AAC/B;AACA,MAAM,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,SAAS;AACrD,QAAQ,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;AAC9C,UAAU,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM;AAC1D,SAAS,CAAC;AACV,QAAQ,MAAM;AACd,QAAQ,IAAI;AACZ,QAAQ,QAAQ;AAChB,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACzB,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,mBAAmB,EAAE,UAAU,QAAQ,EAAE,MAAM,EAAE;AACrD,MAAM,OAAO,UAAU,GAAG,EAAE;AAC5B,QAAQ,IAAI,GAAG,CAAC,IAAI,CAAC,EAAE,KAAK,QAAQ,CAAC,EAAE,EAAE;AACzC,UAAU,MAAM,CAAC,sBAAsB,EAAE,CAAC;AAC1C,UAAU,IAAI,MAAM,CAAC,OAAO,EAAA,EAAE,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,EAAA;AACrD,UAAU,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;AACnC,SAAS;AACT,OAAO,CAAC;AACR,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,aAAa,EAAE,UAAU,MAAM,EAAE,QAAQ,EAAE;;AAAC;AAChD,MAAMA,IAAM,MAAM,GAAG,IAAI,CAAC,CAAC,MAAM,CAAC,MAAM,EAAE;AAC1C,QAAQ,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE;AACvD,QAAQ,WAAW,EAAE,IAAI;AACzB,OAAO,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,MAAMA,IAAM,eAAe,GAAG,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;AACzE,MAAM,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,kBAAkB,EAAE,eAAe,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;AACnE,MAAM,MAAM;AACZ,SAAS,EAAE,CAAC,YAAY,EAAA,YAAQC,EAAAA,OAAAA,QAAI,CAAC,IAAI,CAAC,WAAW,CAACA,QAAI,IAAC,CAAC;AAC5D,SAAS,EAAE,CAAC,QAAQ,EAAE,YAAA,EAAA,OAAMA,QAAI,CAAC,IAAI,CAAC,GAAG,CAAC,kBAAkB,EAAE,eAAe,CAAA,CAAA,EAAC,CAAC,CAAC;AAChF,MAAM,OAAO,MAAM,CAAC;AACpB,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,IAAI,aAAa,EAAE,UAAU,QAAQ,EAAE;AACvC,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE,EAAA,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,EAAA;AACrD,MAAMD,IAAM,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;AAC/C,MAAMA,IAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;AAC1D,MAAMA,IAAM,KAAK,GAAG,IAAI,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,SAAS;AACrD,QAAQ,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;AAC9C,UAAU,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI;AACxD,SAAS,CAAC;AACV,QAAQ,MAAM;AACd,QAAQ,IAAI;AACZ,QAAQ,QAAQ;AAChB,OAAO,CAAC;AACR,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,EAAA,EAAE,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAA;AAC3D,MAAM,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;AAC1B,MAAM,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;AAC5B,KAAK;AACL;AACA;AACA;AACA;AACA,IAAI,aAAa,EAAE,UAAU,QAAQ,EAAE;AACvC,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC;AACxD,MAAM,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;AACnC,KAAK;AACL;AACA;AACA;AACA;AACA,IAAI,eAAe,EAAE,UAAU,QAAQ,EAAE;;AAAC;AAC1C,MAAMA,IAAM,MAAM,GAAG,UAAC,OAAO,EAAK;AAClC,QAAQ,IAAI,CAAC,OAAO,EAAE,EAAA,OAAOC,QAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,EAAA;AAC1D;AACA,QAAQA,QAAI,CAAC,KAAK,CAAC,MAAM,CAACA,QAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;AAC3D,QAAQA,QAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,EAAA,UAAG,QAAQ,EAAK;AAC5D,UAAUA,QAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;AAC5C,SAAS,CAAC,CAAC;AACX,OAAO,CAAC;AACR;AACA,MAAM,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,KAAK,UAAU,EAAE;AACvD,QAAQ,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;AAChD,OAAO,MAAM;AACb,QAAQ,MAAM,CAAC,IAAI,CAAC,CAAC;AACrB,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;AACA,IAAI,kBAAkB,EAAE,UAAU,QAAQ,EAAE;;AAAC;AAC7C,MAAMD,IAAM,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa;AACzC,QAAQ,GAAG;AACX,UAAU,IAAI,CAAC,OAAO,CAAC,uBAAuB,CAAC,SAAS;AACxD,UAAU,YAAY;AACtB,UAAU,QAAQ,CAAC,EAAE;AACrB,UAAU,IAAI;AACd,OAAO,CAAC;AACR;AACA,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC,CAAC;AAC7D;AACA,MAAM,IAAI,EAAE,EAAE;AACd,QAAQ,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;AACpC,QAAQ,UAAU,aAAO;AACzB,UAAU,IAAI,EAAE,CAAC,UAAU,EAAE,EAAA,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,EAAA;AAC3D,UAAU,IAAIC,QAAI,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,EAAEA,EAAAA,QAAI,CAAC,oBAAoB,EAAE,CAAC,EAAA;AACnE,SAAS,EAAE,GAAG,CAAC,CAAC;AAChB,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,gBAAgB,EAAE,UAAU,QAAQ,EAAE;AAC1C,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE;AACxC,QAAQ,OAAO,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;AACjE,OAAO;AACP,MAAM,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;AACtC,KAAK;AACL;AACA;AACA;AACA;AACA,IAAI,gBAAgB,EAAE,UAAU,GAAG,EAAE;AACrC,MAAMD,IAAM,QAAQ,GAAG,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;AACzE,MAAM,IAAI,CAAC,QAAQ,EAAA,EAAE,OAAO,EAAA;AAC5B,MAAM,CAAC,CAAC,QAAQ,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;AACtC;AACA;AACA,MAAM;AACN,QAAQ,CAAC,CAAC,OAAO,CAAC,QAAQ;AAC1B,UAAU,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,UAAU;AACtC,UAAU,IAAI,CAAC,OAAO,CAAC,uBAAuB,CAAC,WAAW;AAC1D,SAAS;AACT,QAAQ;AACR,QAAQ,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;AACvC,OAAO,MAAM;AACb,QAAQ,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC,CAAC;AAC5D,QAAQ,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,EAAA,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAA;AAC1D,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,wBAAwB,EAAE,UAAU,EAAE,EAAE;AAC5C,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,uBAAuB,EAAE;AAChD,QAAQ,OAAO,IAAI,CAAC,OAAO,CAAC,uBAAuB,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;AACnE,OAAO;AACP,MAAM,OAAO,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,CAAC;AAC3D,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,iBAAiB,EAAE,UAAU,QAAQ,EAAE;AAC3C,MAAMA,IAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;AACvD,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC;AACtD,MAAM,OAAO,QAAQ,CAAC,MAAM,CAAC;AAC7B;AACA,MAAM,OAAO,CAAC,CAAC,OAAO,CAAC,UAAU;AACjC,QAAQ;AACR,UAAU,OAAO,EAAE;AACnB,YAAY,IAAI,EAAE,SAAS;AAC3B,YAAY,EAAE,EAAE,QAAQ,CAAC,EAAE;AAC3B,YAAY,UAAU,EAAE,QAAQ;AAChC,WAAW;AACX,SAAS;AACT,QAAQ;AACR,UAAU,IAAI,EAAE,OAAO;AACvB,UAAU,WAAW,EAAE,MAAM;AAC7B,SAAS;AACT,OAAO,CAAC;AACR,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,IAAI,kBAAkB,EAAE,UAAU,QAAQ,EAAE;AAC5C,MAAM,IAAI,QAAQ,CAAC,MAAM,YAAY,CAAC,CAAC,MAAM,EAAE;AAC/C,QAAQ,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,EAAE,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AAC1D,OAAO;AACP,MAAM,OAAO,QAAQ,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;AACvC,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,WAAW,EAAE,UAAU,OAAO,EAAE;AACpC,MAAMA,IAAM,SAAS,GAAG,EAAE,CAAC;AAC3B,MAAM,KAAKD,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,OAAO,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;AACnE,QAAQC,IAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;AAC7C,QAAQ,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,OAAO,EAAE;AAC1C,UAAU,QAAQ,CAAC,UAAU,CAAC,MAAM,GAAG,QAAQ,CAAC,QAAQ,CAAC,WAAW;AACpE,aAAa,MAAM,EAAE;AACrB,aAAa,OAAO,EAAE,CAAC;AACvB,SAAS;AACT,QAAQ,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;AAC5C,OAAO;AACP,MAAM,OAAO,SAAS,CAAC;AACvB,KAAK;AACL;AACA;AACA;AACA;AACA,IAAI,SAAS,EAAE,YAAY;;AAAC;AAC5B,MAAM,OAAO;AACb,QAAQ,IAAI,EAAE,mBAAmB;AACjC,QAAQ,QAAQ,EAAE,CAAC,UAAC,IAAI,EAAK;AAC7B,UAAUA,IAAM,MAAM,GAAG,EAAE,CAAC;AAC5B,UAAU,KAAKD,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;AAC3D,YAAY,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE;AAClC,cAAc,MAAM,CAAC,IAAI,CAACE,QAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3D,aAAa;AACb,WAAW;AACX,UAAU,OAAO,MAAM,CAAC;AACxB,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC;AACtB,OAAO,CAAC;AACR,KAAK;AACL,GAAG;AACH,CAAC;;AC16BD,CAAC,CAAC,OAAO,CAAC,SAAS,GAAG,SAAS;;;;"}