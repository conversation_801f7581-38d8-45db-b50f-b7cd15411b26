{"layers": [{"name": "مرز شهر سهند", "icon": "fas fa", "geojson": "./Data/sahand_limit.geojson", "visible": true, "config": {"title": "مرز سهند", "layerName": "شهر سهند", "hoverProperty": "osm_name", "sortProperty": "FID", "sortOrder": "asc"}, "properties": [{"value": "osm_name", "label": "مرز", "table": {"visible": false, "sortable": true}, "filter": {"type": "string"}, "info": false}]}, {"name": "مرز شهر اسکو", "icon": "fas fa", "geojson": "./Data/oskoo_limit.geojson", "visible": true, "config": {"title": "مرز اسکو", "layerName": "شهر اسکو", "hoverProperty": "osm_name", "sortProperty": "FID", "sortOrder": "asc"}, "properties": [{"value": "osm_name", "label": "مرز", "table": {"visible": false, "sortable": true}, "filter": {"type": "string"}, "info": false}]}, {"name": "محدوده پروژه", "icon": "fas fa", "geojson": "./Data/Boundary.geojson", "visible": true, "config": {"title": "مرز پروژه", "layerName": "محدوده پروژه", "hoverProperty": "osm_name", "sortProperty": "FID", "sortOrder": "asc"}, "properties": [{"value": "osm_name", "label": "محدوده پروژه", "table": {"visible": true, "sortable": true}, "filter": {"type": "string"}, "info": true}]}, {"name": "مس<PERSON><PERSON> ملی", "icon": "fas fa", "geojson": "./Data/MaskanMelli.geojson", "visible": true, "config": {"title": "للایه مسکن ملی", "layerName": "نقشه - مسکن ملی", "hoverProperty": "Descriptio", "label": {"enabled": false, "property": "Name", "className": "layer-label", "direction": "top", "offset": [10, -15], "color": "#0000FF"}, "sortProperty": "Name", "sortOrder": "asc"}, "properties": [{"value": "Name", "label": "<PERSON><PERSON> مسکن", "table": {"visible": true, "sortable": true}, "filter": {"type": "string"}, "info": true}, {"value": "Type", "label": "تیپ", "table": {"visible": true, "sortable": true}, "filter": {"type": "string", "input": "checkbox", "vertical": true, "multiple": true, "operators": ["in", "not_in", "equal", "not_equal"], "values": []}, "info": true}, {"value": "Year_", "label": "سال ساخت", "table": {"visible": true, "sortable": true}, "filter": {"type": "integer"}, "info": true}, {"value": "Descriptio", "label": "توضیحات", "table": {"visible": true, "sortable": true}, "filter": {"type": "string"}, "info": true}, {"value": "Company_Na", "label": "پیمانکار", "table": {"visible": true, "sortable": true}, "filter": false, "info": true}, {"value": "Physical_P", "label": "پیشرفت فیزیکی", "table": {"visible": true, "sortable": true}, "filter": false, "info": true}]}, {"name": "کاربری اراضی", "icon": "fas fa", "geojson": "./Data/Landuse.geojson", "visible": false, "config": {"title": "لایه کاربری اراضی", "layerName": "کاربری اراضی", "hoverProperty": "landuse", "sortProperty": "landuse", "sortOrder": "asc"}, "properties": [{"value": "landuse", "label": "نوع کاربری", "table": {"visible": false, "sortable": true}, "filter": {"type": "string"}, "info": true}]}, {"name": "لایه معابر", "icon": "fas fa", "geojson": "./Data/Roads.geojson", "visible": false, "config": {"title": "لایه معابر", "layerName": "لایه معابر", "hoverProperty": "osm_name", "sortProperty": "highway", "sortOrder": "desc"}, "properties": [{"value": "osm_name", "label": "نام خیابان", "table": {"visible": false, "sortable": true}, "filter": {"type": "string"}, "info": true}, {"value": "highway", "label": "نوع معبر", "table": {"visible": true, "sortable": true}, "filter": {"type": "string", "input": "checkbox", "vertical": true, "multiple": true, "operators": ["in", "not_in", "equal", "not_equal"], "values": []}}, {"value": "osm_oneway", "label": "یک طرفه", "table": {"visible": true, "sortable": true}, "filter": {"type": "string", "input": "checkbox", "vertical": true, "multiple": true, "operators": ["in", "not_in", "equal", "not_equal"], "values": []}}]}, {"name": "لایه بیلدینگ", "icon": "fas fa", "geojson": "./Data/Buildings.geojson", "visible": false, "config": {"title": "ساختمانها", "layerName": "لایه ساختمانها", "hoverProperty": "osm_name", "sortProperty": "FID", "sortOrder": "asc"}, "properties": [{"value": "osm_name", "label": "نام ساختمان", "table": {"visible": false, "sortable": true}, "filter": {"type": "string"}, "info": false}, {"value": "building", "label": "نوع ساختمان", "table": {"visible": true, "sortable": true}, "filter": {"type": "string", "input": "checkbox", "vertical": true, "multiple": true, "operators": ["in", "not_in", "equal", "not_equal"], "values": []}}, {"value": "osm_addr_6", "label": "آدرس", "table": {"visible": true, "sortable": true}, "filter": {"type": "string", "input": "checkbox", "vertical": true, "multiple": true, "operators": ["in", "not_in", "equal", "not_equal"], "values": []}}]}, {"name": "لایه نقاط شهری", "icon": "fas fa", "geojson": "./Data/Facilities.geojson", "visible": true, "config": {"title": "امکانات شهری", "layerName": "لایه امکانات", "hoverProperty": "osm_name", "sortProperty": "FID", "sortOrder": "asc"}, "properties": [{"value": "osm_name", "label": "نام نقطه", "table": {"visible": false, "sortable": true}, "filter": {"type": "string"}, "info": true}, {"value": "amenity", "label": "امکانات", "table": {"visible": true, "sortable": true}, "filter": {"type": "string", "input": "checkbox", "vertical": true, "multiple": true, "operators": ["in", "not_in", "equal", "not_equal"], "values": []}}, {"value": "shop", "label": "فروشگاه", "table": {"visible": true, "sortable": true}, "filter": {"type": "string", "input": "checkbox", "vertical": true, "multiple": true, "operators": ["in", "not_in", "equal", "not_equal"], "values": []}}]}, {"name": "دکل برق", "icon": "fas fa", "geojson": "./Data/DakalBargh.geojson", "visible": false, "config": {"title": "لایه دکل", "layerName": "دکل های برق", "hoverProperty": "power", "sortProperty": "FID", "sortOrder": "asc"}, "properties": [{"value": "power", "label": "دکل برق", "table": {"visible": false, "sortable": true}, "filter": {"type": "string"}, "info": false}, {"value": "FID", "label": "شماره دکل", "table": {"visible": true, "sortable": true}, "filter": {"type": "string", "input": "checkbox", "vertical": true, "multiple": true, "operators": ["in", "not_in", "equal", "not_equal"], "values": []}}]}, {"name": "<PERSON>ط نیرو", "icon": "fas fa", "geojson": "./Data/PowerLine.geojson", "visible": false, "config": {"title": "لایه خط نیرو", "layerName": "لایه خط نیرو", "hoverProperty": "power", "sortProperty": "FID", "sortOrder": "asc"}, "properties": [{"value": "power", "label": "<PERSON>ط نیرو", "table": {"visible": false, "sortable": true}, "filter": {"type": "string"}, "info": false}, {"value": "FID", "label": "شماره خط نیرو", "table": {"visible": true, "sortable": true}, "filter": {"type": "string", "input": "checkbox", "vertical": true, "multiple": true, "operators": ["in", "not_in", "equal", "not_equal"], "values": []}}]}, {"name": "ایستگاه فوق توزیع", "icon": "fas fa", "geojson": "./Data/SubStation.geojson", "visible": false, "config": {"title": "لایه ایستگاه برق", "layerName": "ایستگاه برق", "hoverProperty": "power", "sortProperty": "FID", "sortOrder": "asc"}, "properties": [{"value": "power", "label": "ایستگاه برق", "table": {"visible": false, "sortable": true}, "filter": {"type": "string"}, "info": false}, {"value": "FID", "label": "شماره ایستگاه نیرو", "table": {"visible": true, "sortable": true}, "filter": {"type": "string", "input": "checkbox", "vertical": true, "multiple": true, "operators": ["in", "not_in", "equal", "not_equal"], "values": []}}]}]}