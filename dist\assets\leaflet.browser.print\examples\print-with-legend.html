<!DOCTYPE html>
<html>
	<head>

		<title>Leaflet browser print plugin| Print with Legend</title>
		<meta charset="utf-8" />

		<meta name="viewport" content="width=device-width, initial-scale=1.0">
		<link rel="stylesheet" href="https://unpkg.com/leaflet@latest/dist/leaflet.css"/>
		<script src="https://unpkg.com/leaflet@latest/dist/leaflet.js"></script>
		<script src="../dist/leaflet.browser.print.js"></script>
		<script type="text/javascript" src="data/us-states.js"></script>

		<style>
			html, body, #map { width: 100%; height: 100%; margin: 0px; padding: 0px;}
		</style>

		<style>
			.info { padding: 6px 8px; font: 14px/16px Arial, Helvetica, sans-serif; background: white; background: rgba(255,255,255,0.8); box-shadow: 0 0 15px rgba(0,0,0,0.2); border-radius: 5px; } .info h4 { margin: 0 0 5px; color: #777; }
			.legend { text-align: left; line-height: 18px; color: #555; }
			.legend > * {
				display: flex;
				margin-bottom: 5px;
			}
			.legend i { width: 18px; height: 18px; float: left; margin-right: 8px; opacity: 0.7; }
		</style>
	</head>
	<body>

		<div id='map'></div>

		<script type="text/javascript">

			var map = L.map('map').setView([37.8, -96], 4);
		</script>

		<script type="text/javascript" src="savePNGMap.js"></script>
		<script type="text/javascript">
			L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
				attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
			}).addTo(map);

		    L.control.browserPrint().addTo(map);

			map.on("browser-print-start", function(e){
				/*on print start we already have a print map and we can create new control and add it to the print map to be able to print custom information */
				L.legendControl({position: 'bottomright'}).addTo(e.printMap);
			});

		</script>

	</body>
</html>
