/*! Version: 0.83.1
Copyright (c) 2016 Dominik <PERSON>ritz */
!function(t,i){"object"==typeof exports&&"undefined"!=typeof module?i(exports,require("leaflet")):"function"==typeof define&&define.amd?define(["exports","leaflet"],i):i(((t="undefined"!=typeof globalThis?globalThis:t||self).L=t.L||{},t.L.Control=t.L.Control||{},t.L.Control.Locate={}),t.L)}(this,function(t,l){"use strict";const i=(i,t)=>{t.split(" ").forEach(t=>{i.classList.add(t)})},s=(i,t)=>{t.split(" ").forEach(t=>{i.classList.remove(t)})};var o=l.Marker.extend({initialize(t,i){l.setOptions(this,i),this._latlng=t,this.createIcon()},createIcon(){var t=this.options;let i="";void 0!==t.color&&(i+=`stroke:${t.color};`),void 0!==t.weight&&(i+=`stroke-width:${t.weight};`),void 0!==t.fillColor&&(i+=`fill:${t.fillColor};`),void 0!==t.fillOpacity&&(i+=`fill-opacity:${t.fillOpacity};`),void 0!==t.opacity&&(i+=`opacity:${t.opacity};`);t=this._getIconSVG(t,i);this._locationIcon=l.divIcon({className:t.className,html:t.svg,iconSize:[t.w,t.h]}),this.setIcon(this._locationIcon)},_getIconSVG(t,i){var s=t.radius,t=s+t.weight,o=2*t;return{className:"leaflet-control-locate-location",svg:`<svg xmlns="http://www.w3.org/2000/svg" width="${o}" height="${o}" version="1.1" viewBox="-${t} -${t} ${o} ${o}">`+'<circle r="'+s+'" style="'+i+'" /></svg>',w:o,h:o}},setStyle(t){l.setOptions(this,t),this.createIcon()}}),e=o.extend({initialize(t,i,s){l.setOptions(this,s),this._latlng=t,this._heading=i,this.createIcon()},setHeading(t){this._heading=t},_getIconSVG(t,i){var s=t.radius,o=t.width+t.weight,s=2*(s+t.depth+t.weight),t=`M0,0 l${t.width/2},${t.depth} l-${o},0 z`;return{className:"leaflet-control-locate-heading",svg:`<svg xmlns="http://www.w3.org/2000/svg" width="${o}" height="${s}" version="1.1" viewBox="-${o/2} 0 ${o} ${s}" style="${`transform: rotate(${this._heading}deg)`}">`+'<path d="'+t+'" style="'+i+'" /></svg>',w:o,h:s}}});const n=l.Control.extend({options:{position:"topleft",layer:void 0,setView:"untilPanOrZoom",keepCurrentZoomLevel:!1,initialZoomLevel:!1,getLocationBounds(t){return t.bounds},flyTo:!1,clickBehavior:{inView:"stop",outOfView:"setView",inViewNotFollowing:"inView"},returnToPrevBounds:!1,cacheLocation:!0,drawCircle:!0,drawMarker:!0,showCompass:!0,markerClass:o,compassClass:e,circleStyle:{className:"leaflet-control-locate-circle",color:"#136AEC",fillColor:"#136AEC",fillOpacity:.15,weight:0},markerStyle:{className:"leaflet-control-locate-marker",color:"#fff",fillColor:"#2A93EE",fillOpacity:1,weight:3,opacity:1,radius:9},compassStyle:{fillColor:"#2A93EE",fillOpacity:1,weight:0,color:"#fff",opacity:1,radius:9,width:9,depth:6},followCircleStyle:{},followMarkerStyle:{},followCompassStyle:{},icon:"leaflet-control-locate-location-arrow",iconLoading:"leaflet-control-locate-spinner",iconElementTag:"span",textElementTag:"small",circlePadding:[0,0],metric:!0,createButtonCallback(t,i){var t=l.DomUtil.create("a","leaflet-bar-part leaflet-bar-part-single",t),s=(t.title=i.strings.title,t.href="#",t.setAttribute("role","button"),l.DomUtil.create(i.iconElementTag,i.icon,t));return void 0!==i.strings.text&&(l.DomUtil.create(i.textElementTag,"leaflet-locate-text",t).textContent=i.strings.text,t.classList.add("leaflet-locate-text-active"),t.parentNode.style.display="flex",0<i.icon.length)&&s.classList.add("leaflet-locate-icon"),{link:t,icon:s}},onLocationError(t,i){alert(t.message)},onLocationOutsideMapBounds(t){t.stop(),alert(t.options.strings.outsideMapBoundsMsg)},showPopup:!0,strings:{title:"موقعیت من",metersUnit:"meters",feetUnit:"feet",popup:"You are within {distance} {unit} from this point",outsideMapBoundsMsg:"You seem located outside the boundaries of the map"},locateOptions:{maxZoom:1/0,watch:!0,setView:!1}},initialize(t){for(const i in t)"object"==typeof this.options[i]?l.extend(this.options[i],t[i]):this.options[i]=t[i];this.options.followMarkerStyle=l.extend({},this.options.markerStyle,this.options.followMarkerStyle),this.options.followCircleStyle=l.extend({},this.options.circleStyle,this.options.followCircleStyle),this.options.followCompassStyle=l.extend({},this.options.compassStyle,this.options.followCompassStyle)},onAdd(t){var i=l.DomUtil.create("div","leaflet-control-locate leaflet-bar leaflet-control"),t=(this._container=i,this._map=t,this._layer=this.options.layer||new l.LayerGroup,this._layer.addTo(t),this._event=void 0,this._compassHeading=null,this._prevBounds=null,this.options.createButtonCallback(i,this.options));return this._link=t.link,this._icon=t.icon,l.DomEvent.on(this._link,"click",function(t){l.DomEvent.stopPropagation(t),l.DomEvent.preventDefault(t),this._onClick()},this).on(this._link,"dblclick",l.DomEvent.stopPropagation),this._resetVariables(),this._map.on("unload",this._unload,this),i},_onClick(){this._justClicked=!0;var i=this._isFollowing();if(this._userPanned=!1,this._userZoomed=!1,this._active&&!this._event)this.stop();else if(this._active){var s=this.options.clickBehavior;let t=s.outOfView;switch(t=s[t=this._map.getBounds().contains(this._event.latlng)?i?s.inView:s.inViewNotFollowing:t]?s[t]:t){case"setView":this.setView();break;case"stop":this.stop(),this.options.returnToPrevBounds&&(this.options.flyTo?this._map.flyToBounds:this._map.fitBounds).bind(this._map)(this._prevBounds)}}else this.options.returnToPrevBounds&&(this._prevBounds=this._map.getBounds()),this.start();this._updateContainerStyle()},start(){this._activate(),this._event&&(this._drawMarker(this._map),this.options.setView)&&this.setView(),this._updateContainerStyle()},stop(){this._deactivate(),this._cleanClasses(),this._resetVariables(),this._removeMarker()},stopFollowing(){this._userPanned=!0,this._updateContainerStyle(),this._drawMarker()},_activate(){if(!this._active&&this._map&&(this._map.locate(this.options.locateOptions),this._map.fire("locateactivate",this),this._active=!0,this._map.on("locationfound",this._onLocationFound,this),this._map.on("locationerror",this._onLocationError,this),this._map.on("dragstart",this._onDrag,this),this._map.on("zoomstart",this._onZoom,this),this._map.on("zoomend",this._onZoomEnd,this),this.options.showCompass)){const t="ondeviceorientationabsolute"in window;if(t||"ondeviceorientation"in window){const i=this,s=function(){l.DomEvent.on(window,t?"deviceorientationabsolute":"deviceorientation",i._onDeviceOrientation,i)};DeviceOrientationEvent&&"function"==typeof DeviceOrientationEvent.requestPermission?DeviceOrientationEvent.requestPermission().then(function(t){"granted"===t&&s()}):s()}}},_deactivate(){this._active&&this._map&&(this._map.stopLocate(),this._map.fire("locatedeactivate",this),this._active=!1,this.options.cacheLocation||(this._event=void 0),this._map.off("locationfound",this._onLocationFound,this),this._map.off("locationerror",this._onLocationError,this),this._map.off("dragstart",this._onDrag,this),this._map.off("zoomstart",this._onZoom,this),this._map.off("zoomend",this._onZoomEnd,this),this.options.showCompass)&&(this._compassHeading=null,"ondeviceorientationabsolute"in window?l.DomEvent.off(window,"deviceorientationabsolute",this._onDeviceOrientation,this):"ondeviceorientation"in window&&l.DomEvent.off(window,"deviceorientation",this._onDeviceOrientation,this))},setView(){var t;this._drawMarker(),this._isOutsideMapBounds()?(this._event=void 0,this.options.onLocationOutsideMapBounds(this)):this._justClicked&&!1!==this.options.initialZoomLevel?(this.options.flyTo?this._map.flyTo:this._map.setView).bind(this._map)([this._event.latitude,this._event.longitude],this.options.initialZoomLevel):this.options.keepCurrentZoomLevel?(this.options.flyTo?this._map.flyTo:this._map.panTo).bind(this._map)([this._event.latitude,this._event.longitude]):(t=this.options.flyTo?this._map.flyToBounds:this._map.fitBounds,this._ignoreEvent=!0,t.bind(this._map)(this.options.getLocationBounds(this._event),{padding:this.options.circlePadding,maxZoom:this.options.initialZoomLevel||this.options.locateOptions.maxZoom}),l.Util.requestAnimFrame(function(){this._ignoreEvent=!1},this))},_drawCompass(){var t,i;this._event&&(t=this._event.latlng,this.options.showCompass&&t&&null!==this._compassHeading&&(i=this._isFollowing()?this.options.followCompassStyle:this.options.compassStyle,this._compass?(this._compass.setLatLng(t),this._compass.setHeading(this._compassHeading),this._compass.setStyle&&this._compass.setStyle(i)):this._compass=new this.options.compassClass(t,this._compassHeading,i).addTo(this._layer)),!this._compass||this.options.showCompass&&null!==this._compassHeading||(this._compass.removeFrom(this._layer),this._compass=null))},_drawMarker(){void 0===this._event.accuracy&&(this._event.accuracy=0);var t,i=this._event.accuracy,s=this._event.latlng;this.options.drawCircle&&(t=this._isFollowing()?this.options.followCircleStyle:this.options.circleStyle,this._circle?this._circle.setLatLng(s).setRadius(i).setStyle(t):this._circle=l.circle(s,i,t).addTo(this._layer));let o,e;e=this.options.metric?(o=i.toFixed(0),this.options.strings.metersUnit):(o=(3.2808399*i).toFixed(0),this.options.strings.feetUnit),this.options.drawMarker&&(t=this._isFollowing()?this.options.followMarkerStyle:this.options.markerStyle,this._marker?(this._marker.setLatLng(s),this._marker.setStyle&&this._marker.setStyle(t)):this._marker=new this.options.markerClass(s,t).addTo(this._layer)),this._drawCompass();const n=this.options.strings.popup;function a(){return"string"==typeof n?l.Util.template(n,{distance:o,unit:e}):"function"==typeof n?n({distance:o,unit:e}):n}this.options.showPopup&&n&&this._marker&&this._marker.bindPopup(a())._popup.setLatLng(s),this.options.showPopup&&n&&this._compass&&this._compass.bindPopup(a())._popup.setLatLng(s)},_removeMarker(){this._layer.clearLayers(),this._marker=void 0,this._circle=void 0},_unload(){this.stop(),this._map&&this._map.off("unload",this._unload,this)},_setCompassHeading(t){!isNaN(parseFloat(t))&&isFinite(t)?(t=Math.round(t),this._compassHeading=t,l.Util.requestAnimFrame(this._drawCompass,this)):this._compassHeading=null},_onCompassNeedsCalibration(){this._setCompassHeading()},_onDeviceOrientation(t){this._active&&(t.webkitCompassHeading?this._setCompassHeading(t.webkitCompassHeading):t.absolute&&t.alpha&&this._setCompassHeading(360-t.alpha))},_onLocationError(t){3==t.code&&this.options.locateOptions.watch||(this.stop(),this.options.onLocationError(t,this))},_onLocationFound(t){if((!this._event||this._event.latlng.lat!==t.latlng.lat||this._event.latlng.lng!==t.latlng.lng||this._event.accuracy!==t.accuracy)&&this._active){switch(this._event=t,this._drawMarker(),this._updateContainerStyle(),this.options.setView){case"once":this._justClicked&&this.setView();break;case"untilPan":this._userPanned||this.setView();break;case"untilPanOrZoom":this._userPanned||this._userZoomed||this.setView();break;case"always":this.setView()}this._justClicked=!1}},_onDrag(){this._event&&!this._ignoreEvent&&(this._userPanned=!0,this._updateContainerStyle(),this._drawMarker())},_onZoom(){this._event&&!this._ignoreEvent&&(this._userZoomed=!0,this._updateContainerStyle(),this._drawMarker())},_onZoomEnd(){this._event&&this._drawCompass(),this._event&&!this._ignoreEvent&&this._marker&&!this._map.getBounds().pad(-.3).contains(this._marker.getLatLng())&&(this._userPanned=!0,this._updateContainerStyle(),this._drawMarker())},_isFollowing(){return!!this._active&&("always"===this.options.setView||("untilPan"===this.options.setView?!this._userPanned:"untilPanOrZoom"===this.options.setView?!this._userPanned&&!this._userZoomed:void 0))},_isOutsideMapBounds(){return void 0!==this._event&&this._map.options.maxBounds&&!this._map.options.maxBounds.contains(this._event.latlng)},_updateContainerStyle(){this._container&&(this._active&&!this._event?this._setClasses("requesting"):this._isFollowing()?this._setClasses("following"):this._active?this._setClasses("active"):this._cleanClasses())},_setClasses(t){"requesting"==t?(s(this._container,"active following"),i(this._container,"requesting"),s(this._icon,this.options.icon),i(this._icon,this.options.iconLoading)):"active"==t?(s(this._container,"requesting following"),i(this._container,"active"),s(this._icon,this.options.iconLoading),i(this._icon,this.options.icon)):"following"==t&&(s(this._container,"requesting"),i(this._container,"active following"),s(this._icon,this.options.iconLoading),i(this._icon,this.options.icon))},_cleanClasses(){l.DomUtil.removeClass(this._container,"requesting"),l.DomUtil.removeClass(this._container,"active"),l.DomUtil.removeClass(this._container,"following"),s(this._icon,this.options.iconLoading),i(this._icon,this.options.icon)},_resetVariables(){this._active=!1,this._justClicked=!1,this._userPanned=!1,this._userZoomed=!1}});t.CompassMarker=e,t.LocateControl=n,t.LocationMarker=o,t.locate=function(t){return new n(t)},Object.defineProperty(t,"__esModule",{value:!0})}),"undefined"!=typeof window&&window.L&&(window.L.control=window.L.control||{},window.L.control.locate=window.L.Control.Locate.locate);
//# sourceMappingURL=L.Control.Locate.min.js.map