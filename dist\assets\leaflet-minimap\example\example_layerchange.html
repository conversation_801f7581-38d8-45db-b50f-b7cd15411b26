<!DOCTYPE html>
<html>
<head>
	<title>MiniMap Demo</title>
	<meta charset="utf-8" />

	<link rel="stylesheet" href="./fullscreen.css" />

	<!-- Leaflet -->
	<link rel="stylesheet" href="https://unpkg.com/leaflet@1.0.1/dist/leaflet.css" />
	<script src="https://unpkg.com/leaflet@1.0.1/dist/leaflet.js" type="text/javascript"></script>

	<!-- Leaflet Plugins -->
	<link rel="stylesheet" href="../src/Control.MiniMap.css" />
	<script src="../src/Control.MiniMap.js" type="text/javascript"></script>

</head>
<body>
		<div id="map" ></div>

	<script type="text/javascript">
		
		var map = new L.Map('map');
		var osmUrl='http://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png';
		var osmAttrib='Map data &copy; OpenStreetMap contributors';
		
		var cartoUrl='http://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}.png';
		var cartoAttrib='Map data &copy; OpenStreetMap contributors';
	
		var baseLayers = {
           OSM:  L.tileLayer(osmUrl, {minZoom: 5, maxZoom: 18, attribution: osmAttrib}),
           DarkMap: L.tileLayer(cartoUrl, {minZoom: 5, maxZoom: 18, attribution: cartoAttrib})
       };
        var baseLayersCopy = {
           OSM:  L.tileLayer(osmUrl, {minZoom: 0, maxZoom: 13, attribution: osmAttrib}),
           DarkMap: L.tileLayer(cartoUrl, {minZoom: 0, maxZoom: 13, attribution: cartoAttrib})
       };	   

		map.addLayer(baseLayers.OSM);
		map.setView(new L.LatLng(59.92448055859924, 10.758276373601069),10);
		L.control.layers(baseLayers).addTo(map);
		
		//Plugin magic goes here! Note that you cannot use the same layer object again, as that will confuse the two map controls
		var miniMap = new L.Control.MiniMap(baseLayersCopy.OSM, { toggleDisplay: true }).addTo(map);
		
		map.on('baselayerchange', function (e) {
            miniMap.changeLayer(baseLayersCopy[e.name]);
       })
	   
	   
	</script>
</body>
</html>
