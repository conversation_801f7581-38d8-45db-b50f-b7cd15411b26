{"name": "@turf/helpers", "version": "5.1.5", "description": "turf helpers module", "main": "main.js", "module": "main.es.js", "types": "index.d.ts", "files": ["index.js", "index.d.ts", "main.js", "lib", "main.es.js"], "scripts": {"pretest": "rollup -c ../../rollup.config.js", "test": "node -r @std/esm test.js", "posttest": "node -r @std/esm ../../scripts/validate-es5-dependencies.js", "bench": "node -r @std/esm bench.js", "docs": "node ../../scripts/generate-readmes"}, "repository": {"type": "git", "url": "git://github.com/Turfjs/turf.git"}, "keywords": ["geo", "point", "turf", "g<PERSON><PERSON><PERSON>"], "author": "Turf Authors", "contributors": ["<PERSON> <@tmcw>", "<PERSON> <@stebogit>", "<PERSON> <@DenisCarriere>", "<PERSON> <@wnordmann>"], "license": "MIT", "bugs": {"url": "https://github.com/Turfjs/turf/issues"}, "homepage": "https://github.com/Turfjs/turf", "devDependencies": {"@std/esm": "*", "benchmark": "*", "rollup": "*", "tape": "*"}, "@std/esm": {"esm": "js", "cjs": true}}