!function(f){typeof module!='undefined'&&typeof exports=='object'?module.exports=f():typeof define!='undefined'&&define.amd?define(['fflate',f]):(typeof self!='undefined'?self:this).fflate=f()}(function(){var _e={};"use strict";var t=(typeof module!='undefined'&&typeof exports=='object'?function(_f){"use strict";var e;var r=";var __w=eval('require')('worker_threads');__w.parentPort.on('message',function(m){onmessage({data:m})}),postMessage=function(m,t){__w.parentPort.postMessage(m,t)},close=process.exit;self=global";try{e=require("worker_threads").Worker}catch(e){}_f.default=e?function(t,n,o,s,a){var u=!1,i=new e(t+r,{eval:!0}).on("error",(function(e){return a(e,null)})).on("message",(function(e){return a(null,e)})).on("exit",(function(e){e&&!u&&a(Error("exited with code "+e),null)}));return i.postMessage(o,s),i.terminate=function(){return u=!0,e.prototype.terminate.call(i)},i}:function(e,r,t,n,o){setImmediate((function(){return o(Error("async operations unsupported - update to Node 12+ (or Node 10-11 with the --experimental-worker CLI flag)"),null)}));var s=function(){};return{terminate:s,postMessage:s}};return _f}:function(_f){"use strict";var e={};_f.default=function(r,t,n,o,s){var u=e[t]||(e[t]=URL.createObjectURL(new Blob([r],{type:"text/javascript"}))),a=new Worker(u);return a.onerror=function(e){return s(e.error,null)},a.onmessage=function(e){return s(null,e.data)},a.postMessage(n,o),a};return _f})({}),n=Uint8Array,r=Uint16Array,e=Uint32Array,o=new n([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0]),i=new n([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0]),a=new n([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),s=function(t,n){for(var o=new r(31),i=0;i<31;++i)o[i]=n+=1<<t[i-1];var a=new e(o[30]);for(i=1;i<30;++i)for(var s=o[i];s<o[i+1];++s)a[s]=s-o[i]<<5|i;return[o,a]},f=s(o,2),u=f[0],l=f[1];u[28]=258,l[258]=28;for(var c=s(i,0),h=c[0],p=c[1],v=new r(32768),d=0;d<32768;++d){var g=(43690&d)>>>1|(21845&d)<<1;v[d]=((65280&(g=(61680&(g=(52428&g)>>>2|(13107&g)<<2))>>>4|(3855&g)<<4))>>>8|(255&g)<<8)>>>1}var y=function(t,n,e){for(var o=t.length,i=0,a=new r(n);i<o;++i)++a[t[i]-1];var s,f=new r(n);for(i=0;i<n;++i)f[i]=f[i-1]+a[i-1]<<1;if(e){s=new r(1<<n);var u=15-n;for(i=0;i<o;++i)if(t[i])for(var l=i<<4|t[i],c=n-t[i],h=f[t[i]-1]++<<c,p=h|(1<<c)-1;h<=p;++h)s[v[h]>>>u]=l}else for(s=new r(o),i=0;i<o;++i)s[i]=v[f[t[i]-1]++]>>>15-t[i];return s},w=new n(288);for(d=0;d<144;++d)w[d]=8;for(d=144;d<256;++d)w[d]=9;for(d=256;d<280;++d)w[d]=7;for(d=280;d<288;++d)w[d]=8;var x=new n(32);for(d=0;d<32;++d)x[d]=5;var m=y(w,9,0),b=y(w,9,1),z=y(x,5,0),A=y(x,5,1),S=function(t){for(var n=t[0],r=1;r<t.length;++r)t[r]>n&&(n=t[r]);return n},k=function(t,n,r){var e=n/8>>0;return(t[e]|t[e+1]<<8)>>>(7&n)&r},M=function(t,n){var r=n/8>>0;return(t[r]|t[r+1]<<8|t[r+2]<<16)>>>(7&n)},C=function(t){return(t/8>>0)+(7&t&&1)},D=function(t,o,i){(null==o||o<0)&&(o=0),(null==i||i>t.length)&&(i=t.length);var a=new(t instanceof r?r:t instanceof e?e:n)(i-o);return a.set(t.subarray(o,i)),a},G=function(t,r,e){var s=t.length,f=!r||e,l=!e||e.i;e||(e={}),r||(r=new n(3*s));var c=function(t){var e=r.length;if(t>e){var o=new n(Math.max(2*e,t));o.set(r),r=o}},p=e.f||0,v=e.p||0,d=e.b||0,g=e.l,w=e.d,x=e.m,m=e.n,z=8*s;do{if(!g){e.f=p=k(t,v,1);var G=k(t,v+1,3);if(v+=3,!G){var U=t[(Y=C(v)+4)-4]|t[Y-3]<<8,O=Y+U;if(O>s){if(l)throw"unexpected EOF";break}f&&c(d+U),r.set(t.subarray(Y,O),d),e.b=d+=U,e.p=v=8*O;continue}if(1==G)g=b,w=A,x=9,m=5;else{if(2!=G)throw"invalid block type";var I=k(t,v,31)+257,F=k(t,v+10,15)+4,Z=I+k(t,v+5,31)+1;v+=14;for(var E=new n(Z),T=new n(19),_=0;_<F;++_)T[a[_]]=k(t,v+3*_,7);v+=3*F;var j=S(T),q=(1<<j)-1;if(!l&&v+Z*(j+7)>z)break;var H=y(T,j,1);for(_=0;_<Z;){var Y,B=H[k(t,v,q)];if(v+=15&B,(Y=B>>>4)<16)E[_++]=Y;else{var J=0,K=0;for(16==Y?(K=3+k(t,v,3),v+=2,J=E[_-1]):17==Y?(K=3+k(t,v,7),v+=3):18==Y&&(K=11+k(t,v,127),v+=7);K--;)E[_++]=J}}var L=E.subarray(0,I),N=E.subarray(I);x=S(L),m=S(N),g=y(L,x,1),w=y(N,m,1)}if(v>z)throw"unexpected EOF"}f&&c(d+131072);for(var P=(1<<x)-1,Q=(1<<m)-1,R=x+m+18;l||v+R<z;){var V=(J=g[M(t,v)&P])>>>4;if((v+=15&J)>z)throw"unexpected EOF";if(!J)throw"invalid length/literal";if(V<256)r[d++]=V;else{if(256==V){g=null;break}var W=V-254;V>264&&(W=k(t,v,(1<<(tt=o[_=V-257]))-1)+u[_],v+=tt);var X=w[M(t,v)&Q],$=X>>>4;if(!X)throw"invalid distance";if(v+=15&X,N=h[$],$>3){var tt=i[$];N+=M(t,v)&(1<<tt)-1,v+=tt}if(v>z)throw"unexpected EOF";f&&c(d+131072);for(var nt=d+W;d<nt;d+=4)r[d]=r[d-N],r[d+1]=r[d+1-N],r[d+2]=r[d+2-N],r[d+3]=r[d+3-N];d=nt}}e.l=g,e.p=v,e.b=d,g&&(p=1,e.m=x,e.d=w,e.n=m)}while(!p);return d==r.length?r:D(r,0,d)},U=function(t,n,r){var e=n/8>>0;t[e]|=r<<=7&n,t[e+1]|=r>>>8},O=function(t,n,r){var e=n/8>>0;t[e]|=r<<=7&n,t[e+1]|=r>>>8,t[e+2]|=r>>>16},I=function(t,e){for(var o=[],i=0;i<t.length;++i)t[i]&&o.push({s:i,f:t[i]});var a=o.length,s=o.slice();if(!a)return[new n(0),0];if(1==a){var f=new n(o[0].s+1);return f[o[0].s]=1,[f,1]}o.sort((function(t,n){return t.f-n.f})),o.push({s:-1,f:25001});var u=o[0],l=o[1],c=0,h=1,p=2;for(o[0]={s:-1,f:u.f+l.f,l:u,r:l};h!=a-1;)u=o[o[c].f<o[p].f?c++:p++],l=o[c!=h&&o[c].f<o[p].f?c++:p++],o[h++]={s:-1,f:u.f+l.f,l:u,r:l};var v=s[0].s;for(i=1;i<a;++i)s[i].s>v&&(v=s[i].s);var d=new r(v+1),g=F(o[h-1],d,0);if(g>e){i=0;var y=0,w=g-e,x=1<<w;for(s.sort((function(t,n){return d[n.s]-d[t.s]||t.f-n.f}));i<a;++i){var m=s[i].s;if(!(d[m]>e))break;y+=x-(1<<g-d[m]),d[m]=e}for(y>>>=w;y>0;){var b=s[i].s;d[b]<e?y-=1<<e-d[b]++-1:++i}for(;i>=0&&y;--i){var z=s[i].s;d[z]==e&&(--d[z],++y)}g=e}return[new n(d),g]},F=function(t,n,r){return-1==t.s?Math.max(F(t.l,n,r+1),F(t.r,n,r+1)):n[t.s]=r},Z=function(t){for(var n=t.length;n&&!t[--n];);for(var e=new r(++n),o=0,i=t[0],a=1,s=function(t){e[o++]=t},f=1;f<=n;++f)if(t[f]==i&&f!=n)++a;else{if(!i&&a>2){for(;a>138;a-=138)s(32754);a>2&&(s(a>10?a-11<<5|28690:a-3<<5|12305),a=0)}else if(a>3){for(s(i),--a;a>6;a-=6)s(8304);a>2&&(s(a-3<<5|8208),a=0)}for(;a--;)s(i);a=1,i=t[f]}return[e.subarray(0,o),n]},E=function(t,n){for(var r=0,e=0;e<n.length;++e)r+=t[e]*n[e];return r},T=function(t,n,r){var e=r.length,o=C(n+2);t[o]=255&e,t[o+1]=e>>>8,t[o+2]=255^t[o],t[o+3]=255^t[o+1];for(var i=0;i<e;++i)t[o+i+4]=r[i];return 8*(o+4+e)},_=function(t,n,e,s,f,u,l,c,h,p,v){U(n,v++,e),++f[256];for(var d=I(f,15),g=d[0],b=d[1],A=I(u,15),S=A[0],k=A[1],M=Z(g),C=M[0],D=M[1],G=Z(S),F=G[0],_=G[1],j=new r(19),q=0;q<C.length;++q)j[31&C[q]]++;for(q=0;q<F.length;++q)j[31&F[q]]++;for(var H=I(j,7),Y=H[0],B=H[1],J=19;J>4&&!Y[a[J-1]];--J);var K,L,N,P,Q=p+5<<3,R=E(f,w)+E(u,x)+l,V=E(f,g)+E(u,S)+l+14+3*J+E(j,Y)+(2*j[16]+3*j[17]+7*j[18]);if(Q<=R&&Q<=V)return T(n,v,t.subarray(h,h+p));if(U(n,v,1+(V<R)),v+=2,V<R){K=y(g,b,0),L=g,N=y(S,k,0),P=S;var W=y(Y,B,0);for(U(n,v,D-257),U(n,v+5,_-1),U(n,v+10,J-4),v+=14,q=0;q<J;++q)U(n,v+3*q,Y[a[q]]);v+=3*J;for(var X=[C,F],$=0;$<2;++$){var tt=X[$];for(q=0;q<tt.length;++q)U(n,v,W[nt=31&tt[q]]),v+=Y[nt],nt>15&&(U(n,v,tt[q]>>>5&127),v+=tt[q]>>>12)}}else K=m,L=w,N=z,P=x;for(q=0;q<c;++q)if(s[q]>255){var nt;O(n,v,K[257+(nt=s[q]>>>18&31)]),v+=L[nt+257],nt>7&&(U(n,v,s[q]>>>23&31),v+=o[nt]);var rt=31&s[q];O(n,v,N[rt]),v+=P[rt],rt>3&&(O(n,v,s[q]>>>5&8191),v+=i[rt])}else O(n,v,K[s[q]]),v+=L[s[q]];return O(n,v,K[256]),v+L[256]},j=new e([65540,131080,131088,131104,262176,1048704,1048832,2114560,2117632]),q=new n(0),H=function(t,a,s,f,u,c){var h=t.length,v=new n(f+h+5*(1+Math.floor(h/7e3))+u),d=v.subarray(f,v.length-u),g=0;if(!a||h<8)for(var y=0;y<=h;y+=65535){var w=y+65535;w<h?g=T(d,g,t.subarray(y,w)):(d[y]=c,g=T(d,g,t.subarray(y,h)))}else{for(var x=j[a-1],m=x>>>13,b=8191&x,z=(1<<s)-1,A=new r(32768),S=new r(z+1),k=Math.ceil(s/3),M=2*k,G=function(n){return(t[n]^t[n+1]<<k^t[n+2]<<M)&z},U=new e(25e3),O=new r(288),I=new r(32),F=0,Z=0,E=(y=0,0),H=0,Y=0;y<h;++y){var B=G(y),J=32767&y,K=S[B];if(A[J]=K,S[B]=J,H<=y){var L=h-y;if((F>7e3||E>24576)&&L>423){g=_(t,d,0,U,O,I,Z,E,Y,y-Y,g),E=F=Z=0,Y=y;for(var N=0;N<286;++N)O[N]=0;for(N=0;N<30;++N)I[N]=0}var P=2,Q=0,R=b,V=J-K&32767;if(L>2&&B==G(y-V))for(var W=Math.min(m,L)-1,X=Math.min(32767,y),$=Math.min(258,L);V<=X&&--R&&J!=K;){if(t[y+P]==t[y+P-V]){for(var tt=0;tt<$&&t[y+tt]==t[y+tt-V];++tt);if(tt>P){if(P=tt,Q=V,tt>W)break;var nt=Math.min(V,tt-2),rt=0;for(N=0;N<nt;++N){var et=y-V+N+32768&32767,ot=et-A[et]+32768&32767;ot>rt&&(rt=ot,K=et)}}}V+=(J=K)-(K=A[J])+32768&32767}if(Q){U[E++]=268435456|l[P]<<18|p[Q];var it=31&l[P],at=31&p[Q];Z+=o[it]+i[at],++O[257+it],++I[at],H=y+P,++F}else U[E++]=t[y],++O[t[y]]}}g=_(t,d,c,U,O,I,Z,E,Y,y-Y,g),c||(g=T(d,g,q))}return D(v,0,f+C(g)+u)},Y=function(){for(var t=new e(256),n=0;n<256;++n){for(var r=n,o=9;--o;)r=(1&r&&3988292384)^r>>>1;t[n]=r}return t}(),B=function(){var t=4294967295;return{p:function(n){for(var r=t,e=0;e<n.length;++e)r=Y[255&r^n[e]]^r>>>8;t=r},d:function(){return 4294967295^t}}},J=function(){var t=1,n=0;return{p:function(r){for(var e=t,o=n,i=r.length,a=0;a!=i;){for(var s=Math.min(a+5552,i);a<s;++a)o+=e+=r[a];e%=65521,o%=65521}t=e,n=o},d:function(){return(t>>>8<<16|(255&n)<<8|n>>>8)+2*((255&t)<<23)}}},K=function(t,n,r,e,o){return H(t,null==n.level?6:n.level,null==n.mem?Math.ceil(1.5*Math.max(8,Math.min(13,Math.log(t.length)))):12+n.mem,r,e,!o)},L=function(t,n){var r={};for(var e in t)r[e]=t[e];for(var e in n)r[e]=n[e];return r},N=function(t,n,r){for(var e=t(),o=""+t,i=o.slice(o.indexOf("[")+1,o.lastIndexOf("]")).replace(/ /g,"").split(","),a=0;a<e.length;++a){var s=e[a],f=i[a];if("function"==typeof s){n+=";"+f+"=";var u=""+s;if(s.prototype)if(-1!=u.indexOf("[native code]")){var l=u.indexOf(" ",8)+1;n+=u.slice(l,u.indexOf("(",l))}else for(var c in n+=u,s.prototype)n+=";"+f+".prototype."+c+"="+s.prototype[c];else n+=u}else r[f]=s}return[n,r]},P=[],Q=function(t){var o=[];for(var i in t)(t[i]instanceof n||t[i]instanceof r||t[i]instanceof e)&&o.push((t[i]=new t[i].constructor(t[i])).buffer);return o},R=function(n,r,e,o){var i;if(!P[e]){for(var a="",s={},f=n.length-1,u=0;u<f;++u)a=(i=N(n[u],a,s))[0],s=i[1];P[e]=N(n[f],a,s)}var l=L({},P[e][1]);return t.default(P[e][0]+";onmessage=function(e){for(var k in e.data)self[k]=e.data[k];onmessage="+r+"}",e,l,Q(l),o)},V=function(){return[n,r,e,o,i,a,u,h,b,A,v,y,S,k,M,C,D,G,St,rt,et]},W=function(){return[n,r,e,o,i,a,l,p,m,w,z,x,v,j,q,y,U,O,I,F,Z,E,T,_,C,D,H,K,mt,rt]},X=function(){return[lt,pt,ut,B,Y]},$=function(){return[ct,ht]},tt=function(){return[vt,ut,J]},nt=function(){return[dt]},rt=function(t){return postMessage(t,[t.buffer])},et=function(t){return t&&t.size&&new n(t.size)},ot=function(t,r,e,o,i,a){var s=R(e,o,i,(function(t,n){s.terminate(),a(t,n)}));return r.consume||(t=new n(t)),s.postMessage([t,r],[t.buffer]),function(){s.terminate()}},it=function(t){return t.ondata=function(t,n){return postMessage([t,n],[t.buffer])},function(n){return t.push(n.data[0],n.data[1])}},at=function(t,n,r,e,o){var i,a=R(t,e,o,(function(t,r){t?(a.terminate(),n.ondata.call(n,t)):(r[1]&&a.terminate(),n.ondata.call(n,t,r[0],r[1]))}));a.postMessage(r),n.push=function(t,r){if(i)throw"stream finished";if(!n.ondata)throw"no stream handler";a.postMessage([t,i=r],[t.buffer])},n.terminate=function(){a.terminate()}},st=function(t,n){return t[n]|t[n+1]<<8},ft=function(t,n){return(t[n]|t[n+1]<<8|t[n+2]<<16)+2*(t[n+3]<<23)},ut=function(t,n,r){for(;r;++n)t[n]=r,r>>>=8},lt=function(t,n){var r=n.filename;if(t[0]=31,t[1]=139,t[2]=8,t[8]=n.level<2?4:9==n.level?2:0,t[9]=3,0!=n.mtime&&ut(t,4,Math.floor(new Date(n.mtime||Date.now())/1e3)),r){t[3]=8;for(var e=0;e<=r.length;++e)t[e+10]=r.charCodeAt(e)}},ct=function(t){if(31!=t[0]||139!=t[1]||8!=t[2])throw"invalid gzip data";var n=t[3],r=10;4&n&&(r+=t[10]|2+(t[11]<<8));for(var e=(n>>3&1)+(n>>4&1);e>0;e-=!t[r++]);return r+(2&n)},ht=function(t){var n=t.length;return(t[n-4]|t[n-3]<<8|t[n-2]<<16)+2*(t[n-1]<<23)},pt=function(t){return 10+(t.filename&&t.filename.length+1||0)},vt=function(t,n){var r=n.level,e=0==r?0:r<6?1:9==r?3:2;t[0]=120,t[1]=e<<6|(e?32-2*e:1)},dt=function(t){if(8!=(15&t[0])||t[0]>>>4>7||(t[0]<<8|t[1])%31)throw"invalid zlib data";if(32&t[1])throw"invalid zlib data: preset dictionaries not supported"};function gt(t,n){return n||"function"!=typeof t||(n=t,t={}),this.ondata=n,t}var yt=function(){function t(t,n){n||"function"!=typeof t||(n=t,t={}),this.ondata=n,this.o=t||{}}return t.prototype.p=function(t,n){this.ondata(K(t,this.o,0,0,!n),n)},t.prototype.push=function(t,n){if(this.d)throw"stream finished";if(!this.ondata)throw"no stream handler";this.d=n,this.p(t,n||!1)},t}();_e.Deflate=yt;var wt=function(t,n){at([W,function(){return[it,yt]}],this,gt.call(this,t,n),(function(t){var n=new yt(t.data);onmessage=it(n)}),6)};function xt(t,n,r){if(r||(r=n,n={}),"function"!=typeof r)throw"no callback";return ot(t,n,[W],(function(t){return rt(mt(t.data[0],t.data[1]))}),0,r)}function mt(t,n){return void 0===n&&(n={}),K(t,n,0,0)}_e.AsyncDeflate=wt,_e.deflate=xt,_e.deflateSync=mt;var bt=function(){function t(t){this.s={},this.p=new n(0),this.ondata=t}return t.prototype.e=function(t){if(this.d)throw"stream finished";if(!this.ondata)throw"no stream handler";var r=this.p.length,e=new n(r+t.length);e.set(this.p),e.set(t,r),this.p=e},t.prototype.c=function(t){this.d=this.s.i=t||!1;var n=this.s.b,r=G(this.p,this.o,this.s);this.ondata(D(r,n,this.s.b),this.d),this.o=D(r,this.s.b-32768),this.s.b=this.o.length,this.p=D(this.p,this.s.p/8>>0),this.s.p&=7},t.prototype.push=function(t,n){this.e(t),this.c(n)},t}();_e.Inflate=bt;var zt=function(t){this.ondata=t,at([V,function(){return[it,bt]}],this,0,(function(){var t=new bt;onmessage=it(t)}),7)};function At(t,n,r){if(r||(r=n,n={}),"function"!=typeof r)throw"no callback";return ot(t,n,[V],(function(t){return rt(St(t.data[0],et(t.data[1])))}),1,r)}function St(t,n){return G(t,n)}_e.AsyncInflate=zt,_e.inflate=At,_e.inflateSync=St;var kt=function(){function t(t,n){this.c=B(),this.l=0,this.v=1,yt.call(this,t,n)}return t.prototype.push=function(t,n){yt.prototype.push.call(this,t,n)},t.prototype.p=function(t,n){this.c.p(t),this.l+=t.length;var r=K(t,this.o,this.v&&pt(this.o),n&&8,!n);this.v&&(lt(r,this.o),this.v=0),n&&(ut(r,r.length-8,this.c.d()),ut(r,r.length-4,this.l)),this.ondata(r,n)},t}();_e.Gzip=kt,_e.Compress=kt;var Mt=function(t,n){at([W,X,function(){return[it,yt,kt]}],this,gt.call(this,t,n),(function(t){var n=new kt(t.data);onmessage=it(n)}),8)};function Ct(t,n,r){if(r||(r=n,n={}),"function"!=typeof r)throw"no callback";return ot(t,n,[W,X,function(){return[Dt]}],(function(t){return rt(Dt(t.data[0],t.data[1]))}),2,r)}function Dt(t,n){void 0===n&&(n={});var r=B(),e=t.length;r.p(t);var o=K(t,n,pt(n),8),i=o.length;return lt(o,n),ut(o,i-8,r.d()),ut(o,i-4,e),o}_e.AsyncGzip=Mt,_e.AsyncCompress=Mt,_e.gzip=Ct,_e.compress=Ct,_e.gzipSync=Dt,_e.compressSync=Dt;var Gt=function(){function t(t){this.v=1,bt.call(this,t)}return t.prototype.push=function(t,n){if(bt.prototype.e.call(this,t),this.v){var r=ct(this.p);if(r>=this.p.length&&!n)return;this.p=this.p.subarray(r),this.v=0}if(n){if(this.p.length<8)throw"invalid gzip stream";this.p=this.p.subarray(0,-8)}bt.prototype.c.call(this,n)},t}();_e.Gunzip=Gt;var Ut=function(t){this.ondata=t,at([V,$,function(){return[it,bt,Gt]}],this,0,(function(){var t=new Gt;onmessage=it(t)}),9)};function Ot(t,n,r){if(r||(r=n,n={}),"function"!=typeof r)throw"no callback";return ot(t,n,[V,$,function(){return[It]}],(function(t){return rt(It(t.data[0]))}),3,r)}function It(t,r){return G(t.subarray(ct(t),-8),r||new n(ht(t)))}_e.AsyncGunzip=Ut,_e.gunzip=Ot,_e.gunzipSync=It;var Ft=function(){function t(t,n){this.c=J(),this.v=1,yt.call(this,t,n)}return t.prototype.push=function(t,n){yt.prototype.push.call(this,t,n)},t.prototype.p=function(t,n){this.c.p(t);var r=K(t,this.o,this.v&&2,n&&4,!n);this.v&&(vt(r,this.o),this.v=0),n&&ut(r,r.length-4,this.c.d()),this.ondata(r,n)},t}();_e.Zlib=Ft;var Zt=function(t,n){at([W,tt,function(){return[it,yt,Ft]}],this,gt.call(this,t,n),(function(t){var n=new Ft(t.data);onmessage=it(n)}),10)};function Et(t,n,r){if(r||(r=n,n={}),"function"!=typeof r)throw"no callback";return ot(t,n,[W,tt,function(){return[Tt]}],(function(t){return rt(Tt(t.data[0],t.data[1]))}),4,r)}function Tt(t,n){void 0===n&&(n={});var r=J();r.p(t);var e=K(t,n,2,4);return vt(e,n),ut(e,e.length-4,r.d()),e}_e.AsyncZlib=Zt,_e.zlib=Et,_e.zlibSync=Tt;var _t=function(){function t(t){this.v=1,bt.call(this,t)}return t.prototype.push=function(t,n){if(bt.prototype.e.call(this,t),this.v){if(this.p.length<2&&!n)return;this.p=this.p.subarray(2),this.v=0}if(n){if(this.p.length<4)throw"invalid zlib stream";this.p=this.p.subarray(0,-4)}bt.prototype.c.call(this,n)},t}();_e.Unzlib=_t;var jt=function(t){this.ondata=t,at([V,nt,function(){return[it,bt,_t]}],this,0,(function(){var t=new _t;onmessage=it(t)}),11)};function qt(t,n,r){if(r||(r=n,n={}),"function"!=typeof r)throw"no callback";return ot(t,n,[V,nt,function(){return[Ht]}],(function(t){return rt(Ht(t.data[0],et(t.data[1])))}),5,r)}function Ht(t,n){return G((dt(t),t.subarray(2,-4)),n)}_e.AsyncUnzlib=jt,_e.unzlib=qt,_e.unzlibSync=Ht;var Yt=function(){function t(t){this.G=Gt,this.I=bt,this.Z=_t,this.ondata=t}return t.prototype.push=function(t,r){if(!this.ondata)throw"no stream handler";if(this.s)this.s.push(t,r);else{if(this.p&&this.p.length){var e=new n(this.p.length+t.length);e.set(this.p),e.set(t,this.p.length)}else this.p=t;if(this.p.length>2){var o=this,i=function(){o.ondata.apply(o,arguments)};this.s=31==this.p[0]&&139==this.p[1]&&8==this.p[2]?new this.G(i):8!=(15&this.p[0])||this.p[0]>>4>7||(this.p[0]<<8|this.p[1])%31?new this.I(i):new this.Z(i),this.s.push(this.p,r),this.p=null}}},t}();_e.Decompress=Yt;var Bt=function(){function t(t){this.G=Ut,this.I=zt,this.Z=jt,this.ondata=t}return t.prototype.push=function(t,n){Yt.prototype.push.call(this,t,n)},t}();function Jt(t,n,r){if(r||(r=n,n={}),"function"!=typeof r)throw"no callback";return 31==t[0]&&139==t[1]&&8==t[2]?Ot(t,n,r):8!=(15&t[0])||t[0]>>4>7||(t[0]<<8|t[1])%31?At(t,n,r):qt(t,n,r)}function Kt(t,n){return 31==t[0]&&139==t[1]&&8==t[2]?It(t,n):8!=(15&t[0])||t[0]>>4>7||(t[0]<<8|t[1])%31?St(t,n):Ht(t,n)}_e.AsyncDecompress=Bt,_e.decompress=Jt,_e.decompressSync=Kt;var Lt=function(t,r,e,o){for(var i in t){var a=t[i],s=r+i;a instanceof n?e[s]=[a,o]:Array.isArray(a)?e[s]=[a[0],L(o,a[1])]:Lt(a,s+"/",e,o)}};function Nt(t,r){var e=t.length;if(!r&&"undefined"!=typeof TextEncoder)return(new TextEncoder).encode(t);for(var o=new n(t.length+(t.length>>>1)),i=0,a=function(t){o[i++]=t},s=0;s<e;++s){if(i+5>o.length){var f=new n(i+8+(e-s<<1));f.set(o),o=f}var u=t.charCodeAt(s);u<128||r?a(u):u<2048?(a(192|u>>>6),a(128|63&u)):u>55295&&u<57344?(a(240|(u=65536+(1047552&u)|1023&t.charCodeAt(++s))>>>18),a(128|u>>>12&63),a(128|u>>>6&63),a(128|63&u)):(a(224|u>>>12),a(128|u>>>6&63),a(128|63&u))}return D(o,0,i)}function Pt(t,n){var r="";if(!n&&"undefined"!=typeof TextDecoder)return(new TextDecoder).decode(t);for(var e=0;e<t.length;){var o=t[e++];o<128||n?r+=String.fromCharCode(o):o<224?r+=String.fromCharCode((31&o)<<6|63&t[e++]):o<240?r+=String.fromCharCode((15&o)<<12|(63&t[e++])<<6|63&t[e++]):(o=((15&o)<<18|(63&t[e++])<<12|(63&t[e++])<<6|63&t[e++])-65536,r+=String.fromCharCode(55296|o>>10,56320|1023&o))}return r}_e.strToU8=Nt,_e.strFromU8=Pt;var Qt=function(t,n){return n+30+st(t,n+26)+st(t,n+28)},Rt=function(t,n,r){var e=st(t,n+28),o=Pt(t.subarray(n+46,n+46+e),!(2048&st(t,n+8))),i=n+46+e,a=r?Vt(t,i):[ft(t,n+20),ft(t,n+24),ft(t,n+42)],s=a[0],f=a[1],u=a[2];return[st(t,n+10),s,f,o,i+st(t,n+30)+st(t,n+32),u]},Vt=function(t,n){for(;1!=st(t,n);n+=4+st(t,n+2));return[ft(t,n+12),ft(t,n+4),ft(t,n+20)]},Wt=function(t,n,r,e,o,i,a,s,f,u){var l=i.length,c=e.length;ut(t,n,null!=f?33639248:67324752),n+=4,null!=f&&(t[n]=20,n+=2),t[n]=20,n+=2,t[n++]=8==u&&(1==s.level?6:s.level<6?4:9==s.level?2:0),t[n++]=a&&8,t[n]=u,n+=2;var h=new Date(s.mtime||Date.now()),p=h.getFullYear()-1980;if(p<0||p>119)throw"date not in range 1980-2099";ut(t,n,2*(p<<24)|h.getMonth()+1<<21|h.getDate()<<16|h.getHours()<<11|h.getMinutes()<<5|h.getSeconds()>>>1),ut(t,n+=4,r),ut(t,n+4,c),ut(t,n+8,o),ut(t,n+12,l),n+=16,null!=f&&(ut(t,n+=10,f),n+=4),t.set(i,n),n+=l,null==f&&t.set(e,n)},Xt=function(t,n,r,e,o){ut(t,n,101010256),ut(t,n+8,r),ut(t,n+10,r),ut(t,n+12,e),ut(t,n+16,o)};function $t(t,r,e){if(e||(e=r,r={}),"function"!=typeof e)throw"no callback";var o={};Lt(t,"",o,r);var i=Object.keys(o),a=i.length,s=0,f=0,u=a,l=Array(a),c=[],h=function(){for(var t=0;t<c.length;++t)c[t]()},p=function(){var t=new n(f+22),r=s,o=f-s;f=0;for(var i=0;i<u;++i){var a=l[i];try{Wt(t,f,a.c,a.d,a.m,a.n,a.u,a.p,null,a.t),Wt(t,s,a.c,a.d,a.m,a.n,a.u,a.p,f,a.t),s+=46+a.n.length,f+=30+a.n.length+a.d.length}catch(t){return e(t,null)}}Xt(t,s,l.length,o,r),e(null,t)};a||p();for(var v=function(t){var n=i[t],r=o[n],u=r[0],v=r[1],d=B(),g=u.length;d.p(u);var y=Nt(n),w=y.length,x=0==v.level?0:8,m=function(r,o){if(r)h(),e(r,null);else{var i=o.length;l[t]={t:x,d:o,m:g,c:d.d(),u:n.length!=i,n:y,p:v},s+=30+w+i,f+=76+2*w+i,--a||p()}};if(y.length>65535&&m("filename too long",null),x)if(g<16e4)try{m(null,mt(u,v))}catch(t){m(t,null)}else c.push(xt(u,v,m));else m(null,u)},d=0;d<u;++d)v(d);return h}function tn(t,r){void 0===r&&(r={});var e={},o=[];Lt(t,"",e,r);var i=0,a=0;for(var s in e){var f=e[s],u=f[0],l=f[1],c=0==l.level?0:8,h=Nt(s),p=h.length;if(h.length>65535)throw"filename too long";var v=c?mt(u,l):u,d=v.length,g=B();g.p(u),o.push({t:c,d:v,m:u.length,c:g.d(),u:s.length!=p,n:h,o:i,p:l}),i+=30+p+d,a+=76+2*p+d}for(var y=new n(a+22),w=i,x=a-i,m=0;m<o.length;++m){var b=o[m];Wt(y,b.o,b.c,b.d,b.m,b.n,b.u,b.p,null,b.t),Wt(y,i,b.c,b.d,b.m,b.n,b.u,b.p,b.o,b.t),i+=46+b.n.length}return Xt(y,i,o.length,x,w),y}function nn(t,r){if("function"!=typeof r)throw"no callback";for(var e=[],o=function(){for(var t=0;t<e.length;++t)e[t]()},i={},a=t.length-22;101010256!=ft(t,a);--a)if(!a||t.length-a>65558)return void r("invalid zip file",null);var s=st(t,a+8);s||r(null,{});var f=s,u=ft(t,a+16),l=4294967295==u;if(l){if(a=ft(t,a-12),101075792!=ft(t,a))throw"invalid zip file";f=s=ft(t,a+32),u=ft(t,a+48)}for(var c=function(a){var f=Rt(t,u,l),c=f[0],h=f[1],p=f[2],v=f[3],d=f[4],g=Qt(t,f[5]);u=d;var y=function(t,n){t?(o(),r(t,null)):(i[v]=n,--s||r(null,i))};if(c)if(8==c){var w=t.subarray(g,g+h);if(h<32e4)try{y(null,St(w,new n(p)))}catch(t){y(t,null)}else e.push(At(w,{size:p},y))}else y("unknown compression type "+c,null);else y(null,D(t,g,g+h))},h=0;h<f;++h)c();return o}function rn(t){for(var r={},e=t.length-22;101010256!=ft(t,e);--e)if(!e||t.length-e>65558)throw"invalid zip file";var o=st(t,e+8);if(!o)return{};var i=ft(t,e+16),a=4294967295==i;if(a){if(e=ft(t,e-12),101075792!=ft(t,e))throw"invalid zip file";o=ft(t,e+32),i=ft(t,e+48)}for(var s=0;s<o;++s){var f=Rt(t,i,a),u=f[0],l=f[1],c=f[2],h=f[3],p=f[4],v=Qt(t,f[5]);if(i=p,u){if(8!=u)throw"unknown compression type "+u;r[h]=St(t.subarray(v,v+l),new n(c))}else r[h]=D(t,v,v+l)}return r}_e.zip=$t,_e.zipSync=tn,_e.unzip=nn,_e.unzipSync=rn;return _e})