{"name": "bootstrap-table", "description": "An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)", "version": "1.20.2", "style": "dist/bootstrap-table.min.css", "sass": "src/bootstrap-table.scss", "main": "dist/bootstrap-table.min.js", "directories": {"doc": "site"}, "devDependencies": {"@babel/core": "^7.17.10", "@babel/preset-env": "^7.17.10", "@rollup/plugin-babel": "^5.3.1", "@rollup/plugin-commonjs": "^22.0.0", "@rollup/plugin-inject": "^4.0.4", "@rollup/plugin-multi-entry": "^4.1.0", "@rollup/plugin-node-resolve": "^13.3.0", "chalk": "^4.0.0", "clean-css-cli": "^5.6.0", "core-js": "^3.22.5", "cross-env": "^7.0.3", "cypress": "^9.6.1", "eslint": "^8.15.0", "esm": "^3.2.25", "foreach-cli": "^1.8.1", "glob": "^8.0.2", "headr": "^0.0.4", "node-sass": "^7.0.1", "npm-run-all": "^4.1.5", "rimraf": "^3.0.2", "rollup": "^2.72.1", "rollup-plugin-copy": "^3.4.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-vue": "5.1.9", "stylelint": "^14.8.2", "stylelint-config-standard": "^25.0.0", "vue-template-compiler": "^2.6.14"}, "scripts": {"lint:js": "eslint src", "lint:css": "stylelint src/**/*.scss", "lint": "run-s lint:*", "test": "cypress run --headless", "docs:check:api": "cd tools && node check-api.js", "docs:check:locale": "cd tools && node check-locale.js", "docs:check": "run-s docs:check:*", "docs:serve": "bundle exec jekyll serve", "docs": "bundle exec jekyll build", "js:build:base": "rollup -c", "js:build:min": "cross-env NODE_ENV=production rollup -c", "js:build:banner": "foreach -g \"dist/**/*.min.js\" -x \"headr #{path} -o #{path} --version --homepage --author --license\"", "js:build": "run-s js:build:*", "css:build:src": "node-sass --output-style expanded src -o src", "css:build:base": "node-sass --output-style expanded src -o dist", "css:build:min": "foreach -g \"dist/**/*.css\" -x \"cleancss #{path} -o #{dir}/#{name}.min.css\"", "css:build:banner": "foreach -g \"dist/**/*.min.css\" -x \"headr #{path} -o #{path} --version --homepage --author --license\"", "css:build": "run-s css:build:*", "clean": "<PERSON><PERSON><PERSON> dist", "build": "run-s lint clean *:build", "pre-commit": "run-s lint docs:check"}, "peerDependencies": {"jquery": "1.9.1 - 3"}, "repository": {"type": "git", "url": "https://github.com/wenzhixin/bootstrap-table.git"}, "keywords": ["bootstrap", "table", "pagination", "checkbox", "radio", "datatables", "css", "css-framework", "semantic", "semantic-ui", "bulma", "material", "material-design", "materialize", "foundation"], "author": "wenzhixin <<EMAIL>> (http://wenzhixin.net.cn/)", "license": "MIT", "bugs": {"url": "https://github.com/wenzhixin/bootstrap-table/issues"}, "homepage": "https://bootstrap-table.com", "types": "./index.d.ts"}