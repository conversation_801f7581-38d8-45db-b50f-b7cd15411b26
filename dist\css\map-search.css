/* استایل‌ها<PERSON> کنترل جستجو روی نقشه */
.map-search-control {
  position: absolute;
  top: 60px; /* زیر ناوبار */
  left: 10px; /* سمت چپ نقشه */
  background: white;
  border: 2px solid #ddd;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  padding: 15px;
  min-width: 280px;
  max-width: 320px;
  transition: all 0.3s ease;
}

.map-search-control:hover {
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.map-search-control .search-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.map-search-control .search-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin: 0;
}

.map-search-control .search-close {
  background: none;
  border: none;
  color: #999;
  font-size: 18px;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.map-search-control .search-close:hover {
  background: #f5f5f5;
  color: #666;
}

.map-search-control .layer-select-container {
  margin-bottom: 15px;
}

.map-search-control .layer-select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  background: white;
  color: #333;
}

.map-search-control .layer-select:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 7, 255, 0.1);
}

.map-search-control .search-input-container {
  position: relative;
}

.map-search-control .search-input {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  background: white;
  color: #333;
  transition: all 0.2s ease;
}

.map-search-control .search-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 7, 255, 0.1);
}

.map-search-control .search-input::placeholder {
  color: #999;
}

/* استایل‌های نتایج جستجو */
.map-search-control .search-results {
  margin-top: 10px;
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #eee;
  border-radius: 4px;
  background: white;
}

.map-search-control .search-result-item {
  padding: 8px 12px;
  border-bottom: 1px solid #f5f5f5;
  cursor: pointer;
  transition: background-color 0.2s ease;
  font-size: 13px;
  color: #333;
}

.map-search-control .search-result-item:hover {
  background-color: #f8f9fa;
}

.map-search-control .search-result-item:last-child {
  border-bottom: none;
}

/* ریسپانسیو */
@media (max-width: 768px) {
  .map-search-control {
    left: 5px;
    right: 5px;
    min-width: auto;
    max-width: none;
    top: 80px; /* پایین‌تر از تولبار در موبایل */
  }
}

@media (max-width: 480px) {
  .map-search-control {
    left: 2px;
    right: 2px;
    padding: 10px;
    min-width: auto;
    max-width: none;
  }
  
  .map-search-control .search-title {
    font-size: 14px;
  }
  
  .map-search-control .layer-select,
  .map-search-control .search-input {
    font-size: 13px;
    padding: 8px 10px;
  }
}

/* انیمیشن ورود */
.map-search-control.entering {
  opacity: 0;
  transform: translateX(-20px);
}

.map-search-control.entered {
  opacity: 1;
  transform: translateX(0);
}

/* حالت مخفی */
.map-search-control.hidden {
  display: none;
}
