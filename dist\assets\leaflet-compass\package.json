{"name": "leaflet-compass", "version": "1.5.6", "description": "A leaflet control plugin to make simple rotating compass", "repository": {"type": "git", "url": "**************:stefan<PERSON><PERSON><PERSON>/leaflet-compass.git"}, "homepage": "https://opengeo.tech/maps/leaflet-compass/", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://opengeo.tech/"}, "license": "MIT", "keywords": ["gis", "maps", "leaflet", "html5"], "main": "dist/leaflet-compass.src.js", "dependencies": {"leaflet": "*"}, "devDependencies": {"grunt": "~0.4.2", "grunt-cli": "~0.1.11", "grunt-contrib-clean": "^0.5.0", "grunt-contrib-concat": "^0.3.0", "grunt-contrib-cssmin": "^0.7.0", "grunt-contrib-jshint": "^0.7.2", "grunt-contrib-uglify": "^4.0.0", "grunt-contrib-watch": "^0.5.3", "grunt-remove-logging": "^0.2.0", "grunt-todos": "^0.2.0", "standard": "^17.0.0"}}