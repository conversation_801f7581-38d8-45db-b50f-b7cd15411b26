{"name": "d3-geo", "version": "1.12.1", "description": "Shapes and calculators for spherical coordinates.", "keywords": ["d3", "d3-module", "geo", "maps", "cartography"], "homepage": "https://d3js.org/d3-geo/", "license": "BSD-3-<PERSON><PERSON>", "author": {"name": "<PERSON>", "url": "https://bost.ocks.org/mike"}, "main": "dist/d3-geo.js", "unpkg": "dist/d3-geo.min.js", "jsdelivr": "dist/d3-geo.min.js", "module": "src/index.js", "repository": {"type": "git", "url": "https://github.com/d3/d3-geo.git"}, "files": ["dist/**/*.js", "src/**/*.js"], "scripts": {"pretest": "rollup -c", "test": "tape -r esm 'test/**/*-test.js' && eslint src", "prepublishOnly": "rm -rf dist && yarn test && mkdir -p test/output && test/compare-images", "postpublish": "git push && git push --tags && cd ../d3.github.com && git pull && cp ../${npm_package_name}/dist/${npm_package_name}.js ${npm_package_name}.v${npm_package_version%%.*}.js && cp ../${npm_package_name}/dist/${npm_package_name}.min.js ${npm_package_name}.v${npm_package_version%%.*}.min.js && git add ${npm_package_name}.v${npm_package_version%%.*}.js ${npm_package_name}.v${npm_package_version%%.*}.min.js && git commit -m \"${npm_package_name} ${npm_package_version}\" && git push && cd - && zip -j dist/${npm_package_name}.zip -- LICENSE README.md dist/${npm_package_name}.js dist/${npm_package_name}.min.js"}, "dependencies": {"d3-array": "1"}, "sideEffects": false, "devDependencies": {"canvas": "1", "d3-format": "1", "eslint": "6", "eslint-plugin-es5": "1", "esm": "3", "rollup": "1", "rollup-plugin-terser": "5", "tape": "4", "topojson-client": "3", "world-atlas": "1"}}