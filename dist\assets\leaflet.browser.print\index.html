<!DOCTYPE html>
<html>
<head>
  <title>Leaflet browser print plugin with custom print Layer and content</title>
  <meta charset="utf-8"/>

  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link rel="stylesheet" href="https://unpkg.com/leaflet@latest/dist/leaflet.css"/>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@geoman-io/leaflet-geoman-free@latest/dist/leaflet-geoman.css">
  <link rel="stylesheet" href="https://leaflet.github.io/Leaflet.markercluster/dist/MarkerCluster.css"/>
  <link rel="stylesheet" href="https://leaflet.github.io/Leaflet.markercluster/dist/MarkerCluster.Default.css"/>
  <style>
    html, body {
      width: 100%;
      height: 100%;
      margin: 0px;
      padding: 0px;
    }

    #map {
      width: 80%;
      margin: auto;
      height: 70%;
    }
  </style>
</head>

<body>
<h1 class="title" leaflet-browser-print-content>Leaflet Browser print TITLE</h1>
<h3 class="sub-content" leaflet-browser-print-content>Leaflet browser print plugin for map printing with additional
  controls and content</h3>
<div id="map"></div>
<table leaflet-browser-print-pages>
  <thead>
  <tr>
    <th>Option</th>
    <th>Type</th>
    <th>Default</th>
    <th>Description</th>
  </tr>
  </thead>
  <tbody>
  <tr>
    <td>title</td>
    <td>String</td>
    <td>'Print map'</td>
    <td>Sets the text which appears as the tooltip of the print button</td>
  </tr>
  <tr>
    <td>position</td>
    <td><a href="http://leafletjs.com/reference-0.7.7.html#control-position" rel="nofollow">Leaflet control position</a>
    </td>
    <td>'topleft'</td>
    <td>Position the print button</td>
  </tr>
  <tr>
    <td>printModes</td>
    <td>Array</td>
    <td>["Portrait", "Landscape", "Auto", "Custom"]</td>
    <td>Collection of page print actions</td>
  </tr>
  <tr>
    <td>printModesNames</td>
    <td>Object</td>
    <td>{ Portrait: "Portrait", Landscape: "Landscape", Auto:"Auto", Custom:"Custom" }</td>
    <td>Customize each print mode name</td>
  </tr>
  <tr>
    <td>printLayer</td>
    <td><a href="http://leafletjs.com/reference-0.7.7.html#tilelayer" rel="nofollow">Leaflet tile layer</a></td>
    <td>null</td>
    <td>A tiles layer to show instead of all current active tiles layers</td>
  </tr>
  <tr>
    <td>closePopupsOnPrint</td>
    <td>Boolean</td>
    <td>true</td>
    <td>Indicates if we need to force popup closing for printed map</td>
  </tr>
  <tr>
    <td>contentSelector</td>
    <td>String</td>
    <td>"[leaflet-browser-print-content]"</td>
    <td>Content selector for printed map, will select and dynamically inject content on printed maps. For full
      functionality please check "Printing additional content section"
    </td>
  </tr>
  </tbody>
</table>
<script src="https://unpkg.com/leaflet@latest/dist/leaflet.js"></script>
<script src="https://leaflet.github.io/Leaflet.markercluster/dist/leaflet.markercluster-src.js"></script>
<script src="dist/leaflet.browser.print.js"></script>
<script src="https://leaflet.github.io/Leaflet.markercluster/example/realworld.388.js">

</script>
<script src="https://cdn.jsdelivr.net/npm/@geoman-io/leaflet-geoman-free@latest/dist/leaflet-geoman.min.js"></script>
<script>
    var canvasRenderer = L.canvas();
    //Layers
    var cities = L.markerClusterGroup();
    var arrayOfPoints = [[39.61, -105.02], [39.73, -104.8], [39.77, -105.23]];

    L.marker(arrayOfPoints[0]).bindPopup('This is Littleton, CO.').addTo(cities);
    L.marker(arrayOfPoints[1]).bindPopup('This is Aurora, CO.').addTo(cities);
    L.marker(arrayOfPoints[2]).bindPopup('This is Golden, CO.').addTo(cities);

    var p = L.polygon(arrayOfPoints, {color: 'yellow'}).bindTooltip("polygon").bindPopup('This is Polygon, CO.').addTo(cities);

    var base = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
    });
    var wms = L.tileLayer.wms('https://ahocevar.com/geoserver/wms', {
        layers: 'topp:states',
        CQL_FILTER: 'PERSONS > 2000000',
        styles: '',
        format: 'image/png',
        transparent: true,
    });

    var map = L.map('map', {
        center: [39.73, -104.99],
        zoom: 10,
        layers: [base, cities]
    });

    var baseLayers = {
        "Origin": base,
        "WMS": wms
    };

    var overlays = {
        "Cities": cities
    };

    L.control.layers(baseLayers, overlays).addTo(map);


    L.control.scale({position: 'topleft'}).addTo(map);

    map.pm.addControls({
        position: 'bottomright',
        editControls: false
    });
    var drawnItems = new L.FeatureGroup().addTo(map);
    map.on("pm:create", function (e) {
        var type = e.shape,
            layer = e.layer;

        if (type === 'Marker') {
            layer.bindPopup('A popup!');
        }

        drawnItems.addLayer(layer);
    });


    map.createPane("new-pane");
    var l = L.layerGroup().addTo(map);
    var green = L.circle([39.73, -104.99], 5000, {
        color: 'green',
        fillColor: 'darkgreen',
        fillOpacity: 0.8,
        pane: "new-pane",
        renderer: canvasRenderer
    }).addTo(l);


    var customActionToPrint = function (context, mode) {
        return function () {
            window.alert("We are printing the MAP. Let's do Custom print here!");
            context._printCustom(mode);
        }
    };

    L.control.browserPrint({
        printLayer: L.tileLayer('https://stamen-tiles-{s}.a.ssl.fastly.net/watercolor/{z}/{x}/{y}.{ext}', {
            attribution: 'Map tiles by <a href="http://stamen.com">Stamen Design</a>, <a href="http://creativecommons.org/licenses/by/3.0">CC BY 3.0</a> &mdash; Map data &copy; <a href="http://www.openstreetmap.org/copyright">OpenStreetMap</a>',
            subdomains: 'abcd',
            minZoom: 1,
            maxZoom: 16,
            ext: 'png'
        }),
        closePopupsOnPrint: false,
        printModes: [
            L.BrowserPrint.Mode.Landscape("Tabloid",{title: "Tabloid VIEW"}),
            new L.BrowserPrint.Mode("Alert",{title:"User specified print action",pageSize: "A6", action: customActionToPrint, invalidateBounds: false}),
            L.BrowserPrint.Mode.Landscape(),
            "Portrait",
            L.BrowserPrint.Mode.Auto("B4",{title: "Auto"}),
            L.BrowserPrint.Mode.Custom("B5",{title:"Select area"})
        ]
    }).addTo(map);

    L.BrowserPrint.Utils.registerLayer(L.TileLayer.WMS, 'L.TileLayer.WMS', function (layer) {
        console.info("Printing WMS layer.");
        return L.tileLayer.wms(layer._url, layer.options);
    });

    L.popup({minWidth: 500}).setLatLng(L.latLng(39.73, -104.99)).setContent("Leaflet browser print plugin with custom print Layer and content").openOn(map);

    var markers = L.markerClusterGroup();

    for (var i = 0; i < addressPoints.length; i++) {
        var a = addressPoints[i];
        var title = a[2];
        var marker = L.marker(new L.LatLng(a[0], a[1]), {title: title});
        marker.bindPopup(title);
        markers.addLayer(marker);
    }
    map.addLayer(markers);

    // We are not cloning markercluster to preserve original clasterization behavior and prevent OutOfMemory problems
    L.BrowserPrint.Utils.registerLayer(L.MarkerClusterGroup, 'L.MarkerClusterGroup', function (layer, utils) {
        return layer;
    });

    // On print end we invalidate markercluster to update markers;
    map.on(L.BrowserPrint.Event.PrintEnd, function (e) {
        map.removeLayer(markers);
        map.addLayer(markers);
    });


</script>

<style>
  .grid-print-container {
    grid-template: auto 1fr auto / 1fr;
    background-color: orange;
  }

  .grid-map-print {
    grid-row: 2;
  }

  .grid-print-container > .title,
  .grid-print-container > .sub-content {
    color: white;
  }

  .title {
    grid-row: 1;
    justify-self: center;
    text-align: center;
    color: grey;
    box-sizing: border-box;
    margin-top: 0;
  }

  .sub-content {
    grid-row: 5;
    padding-left: 10px;
    text-align: center;
    color: grey;
    box-sizing: border-box;
  }
</style>
<style>
  [leaflet-browser-print-pages] {
    display: none;
  }

  .pages-print-container [leaflet-browser-print-pages] {
    display: block;
  }
</style>
</body>

</html>
