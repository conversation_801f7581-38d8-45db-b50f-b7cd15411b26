{"name": "leaflet-draw", "version": "1.0.4", "description": "Vector drawing plugin for Leaflet", "devDependencies": {"eslint": "^3.5.0 <3.6.0", "eslint-config-mourner": "^2.0.3", "git-rev": "^0.2.1", "happen": "^0.3.2", "jake": "^8.0.15", "karma": "^1.7.1", "karma-chrome-launcher": "^2.2.0", "karma-coverage": "~1.1.1", "karma-firefox-launcher": "~1.0.0", "karma-mocha": "^1.2.0", "karma-phantomjs-launcher": "^1.0.2", "karma-safari-launcher": "~1.0.0", "leafdoc": "^1.4.0", "leaflet": "^0.7.0", "mocha": "^3.5.3", "phantomjs-prebuilt": "^2.1.16", "prosthetic-hand": "^1.3.1", "source-map": "^0.5.7", "uglify-js": "^2.8.29", "jshint": "^2.9.6", "uglifycss": "0.0.25"}, "main": "dist/leaflet.draw.js", "style": "dist/leaflet.draw.css", "directories": {"doc": "docs/", "example": "docs/example", "lib": "src/"}, "files": ["dist/*", "docs/*"], "scripts": {"build": "jake build -f ./Jakefile.js", "test": "jake test", "docs": "jake docs", "release": "./build/publish.sh"}, "repository": {"type": "git", "url": "https://github.com/Leaflet/Leaflet.draw.git"}, "keywords": ["maps", "leaflet", "client", "vector", "drawing", "draw"], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}], "license": "MIT", "readmeFilename": "README.md", "eslintConfig": {"root": true, "globals": {"L": true}, "env": {"commonjs": true, "amd": true, "node": false}, "extends": "mourner", "rules": {"no-mixed-spaces-and-tabs": [2, "smart-tabs"], "indent": [2, "tab", {"VariableDeclarator": 0}], "curly": 2, "spaced-comment": 2, "strict": 0, "wrap-iife": 0, "key-spacing": 0, "consistent-return": 0}}}