{"name": "leaflet.browser.print", "version": "2.0.2", "keywords": ["leaflet.js", "browser", "print", "portrait", "landscape", "custom selection print tool"], "description": "A leaflet plugin which allows users to print the map directly from the browser", "homepage": "https://github.com/<PERSON>-<PERSON><PERSON><PERSON>/leaflet.browser.print", "bugs": "https://github.com/<PERSON>-<PERSON><PERSON><PERSON>/leaflet.browser.print/issues", "repository": {"url": "https://github.com/<PERSON>-<PERSON><PERSON><PERSON>/leaflet.browser.print", "type": "git"}, "author": "<PERSON> <<EMAIL>> (https://github.com/<PERSON><PERSON>/)", "license": "MIT (http://www.opensource.org/licenses/mit-license.php)", "scripts": {"build": "npx webpack", "watch": "npx webpack -w"}, "devDependencies": {"leaflet": "^1.7.1", "webpack": "^4.42.1", "webpack-cli": "^3.3.11"}}