html,body {
	margin:0;
	padding:0;
}

h3 {
	font-size:.85em;
}
h3,h4 {
	white-space:nowrap;
	float:left;
	margin:2px;
	padding:0;
}
#copy {
	right:-4px;
}
#map {
	position:absolute;
	top:22px;
	left:0;
	right:0;
	bottom:0;
	border:none;
	border-top:1px solid #1978cf;
	height:auto;
	width:auto;
}
#post-it {
	position:absolute;
	right:5px;
	top:40px;
	height:90px;
	width:90px;
	font-size:.65em;
}

/********** Leaflet Customizations for **********/

/* CONTROLS */
.leaflet-bottom .leaflet-control,
.leaflet-left .leaflet-control {
	box-shadow: 0 0 8px rgba(0,0,0,0.4);
	border: 2px solid #1978cf;
	border-radius:6px;
}
.leaflet-left.leaflet-top .leaflet-control,
.leaflet-touch .leaflet-left .leaflet-control {
	margin-left: 8px;
	margin-top: 8px;
	margin-bottom: 0;
}
.leaflet-left.leaflet-bottom .leaflet-control {
	margin-left: 8px;
	margin-bottom: 8px;
	margin-top: 0;
}

/* ZOOM */
.leaflet-control-zoom,
.leaflet-touch .leaflet-control-zoom {
	border-radius:4px;
}
.leaflet-control-zoom a,
.leaflet-touch .leaflet-control-zoom a,
.leaflet-bar a,
.leaflet-touch .leaflet-bar a {
	background-color: rgba(255, 255, 255, 0.8);	
	width: 32px;
	height: 28px;
}
.leaflet-touch .leaflet-control-zoom-out {
	line-height: 24px;	
}
.leaflet-control-zoom a:hover,
.leaflet-touch .leaflet-control-zoom a:hover,
.leaflet-bar a:hover,
.leaflet-touch .leaflet-bar a:hover {
	background-color: #fff;
	width: 32px;
	height: 28px;	
}

.leaflet-touch .leaflet-bar a:first-child {
	-webkit-border-top-left-radius: 4px;
	        border-top-left-radius: 4px;
	-webkit-border-top-right-radius: 4px;
	        border-top-right-radius: 4px;
}
.leaflet-touch .leaflet-bar a:last-child {
	-webkit-border-bottom-left-radius: 4px;
	        border-bottom-left-radius: 4px;
	-webkit-border-bottom-right-radius: 4px;
	        border-bottom-right-radius: 4px;
	border-bottom: none;
}

/* ATTRIBUTION*/
.leaflet-control.leaflet-control-attribution {
	border:none;
	padding:0 4px 2px 4px;
	margin:0 -4px -4px 0;
}

