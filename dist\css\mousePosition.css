@font-face {
  font-family: Nahid;
  src: url('nahid-font-v0.3.0/Farsi-Digits-Without-Latin/Nahid-FD-WOL.eot');
  src: url('nahid-font-v0.3.0/Farsi-Digits-Without-Latin/Nahid-FD-WOL.eot?#iefix') format('embedded-opentype'),
       url('nahid-font-v0.3.0/Farsi-Digits-Without-Latin/Nahid-FD-WOL.woff') format('woff2'),
       url('nahid-font-v0.3.0/Farsi-Digits-Without-Latin/Nahid-FD-WOL.woff') format('woff'),
       url('nahid-font-v0.3.0/Farsi-Digits-Without-Latin/Nahid-FD-WOL.ttf') format('truetype');
  font-weight: normal;
}


.leaflet-container .leaflet-control-mouseposition {
  background-color: rgba(255, 255, 255, 0.85); /* پس‌زمینه کمی شفاف‌تر برای کنتراست بهتر */
  box-shadow: 0 0 5px #888; /* سایه تیره‌تر برای وضوح بیشتر */
  padding: 2px 8px; /* کمی بیشتر برای خوانایی بهتر */
  margin: 0;
  color: #444343; /* متن تیره‌تر */
  font: bold 16px/2 "Nahid", Arial, Helvetica, sans-serif; /* ضخیم‌تر و کمی بزرگ‌تر */
  border-radius: 4px; /* کمی گردی برای زیباتر شدن */
}

/* .leaflet-container .leaflet-control-mouseposition {
  background-color: rgba(255, 255, 255, 0.8);
  box-shadow: 0 0 5px #bbb;
  padding: 5px;
  color: #333;
  font: 14px/1.5 "Nahid", Arial, Helvetica, sans-serif;
  white-space: nowrap;
  direction: ltr;
} */

/* مخفی کردن مختصات UTM در سایز موبایل */
@media (max-width: 768px) {
  .leaflet-container .leaflet-control-mouseposition {
    /* مخفی کردن کامل در موبایل */
    display: none !important;
  }
}

/* مخفی کردن مختصات UTM در سایز تبلت */
@media (max-width: 1024px) and (min-width: 769px) {
  .leaflet-container .leaflet-control-mouseposition {
    /* نمایش فقط مختصات جغرافیایی بدون UTM */
    font-size: 14px !important;
    padding: 2px 6px !important;
  }
  
  /* مخفی کردن بخش UTM در تبلت */
  .leaflet-container .leaflet-control-mouseposition::after {
    content: "" !important;
  }
}
