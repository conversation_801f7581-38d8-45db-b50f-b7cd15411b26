/* Variables and Mixins */
/* Generic L.Toolbar */
.leaflet-toolbar-0 {
  list-style: none;
  padding-left: 0;
  border: 2px solid rgba(0, 0, 0, 0.2);
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
}
.leaflet-toolbar-0 > li {
  position: relative;
}
.leaflet-toolbar-0 > li > .leaflet-toolbar-icon {
  display: block;
  width: 30px;
  height: 30px;
  line-height: 30px;
  margin-right: 0;
  padding-right: 0;
  border-right: 0;
  text-align: center;
  text-decoration: none;
  background-color: #ffffff;
}
.leaflet-toolbar-0 > li > .leaflet-toolbar-icon:hover {
  background-color: #f4f4f4;
}
.leaflet-toolbar-0 .leaflet-toolbar-1 {
  display: none;
  list-style: none;
}
.leaflet-toolbar-tip-container {
  margin: 0 auto;
  margin-top: -16px;
  height: 16px;
  position: relative;
  overflow: hidden;
}
.leaflet-toolbar-tip {
  width: 16px;
  height: 16px;
  margin: -8px auto 0;
  background-color: #ffffff;
  border: 2px solid rgba(0, 0, 0, 0.2);
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
  background-clip: content-box;
  -webkit-transform: rotate(45deg);
      -ms-transform: rotate(45deg);
          transform: rotate(45deg);
}
/* L.Toolbar.Control */
.leaflet-control-toolbar {
  /* Secondary Toolbar */
}
.leaflet-control-toolbar > li > .leaflet-toolbar-icon {
  border-bottom: 1px solid #ccc;
}
.leaflet-control-toolbar > li:first-child > .leaflet-toolbar-icon {
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
}
.leaflet-control-toolbar > li:last-child > .leaflet-toolbar-icon {
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
  border-bottom-width: 0;
}
.leaflet-control-toolbar .leaflet-toolbar-1 {
  margin: 0;
  padding: 0;
  position: absolute;
  left: 30px;
  /* leaflet-draw-toolbar.left + leaflet-draw-toolbar.width */
  top: 0;
  white-space: nowrap;
  height: 30px;
}
.leaflet-control-toolbar .leaflet-toolbar-1 > li {
  display: inline-block;
}
.leaflet-control-toolbar .leaflet-toolbar-1 > li > .leaflet-toolbar-icon {
  display: block;
  background-color: #919187;
  border-left: 1px solid #aaa;
  color: #fff;
  font: 11px/19px "Helvetica Neue", Arial, Helvetica, sans-serif;
  line-height: 30px;
  text-decoration: none;
  padding-left: 10px;
  padding-right: 10px;
  height: 30px;
}
.leaflet-control-toolbar .leaflet-toolbar-1 > li > .leaflet-toolbar-icon:hover {
  background-color: #a0a098;
}
.leaflet-control-toolbar .leaflet-toolbar-1 > li:last-child > .leaflet-toolbar-icon {
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}
/* L.Toolbar.Popup */
.leaflet-popup-toolbar {
  position: relative;
  box-sizing: content-box;
}
.leaflet-popup-toolbar > li {
  float: left;
}
.leaflet-popup-toolbar > li > .leaflet-toolbar-icon {
  border-right: 1px solid #ccc;
}
.leaflet-popup-toolbar > li:first-child > .leaflet-toolbar-icon {
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
}
.leaflet-popup-toolbar > li:last-child > .leaflet-toolbar-icon {
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
  border-bottom-width: 0;
  border-right: none;
}
.leaflet-popup-toolbar .leaflet-toolbar-1 {
  position: absolute;
  top: 30px;
  left: 0;
  padding-left: 0;
}
.leaflet-popup-toolbar .leaflet-toolbar-1 > li > .leaflet-toolbar-icon {
  position: relative;
  float: left;
  width: 30px;
  height: 30px;
}
