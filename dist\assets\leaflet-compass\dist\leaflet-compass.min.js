/* 
 * Leaflet Control Compass v1.5.6 - 2022-12-13 
 * 
 * Copyright 2014 <PERSON> 
 * <EMAIL> 
 * https://opengeo.tech/ 
 * 
 * Licensed under the MIT license. 
 * 
 * Demos: 
 * https://opengeo.tech/maps/leaflet-compass/ 
 * 
 * Source: 
 * **************:ste<PERSON><PERSON><PERSON><PERSON>/leaflet-compass.git 
 * 
 */
!function(t){if("function"==typeof define&&define.amd)define(["leaflet"],t);else if("undefined"!=typeof module)module.exports=t(require("leaflet"));else{if(void 0===window.L)throw"Leaflet must be loaded first";t(window.L)}}(function(e){return e.Control.Compass=e.Control.extend({includes:"1"==e.version[0]?e.Evented.prototype:e.Mixin.Events,options:{position:"topright",autoActive:!1,showDigit:!1,textErr:"",callErr:null,angleOffset:2},initialize:function(t){t&&t.style&&(t.style=e.Util.extend({},this.options.style,t.style)),e.Util.setOptions(this,t),this._errorFunc=this.options.callErr||this.showAlert,this._isActive=!1,this._currentAngle=null},onAdd:function(t){var i=this,t=(this._map=t,e.DomUtil.create("div","leaflet-compass"));return this._button=e.DomUtil.create("span","compass-button",t),this._button.href="#",this._icon=e.DomUtil.create("div","compass-icon",this._button),this._digit=e.DomUtil.create("span","compass-digit",this._button),this._alert=e.DomUtil.create("div","compass-alert",t),this._alert.style.display="none",e.DomEvent.on(this._button,"click",e.DomEvent.stop,this).on(this._button,"click",this._switchCompass,this),e.DomEvent.on(window,"compassneedscalibration",function(t){i.showAlert("Your compass needs calibrating! Wave your device in a figure-eight motion")},this),this.options.autoActive&&this.activate(!0),t},onRemove:function(t){this.deactivate(),e.DomEvent.off(this._button,"click",e.DomEvent.stop,this).off(this._button,"click",this._switchCompass,this)},_switchCompass:function(){this._isActive?this.deactivate():this.activate()},_rotateHandler:function(t){var i;if(!this._isActive)return!1;t.webkitCompassHeading?(i=360-t.webkitCompassHeading,this._compassIphone=!0):t.alpha?(i=t.alpha-180,this._compassAndroid=!0):this._errorCompass({message:"Orientation angle not found"}),(i=Math.round(i))%this.options.angleOffset==0&&this.setAngle(i)},_errorCompass:function(t){this.deactivate(),this._errorFunc.call(this,this.options.textErr||t.message)},_rotateElement:function(t){var i=this._currentAngle;t.style.webkitTransform="rotate("+i+"deg)",t.style.MozTransform="rotate("+i+"deg)",t.style.transform="rotate("+i+"deg)"},setAngle:function(t){this.options.showDigit&&!isNaN(parseFloat(t))&&isFinite(t)&&(this._digit.innerHTML=-t+"°"),this._currentAngle=t,this._rotateElement(this._icon),this.fire("compass:rotated",{angle:t})},getAngle:function(){return this._currentAngle},_activate:function(){this._isActive=!0,e.DomEvent.on(window,"deviceorientation",this._rotateHandler,this),e.DomUtil.addClass(this._button,"active")},activate:function(i){var e;"undefined"!=typeof DeviceOrientationEvent&&"function"==typeof DeviceOrientationEvent.requestPermission?(e=this,DeviceOrientationEvent.requestPermission().then(function(t){"granted"===t?e._activate():!0!==i&&alert("Cannot activate compass: permission "+t)},function(t){!0!==i&&alert("Error activating compass: "+t)})):this._activate()},deactivate:function(){this.setAngle(0),this._isActive=!1,e.DomEvent.off(window,"deviceorientation",this._rotateHandler,this),e.DomUtil.removeClass(this._button,"active"),this.fire("compass:disabled")},showAlert:function(t){this._alert.style.display="block",this._alert.innerHTML=t;var i=this;clearTimeout(this.timerAlert),this.timerAlert=setTimeout(function(){i._alert.style.display="none"},5e3)}}),e.control.compass=function(t){return new e.Control.Compass(t)},e.Control.Compass});