<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"> 
<html xmlns="http://www.w3.org/1999/xhtml"> 
<head> 
<title></title> 
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" /> 
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.3.1/dist/leaflet.css" />
<link rel="stylesheet" href="../src/leaflet-search.css" />
<link rel="stylesheet" href="style.css" />
</head>

<body>
<h3><a href="../"><big>◄</big> Leaflet.Control.Search</a></h3>

<h4>Custom Filter Example: <em>search markers with <b>Fuzzy Search</b>, using <a href="https://github.com/krisk/fuse" target="_blank">Fuse.js</a> by Kirollos Risk</em></h4>
<div id="map"></div>

<div id="post-it">
Search restaurants in Rome, data by <a href="http://wiki.openstreetmap.org/wiki/Overpass_API">OSM Overpass</a><br />
Example: <i>pizza</i>, <i>vege</i>, <i>japa</i>, <i>giappo</i>, <i>cucina romana</i>, <i>chine</i> ...
</div>


<script src="https://unpkg.com/leaflet@1.3.1/dist/leaflet.js"></script>
<script src="../src/leaflet-search.js"></script>

<script src="//cdnjs.cloudflare.com/ajax/libs/fuse.js/1.2.2/fuse.min.js"></script>

<script src="data/restaurant500.geojson.js"></script>
<script>

	var map = L.map('map', {
			zoom: 12,
			center: new L.latLng(41.8990, 12.4977),
			layers: L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png')
		});
	
	var restLayers = L.geoJson(restaurant500, {
			pointToLayer: function(feature, latlng) {

				var props = L.Util.extend({
							'name': '',
							'cuisine': '',
							'website': '',
							'phone': ''
						},feature.properties),
					textPopup = L.Util.template("<h3>{name}</h3>{cuisine}<br>{website}<br>{phone}", props),
					style = {
						radius: 5,
						opacity: 0.8,
						fillColor: '#ddd',
						fillOpacity: 0.8
					};

				return L.circleMarker(latlng, style).bindPopup(textPopup);
			}
		}).addTo(map);

	var fuse = new Fuse(restaurant500.features, {
		keys: [
			'properties.name',
			'properties.description',
			'properties.cuisine',
			'properties.note',
			//'properties.operator'
		]
	});

	L.control.search({
		layer: restLayers,
		propertyName: 'name',
		filterData: function(text, records) {
			var jsons = fuse.search(text),
				ret = {}, key;
			
			for(var i in jsons) {
				key = jsons[i].properties.name;
				ret[ key ]= records[key];
			}

			console.log(jsons,ret);
			return ret;
		}
	})
	.on('search:locationfound', function(e) {
		e.layer.openPopup();
	})
	.addTo(map);


</script>

<div id="copy"><a href="https://opengeo.tech/">Website</a> &bull; <a rel="author" href="https://opengeo.tech/stefano-cudini/">Stefano Cudini</a></div>



</body>
</html>
