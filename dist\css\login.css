/* Importing fonts from Google */
/* @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800;900&display=swap'); */

/* Reseting */
* {
   margin: 0;
   padding: 0;
   box-sizing: border-box;
   font-family: 'Poppins', sans-serif;
}

/* اضافه کردن عکس به عنوان پس‌زمینه */
body {
   background-image: url('/src//images/background.jpg'); /* مسیر عکس خود را اینجا وارد کنید */
   background-size: cover; /* عکس تمام صفحه را پوشش می‌دهد */
   background-position: center; /* عکس در مرکز صفحه قرار می‌گیرد */
   background-repeat: no-repeat; /* عکس تکرار نمی‌شود */
   background-attachment: fixed; /* عکس ثابت می‌ماند */
}

.wrapper {
   max-width: 350px;
   min-height: 500px;
   margin: 80px auto;
   padding: 40px 30px 30px 30px;
   border: 2px solid #cac5c5; /* حاشیه خاکستری با ضخامت ۲ پیکسل */

   background-color: rgba(236, 240, 243, 0.9); /* پس‌زمینه فرم با شفافیت */
   border-radius: 15px;
   box-shadow: 13px 13px 20px rgba(203, 206, 209, 0.5), -13px -13px 20px rgba(255, 255, 255, 0.5);
}

.logo {
   width: 80px;
   margin: auto;
}

.logo img {
   width: 100%;
   height: 80px;
   object-fit: cover;
   border-radius: 50%;
   box-shadow: 0px 0px 3px #5f5f5f,
       0px 0px 0px 5px #ecf0f3,
       8px 8px 15px #a7aaa7,
       -8px -8px 15px #fff;
}

.wrapper .name {
   font-weight: 600;
   font-size: 1.4rem;
   letter-spacing: 1.3px;
   padding-left: 10px;
   color: #555;
}

.wrapper .form-field input {
   width: 100%;
   display: block;
   border: none;
   outline: none;
   background: none;
   font-size: 1.2rem;
   color: #666;
   padding: 10px 15px 10px 10px;
   /* border: 1px solid red; */
}

.wrapper .form-field {
   padding-left: 10px;
   margin-bottom: 20px;
   border-radius: 20px;
   box-shadow: inset 8px 8px 8px #cbced1, inset -8px -8px 8px #fff;
}

.wrapper .form-field .fas {
   color: #555;
}

.wrapper .btn {
   box-shadow: none;
   width: 100%;
   height: 40px;
   background-color: #03A9F4;
   color: #fff;
   border-radius: 25px;
   box-shadow: 3px 3px 3px #b1b1b1,
       -3px -3px 3px #fff;
   letter-spacing: 1.3px;
}

.wrapper .btn:hover {
   background-color: #039BE5;
}

.wrapper a {
   text-decoration: none;
   font-size: 0.8rem;
   color: #03A9F4;
}

.wrapper a:hover {
   color: #039BE5;
}

@media(max-width: 380px) {
   .wrapper {
       margin: 30px 20px;
       padding: 40px 15px 15px 15px;
   }
}

/* اضافه کردن فاصله بین دکمه‌ها */
.btn-google {
   margin-top: 10px; /* فاصله از بالا */
}
