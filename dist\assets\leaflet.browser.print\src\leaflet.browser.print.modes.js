/**
	MIT License http://www.opensource.org/licenses/mit-license.php
	نویسنده: ایگور ولادیکا <<EMAIL>> (https://github.com/Igor-<PERSON><PERSON><PERSON>/leaflet.browser.print)
**/
// leaflet.browser.print.modes.js
L.BrowserPrint.Mode = L.Class.extend({
	options: {
		title: '', // عنوان حالت چاپ
		invalidateBounds: false, // آیا محدوده نقشه باید مجدداً محاسبه شود؟
		margin: {}, // حاشیه‌های صفحه
		enableZoom: true, // آیا زوم فعال باشد؟
		zoom: 0, // سطح زوم
		rotate: 0, // چرخش
		scale: 1, // مقیاس
		orientation: '', // جهت صفحه (عمودی یا افقی)
		pageSize: 'A4', // اندازه صفحه
		pageSeries: '', // سری صفحه (مانند A, B, C, D)
		pageSeriesSize: '', // اندازه سری صفحه
		action: null, // عملیات چاپ
		header: { // تنظیمات هدر
			enabled: false, // آیا هدر فعال باشد؟
			text: "", // متن هدر
			size: "10mm", // اندازه هدر
			overTheMap: false, // آیا هدر روی نقشه نمایش داده شود؟
		},
		footer: { // تنظیمات فوتر
			enabled: false, // آیا فوتر فعال باشد؟
			text: "", // متن فوتر
			size: "10mm", // اندازه فوتر
			overTheMap: false, // آیا فوتر روی نقشه نمایش داده شود؟
		},
	},
	initialize: function (mode, options = {}) {
		if (!mode) {
			throw 'حالت چاپ باید مشخص شود. حالت‌های پیش‌فرض: "Portrait"، "Landscape"، "Auto" یا "Custom". توابع کوتاه مانند "L.BrowserPrint.Mode.Portrait" ترجیح داده می‌شوند.';
		}

		this.mode = mode; // حالت چاپ
		this.setOptions(options); // تنظیم گزینه‌ها
	},

    // تابع برای تنظیم گزینه‌ها
    setOptions: function(options){
        L.setOptions(this, options);

        // اگر عنوان مشخص نشده باشد، از نام حالت چاپ استفاده می‌شود
        if(!this.options.title){
            this.options.title = this.mode;
        }

        // اگر حالت چاپ "Portrait" یا "Landscape" باشد، جهت صفحه را تنظیم می‌کند
        if(this.mode === "Portrait" || this.mode === "Landscape"){
            this.options.orientation = this.mode;
        }

        // تنظیم اندازه صفحه و سری صفحه
        this.options.pageSize = (this.options.pageSize || 'A4').toUpperCase();
        this.options.pageSeries = ["A", "B", "C", "D"].indexOf(this.options.pageSize[0]) !== -1 ? this.options.pageSize[0] : "";
        this.options.pageSeriesSize = this.options.pageSize.substring(this.options.pageSeries.length);

        // اگر عملیات چاپ مشخص نشده باشد، یک عملیات پیش‌فرض تعریف می‌شود
        this.options.action = this.options.action || function (context, element) {
            return function () {
                context._printMode(element)
            };
        };
    }
});

// تابع کمکی برای ایجاد یک حالت چاپ جدید
L.browserPrint.mode = function(mode, options){
	return new L.BrowserPrint.Mode(mode, options);
};

// نام‌های حالت‌های چاپ
L.BrowserPrint.Mode.Name = {
	Landscape: "Landscape", // حالت افقی
	Portrait: "Portrait", // حالت عمودی
	Auto: "Auto", // حالت خودکار
	Custom: "Custom", // حالت سفارشی
};

// توابع کوتاه برای ایجاد حالت‌های چاپ

// حالت عمودی
L.BrowserPrint.Mode.Portrait = function(pageSize, options = {}) {
	options.pageSize = pageSize;
	options.invalidateBounds = options.invalidateBounds === true || options.invalidateBounds === false ?  options.invalidateBounds : false;
	return new L.BrowserPrint.Mode(L.BrowserPrint.Mode.Name.Portrait, options);
};

// حالت افقی
L.BrowserPrint.Mode.Landscape = function(pageSize, options = {}) {
	options.pageSize = pageSize;
	options.invalidateBounds = options.invalidateBounds === true || options.invalidateBounds === false ?  options.invalidateBounds : false;
	return new L.BrowserPrint.Mode(L.BrowserPrint.Mode.Name.Landscape, options);
};

// حالت خودکار
L.BrowserPrint.Mode.Auto = function(pageSize, options = {}) {
	options.pageSize = pageSize;
	options.invalidateBounds = options.invalidateBounds === true || options.invalidateBounds === false ?  options.invalidateBounds : true;
	return new L.BrowserPrint.Mode(L.BrowserPrint.Mode.Name.Auto, options);
};

// حالت سفارشی
L.BrowserPrint.Mode.Custom = function(pageSize, options = {}) {
	options.pageSize = pageSize;
	options.invalidateBounds = options.invalidateBounds === true || options.invalidateBounds === false ?  options.invalidateBounds : true;
	return new L.BrowserPrint.Mode(L.BrowserPrint.Mode.Name.Custom, options);
};