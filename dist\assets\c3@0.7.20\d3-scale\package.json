{"name": "d3-scale", "version": "2.2.2", "description": "Encodings that map abstract data to visual representation.", "keywords": ["d3", "d3-module", "scale", "visualization"], "homepage": "https://d3js.org/d3-scale/", "license": "BSD-3-<PERSON><PERSON>", "author": {"name": "<PERSON>", "url": "http://bost.ocks.org/mike"}, "main": "dist/d3-scale.js", "unpkg": "dist/d3-scale.min.js", "jsdelivr": "dist/d3-scale.min.js", "module": "src/index.js", "repository": {"type": "git", "url": "https://github.com/d3/d3-scale.git"}, "scripts": {"pretest": "rollup -c", "test": "TZ=America/Los_Angeles tape 'test/**/*-test.js' && eslint src", "prepublishOnly": "rm -rf dist && yarn test", "postpublish": "git push && git push --tags && cd ../d3.github.com && git pull && cp ../${npm_package_name}/dist/${npm_package_name}.js ${npm_package_name}.v${npm_package_version%%.*}.js && cp ../${npm_package_name}/dist/${npm_package_name}.min.js ${npm_package_name}.v${npm_package_version%%.*}.min.js && git add ${npm_package_name}.v${npm_package_version%%.*}.js ${npm_package_name}.v${npm_package_version%%.*}.min.js && git commit -m \"${npm_package_name} ${npm_package_version}\" && git push && cd - && zip -j dist/${npm_package_name}.zip -- LICENSE README.md dist/${npm_package_name}.js dist/${npm_package_name}.min.js"}, "dependencies": {"d3-array": "^1.2.0", "d3-collection": "1", "d3-format": "1", "d3-interpolate": "1", "d3-time": "1", "d3-time-format": "2"}, "devDependencies": {"d3-color": "1", "eslint": "5", "rollup": "0.64", "rollup-plugin-terser": "1", "tape": "4"}}