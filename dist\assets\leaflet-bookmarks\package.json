{"name": "leaflet-bookmarks", "version": "0.5.1", "description": "Leaflet plugin for user-generated bookmarks", "main": "dist/index.min.js", "module": "dist/index.mjs", "unpkg": "dist/index.min.js", "jsdelivr": "dist/index.min.js", "files": ["dist"], "scripts": {"test": "mocha-puppeteer test/*.test.js", "start": "npm run watch & serve -p 3001", "watch": "rollup -cw", "build-less": "lessc src/leaflet.bookmarks.less > dist/leaflet.bookmarks.css", "compress-less": "lessc -x src/leaflet.bookmarks.less > dist/leaflet.bookmarks.min.css", "build-css": "npm run build-less && npm run compress-less", "build-js": "rollup -c", "build": "npm run build-js && npm run build-css"}, "repository": {"type": "git", "url": "https://github.com/w8r/Leaflet.Bookmarks"}, "keywords": ["leaflet", "bookmarks", "plugin"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/w8r/Leaflet.Bookmarks/issues"}, "homepage": "https://github.com/w8r/Leaflet.Bookmarks", "devDependencies": {"@rollup/plugin-buble": "^0.21.1", "@rollup/plugin-commonjs": "^11.0.2", "@rollup/plugin-node-resolve": "^7.1.1", "chai": "^4.2.0", "leaflet-contextmenu": "^1.4.0", "leaflet-modal": "^0.2.0", "less": "^2.1.1", "lessc": "^1.0.2", "mocha": "^10.2.0", "mocha-puppeteer": "^0.14.0", "reify": "^0.20.12", "rollup": "^2.0.2", "rollup-plugin-embed-css": "^1.0.16", "rollup-plugin-terser": "^5.2.0", "serve": "^11.3.0", "tape": "^4.0.0"}, "dependencies": {"leaflet": "^1.9.3"}}