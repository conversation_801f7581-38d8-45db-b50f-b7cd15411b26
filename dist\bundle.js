/******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/******/ 	// expose the modules object (__webpack_modules__)
/******/ 	__webpack_require__.m = __webpack_modules__;
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/create fake namespace object */
/******/ 	(() => {
/******/ 		var getProto = Object.getPrototypeOf ? (obj) => (Object.getPrototypeOf(obj)) : (obj) => (obj.__proto__);
/******/ 		var leafPrototypes;
/******/ 		// create a fake namespace object
/******/ 		// mode & 1: value is a module id, require it
/******/ 		// mode & 2: merge all properties of value into the ns
/******/ 		// mode & 4: return value when already ns object
/******/ 		// mode & 16: return value when it's Promise-like
/******/ 		// mode & 8|1: behave like require
/******/ 		__webpack_require__.t = function(value, mode) {
/******/ 			if(mode & 1) value = this(value);
/******/ 			if(mode & 8) return value;
/******/ 			if(typeof value === 'object' && value) {
/******/ 				if((mode & 4) && value.__esModule) return value;
/******/ 				if((mode & 16) && typeof value.then === 'function') return value;
/******/ 			}
/******/ 			var ns = Object.create(null);
/******/ 			__webpack_require__.r(ns);
/******/ 			var def = {};
/******/ 			leafPrototypes = leafPrototypes || [null, getProto({}), getProto([]), getProto(getProto)];
/******/ 			for(var current = mode & 2 && value; (typeof current == 'object' || typeof current == 'function') && !~leafPrototypes.indexOf(current); current = getProto(current)) {
/******/ 				Object.getOwnPropertyNames(current).forEach((key) => (def[key] = () => (value[key])));
/******/ 			}
/******/ 			def['default'] = () => (value);
/******/ 			__webpack_require__.d(ns, def);
/******/ 			return ns;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/ensure chunk */
/******/ 	(() => {
/******/ 		__webpack_require__.f = {};
/******/ 		// This file contains only the entry chunk.
/******/ 		// The chunk loading function for additional chunks
/******/ 		__webpack_require__.e = (chunkId) => {
/******/ 			return Promise.all(Object.keys(__webpack_require__.f).reduce((promises, key) => {
/******/ 				__webpack_require__.f[key](chunkId, promises);
/******/ 				return promises;
/******/ 			}, []));
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/get javascript chunk filename */
/******/ 	(() => {
/******/ 		// This function allow to reference async chunks
/******/ 		__webpack_require__.u = (chunkId) => {
/******/ 			// return url for filenames based on template
/******/ 			return "" + chunkId + ".bundle.js";
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/load script */
/******/ 	(() => {
/******/ 		var inProgress = {};
/******/ 		var dataWebpackPrefix = "hozeh-gis:";
/******/ 		// loadScript function to load a script via script tag
/******/ 		__webpack_require__.l = (url, done, key, chunkId) => {
/******/ 			if(inProgress[url]) { inProgress[url].push(done); return; }
/******/ 			var script, needAttach;
/******/ 			if(key !== undefined) {
/******/ 				var scripts = document.getElementsByTagName("script");
/******/ 				for(var i = 0; i < scripts.length; i++) {
/******/ 					var s = scripts[i];
/******/ 					if(s.getAttribute("src") == url || s.getAttribute("data-webpack") == dataWebpackPrefix + key) { script = s; break; }
/******/ 				}
/******/ 			}
/******/ 			if(!script) {
/******/ 				needAttach = true;
/******/ 				script = document.createElement('script');
/******/ 		
/******/ 				script.charset = 'utf-8';
/******/ 				script.timeout = 120;
/******/ 				if (__webpack_require__.nc) {
/******/ 					script.setAttribute("nonce", __webpack_require__.nc);
/******/ 				}
/******/ 				script.setAttribute("data-webpack", dataWebpackPrefix + key);
/******/ 		
/******/ 				script.src = url;
/******/ 			}
/******/ 			inProgress[url] = [done];
/******/ 			var onScriptComplete = (prev, event) => {
/******/ 				// avoid mem leaks in IE.
/******/ 				script.onerror = script.onload = null;
/******/ 				clearTimeout(timeout);
/******/ 				var doneFns = inProgress[url];
/******/ 				delete inProgress[url];
/******/ 				script.parentNode && script.parentNode.removeChild(script);
/******/ 				doneFns && doneFns.forEach((fn) => (fn(event)));
/******/ 				if(prev) return prev(event);
/******/ 			}
/******/ 			var timeout = setTimeout(onScriptComplete.bind(null, undefined, { type: 'timeout', target: script }), 120000);
/******/ 			script.onerror = onScriptComplete.bind(null, script.onerror);
/******/ 			script.onload = onScriptComplete.bind(null, script.onload);
/******/ 			needAttach && document.head.appendChild(script);
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	(() => {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = (exports) => {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/publicPath */
/******/ 	(() => {
/******/ 		__webpack_require__.p = "./";
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/jsonp chunk loading */
/******/ 	(() => {
/******/ 		// no baseURI
/******/ 		
/******/ 		// object to store loaded and loading chunks
/******/ 		// undefined = chunk not loaded, null = chunk preloaded/prefetched
/******/ 		// [resolve, reject, Promise] = chunk loading, 0 = chunk loaded
/******/ 		var installedChunks = {
/******/ 			792: 0
/******/ 		};
/******/ 		
/******/ 		__webpack_require__.f.j = (chunkId, promises) => {
/******/ 				// JSONP chunk loading for javascript
/******/ 				var installedChunkData = __webpack_require__.o(installedChunks, chunkId) ? installedChunks[chunkId] : undefined;
/******/ 				if(installedChunkData !== 0) { // 0 means "already installed".
/******/ 		
/******/ 					// a Promise means "currently loading".
/******/ 					if(installedChunkData) {
/******/ 						promises.push(installedChunkData[2]);
/******/ 					} else {
/******/ 						if(true) { // all chunks have JS
/******/ 							// setup Promise in chunk cache
/******/ 							var promise = new Promise((resolve, reject) => (installedChunkData = installedChunks[chunkId] = [resolve, reject]));
/******/ 							promises.push(installedChunkData[2] = promise);
/******/ 		
/******/ 							// start chunk loading
/******/ 							var url = __webpack_require__.p + __webpack_require__.u(chunkId);
/******/ 							// create error before stack unwound to get useful stacktrace later
/******/ 							var error = new Error();
/******/ 							var loadingEnded = (event) => {
/******/ 								if(__webpack_require__.o(installedChunks, chunkId)) {
/******/ 									installedChunkData = installedChunks[chunkId];
/******/ 									if(installedChunkData !== 0) installedChunks[chunkId] = undefined;
/******/ 									if(installedChunkData) {
/******/ 										var errorType = event && (event.type === 'load' ? 'missing' : event.type);
/******/ 										var realSrc = event && event.target && event.target.src;
/******/ 										error.message = 'Loading chunk ' + chunkId + ' failed.\n(' + errorType + ': ' + realSrc + ')';
/******/ 										error.name = 'ChunkLoadError';
/******/ 										error.type = errorType;
/******/ 										error.request = realSrc;
/******/ 										installedChunkData[1](error);
/******/ 									}
/******/ 								}
/******/ 							};
/******/ 							__webpack_require__.l(url, loadingEnded, "chunk-" + chunkId, chunkId);
/******/ 						}
/******/ 					}
/******/ 				}
/******/ 		};
/******/ 		
/******/ 		// no prefetching
/******/ 		
/******/ 		// no preloaded
/******/ 		
/******/ 		// no HMR
/******/ 		
/******/ 		// no HMR manifest
/******/ 		
/******/ 		// no on chunks loaded
/******/ 		
/******/ 		// install a JSONP callback for chunk loading
/******/ 		var webpackJsonpCallback = (parentChunkLoadingFunction, data) => {
/******/ 			var [chunkIds, moreModules, runtime] = data;
/******/ 			// add "moreModules" to the modules object,
/******/ 			// then flag all "chunkIds" as loaded and fire callback
/******/ 			var moduleId, chunkId, i = 0;
/******/ 			if(chunkIds.some((id) => (installedChunks[id] !== 0))) {
/******/ 				for(moduleId in moreModules) {
/******/ 					if(__webpack_require__.o(moreModules, moduleId)) {
/******/ 						__webpack_require__.m[moduleId] = moreModules[moduleId];
/******/ 					}
/******/ 				}
/******/ 				if(runtime) var result = runtime(__webpack_require__);
/******/ 			}
/******/ 			if(parentChunkLoadingFunction) parentChunkLoadingFunction(data);
/******/ 			for(;i < chunkIds.length; i++) {
/******/ 				chunkId = chunkIds[i];
/******/ 				if(__webpack_require__.o(installedChunks, chunkId) && installedChunks[chunkId]) {
/******/ 					installedChunks[chunkId][0]();
/******/ 				}
/******/ 				installedChunks[chunkId] = 0;
/******/ 			}
/******/ 		
/******/ 		}
/******/ 		
/******/ 		var chunkLoadingGlobal = self["webpackChunkhozeh_gis"] = self["webpackChunkhozeh_gis"] || [];
/******/ 		chunkLoadingGlobal.forEach(webpackJsonpCallback.bind(null, 0));
/******/ 		chunkLoadingGlobal.push = webpackJsonpCallback.bind(null, chunkLoadingGlobal.push.bind(chunkLoadingGlobal));
/******/ 	})();
/******/ 	
/************************************************************************/
var __webpack_exports__ = {};

;// ./src/config/styles.js
//styles.js
function loadStyles() {
  const styles = [
    "./assets/bootstrap-3.3.6/dist/css/bootstrap.min.css",
    "./assets/jquerySelect2/dist/css/select2.min.css",
    "./assets/font-awesome4/4.6.3/css/font-awesome.min.css", // 2. Font Awesome
    "./assets/fontawesome-5.15.2/css/all.css",
    "./assets/leaflet@1.9.4/dist/leaflet.css", // 3. Leaflet
    // "./assets/bootstrap@5.3.0/bootstrap/dist/css/bootstrap.min.css",
    "./assets/leaflet-bookmarks/dist/leaflet.bookmarks.min.css",
    "./assets/leaflet-minimap/dist/Control.MiniMap.min.css",
    "./assets/leaflet-compass/dist/leaflet-compass.min.css",
    "./assets/leaflet-ruler/src/leaflet-ruler.css",
    "./assets/Leaflet.NavBar/src/Leaflet.NavBar.css",
    "./assets/leaflet-toolbar/dist/leaflet.toolbar.css",
    "./assets/leaflet.locatecontrol/dist/L.Control.Locate.min.css",
    "./assets/LeafletPlugins/sidebar/css/leaflet-sidebar.css", // 3.1 Leaflet Sidebar
    "./assets/bootstrap-table@1.20.2/bootstrap-table/dist/bootstrap-table.min.css", // 4. Bootstrap Table
    "./assets/c3@0.7.20/c3/c3.min.css", // 5. C3.js
    "./assets/geocoder/leaflet-control-geocoder/leaflet-control-geocoder/dist/Control.Geocoder.css",
    "./assets/jQuery-QueryBuilder@3.0.0/jQuery-QueryBuilder/dist/css/query-builder.default.css", // 7. QueryBuilder
    "./assets/LeafletDraw/dist/leaflet.draw.css", 
    "./assets/LeafletMeasure/leaflet-measure/dist/leaflet-measure.css", 
    "./assets/lightbox2/dist/css/lightbox.min.css",
    "./assets/coordinatedimagepreview/coordinatedimagepreview.css",
    "./assets/Leaflet.label/dist/leaflet.label.css",
    "./assets/leaflet-search/dist/leaflet-search.src.css",
    "./css/app.css",  
    "./css/loading.css",
    "./css/mousePosition.css",
    "./css/search-custom.css",
    "./css/widget-toolbar.css"
    
  ];

  const loadStyle = (href) => {
    return new Promise((resolve, reject) => {
      const link = document.createElement("link");
      link.rel = "stylesheet";
      link.href = href;
      link.onload = () => {
        resolve();
      };
      link.onerror = () => reject(new Error(`خطا در لود ${href}`));
      document.head.appendChild(link);
    });
  };

  return styles.reduce((promise, href) => {
    return promise.then(() => loadStyle(href));
  }, Promise.resolve());
}


;// ./src/config/scripts.js
// scripts.js
function loadScripts() {
  const scripts = [
    "./assets/ejs@3.1.10/ejs/ejs.min.js",
    "./assets/jquery@2.2.4/jquery/dist/jquery.min.js", // 1. jQuery
    "./assets/jquerySelect2/dist/js/select2.full.min.js",
    "./assets/bootstrap-3.3.6/dist/js/bootstrap.min.js", // 2. Bootstrap
    // "./assets/bootstrap@5.3.0/bootstrap/dist/js/bootstrap.bundle.min.js",
    // "./assets/bootstrap@5.3.0/@popperjs/core/dist/umd/popper.min.js",
    "./assets/leaflet@1.9.4/dist/leaflet.js", // 3. Leaflet
    "./assets/leaflet-hash/dist/leaflet-hash.min.js",
    "./assets/leaflet-bookmarks/dist/index.min.js",
    "./assets/leaflet-minimap/dist/Control.MiniMap.min.js",
    "./assets/leaflet-compass/dist/leaflet-compass.min.js",
    "./assets/leaflet-ruler/src/leaflet-ruler.js",
    "./assets/leaflet.browser.print/dist/leaflet.browser.print.js",
    "./assets/Leaflet.NavBar/src/Leaflet.NavBar.js",
    "./assets/leaflet-toolbar/dist/leaflet.toolbar.js",
    "./assets/leaflet.locatecontrol/dist/L.Control.Locate.min.js",
    "./assets/LeafletPlugins/sidebar/js/leaflet-sidebar.js", // 3.1 Leafletsidebar
    "./assets/bootstrap-table@1.20.2/bootstrap-table/dist/bootstrap-table.min.js", // 4. Bootstrap Table
    "./assets/d3@7.8.2/d3/dist/d3.min.js", // 5. D3.js
    "./assets/c3@0.7.20/c3/c3.min.js", // 6. C3.js
   // "./assets/esri-leaflet@3.0.5/esri-leaflet/dist/esri-leaflet.js", // 7. Esri Leaflet
    "./assets/geocoder/leaflet-control-geocoder/leaflet-control-geocoder/dist/Control.Geocoder.js", // 8. Esri Leaflet Geocoder
    "./assets/alasql@4.6.4/alasql/dist/alasql.min.js", // 9. AlaSQL
    "./assets/jQuery-QueryBuilder@3.0.0/jQuery-QueryBuilder/dist/js/query-builder.standalone.js", // 10. QueryBuilder
    "./assets/jspdf@2.5.1/jspdf/dist/jspdf.umd.min.js", // 11. jsPDF
    "./assets/jspdf-autotable@3.5.23/jspdf-autotable/dist/jspdf.plugin.autotable.min.js", // 12. jsPDF-AutoTable
    "./assets/LeafletDraw/dist/leaflet.draw.js",  
    "./assets/LeafletMeasure/leaflet-measure/dist/leaflet-measure.fa.js",
    "./assets/Leaflet.bouncemarker/bouncemarker.js",
    "./assets/coordinatedimagepreview/coordinatedimagepreview.js",
    "./assets/lightbox2/dist/js/lightbox.min.js",
    "./assets/Leaflet.label/dist/leaflet.label-src.js",
    "./assets/leaflet-search/dist/leaflet-search.src.js"

  ];
  
    const loadScript = (src) => {
      return new Promise((resolve, reject) => {
        const script = document.createElement("script");
        script.src = src;
        script.async = false;
        script.onload = () => {
          resolve();
        };
        script.onerror = () => {
          console.error(` خطا در لود ${src}`);
          reject(new Error(`خطا در لود ${src}`));
        };
        document.body.appendChild(script);
      });
    };
  
    return scripts.reduce((promise, src) => {
      return promise.then(() => loadScript(src));
    }, Promise.resolve());
  }
  
  
;// ./src/js/config.js
// src/js/config.js
const config_config = {
  defaultBounds: null,
  defaultZoom: null,
  geojsonPath: "./Data",
  templatePath: "./templates",
};

// Load map configuration from JSON file
async function loadMapConfig() {
  const response = await fetch('./config/map.json');
  const mapConfig = await response.json();
  
  config_config.defaultBounds = mapConfig.defaultBounds;
  config_config.defaultZoom = mapConfig.defaultZoom;
  
  console.log('Map configuration loaded successfully:', mapConfig);
}

// Initialize map configuration
loadMapConfig();

let layersConfig = [];
let currentLayerIndex = -1; // مقدار پیش‌فرض که نشون بده هنوز لایه‌ای انتخاب نشده
let geojsonLayers = {};
let features = [];
let map = null;
let featureLayers = {};
let highlightLayer = null;
let isTableFullScreen = false;

function getMap() { return map; }
function setMap(m) { map = m; }

function getCurrentLayerIndex() { return currentLayerIndex; }
function setCurrentLayerIndex(index) { 
  if (typeof index === 'number' && index >= 0 && index < layersConfig.length) {
    currentLayerIndex = index;
  } else {
    console.warn("اندیس نامعتبر برای setCurrentLayerIndex:", index, "طول layersConfig:", layersConfig.length);
  }
}

function config_getFeatures() { return features; }
function setFeatures(data) { 
  if (Array.isArray(data)) {
    features = data;
  } else {
    console.warn("داده‌های ورودی برای setFeatures باید آرایه باشند:", data);
    features = [];
  }
}

function getHighlightLayer() { return highlightLayer; }
function setHighlightLayer(layer) { highlightLayer = layer; }

function config_getLayersConfig() { return layersConfig; }
function setLayersConfig(data) { 
  if (Array.isArray(data)) {
    layersConfig = data;
  } else {
    console.warn("داده‌های ورودی برای setLayersConfig باید آرایه باشند:", data);
    layersConfig = [];
  }
}

function getGeojsonLayers() { return geojsonLayers; }
function config_getFeatureLayers() { return featureLayers; }

function getIsTableFullScreen() { return isTableFullScreen; }
function setIsTableFullScreen(value) { 
  if (typeof value === 'boolean') {
    isTableFullScreen = value;
  } else {
    console.warn("مقدار ورودی برای setIsTableFullScreen باید بولی باشد:", value);
    isTableFullScreen = false;
  }
}


;// ./src/js/map.js
// src/js/map.js


function fitToBounds() {
  const map = getMap();
  const currentHash = L.Hash.parseHash(window.location.hash);
  if (currentHash) {
    return; // اگه هَش وجود داره، کاری نکن
  }

  const bounds = L.latLngBounds(
    L.latLng(config_config.defaultBounds.bot, config_config.defaultBounds.left),
    L.latLng(config_config.defaultBounds.top, config_config.defaultBounds.right)
  );

  const center = bounds.getCenter();
  
  // استفاده از زوم پیش‌فرض تعریف شده در کانفیگ
  const defaultZoom = config_config.defaultZoom || 14;
  const calculatedZoom = map.getBoundsZoom(bounds);
  
  // انتخاب زوم بالاتر بین زوم محاسبه شده و زوم پیش‌فرض
  const finalZoom = Math.max(calculatedZoom, defaultZoom);

  map.setView(center, finalZoom, {
    animate: true,
    duration: 2.0,
    easeLinearity: 0.3
  });

}

// تابع برای تغییر زوم پیش‌فرض
function setDefaultZoom(zoom) {
  if (typeof zoom === 'number' && zoom >= 1 && zoom <= 21) {
    config.defaultZoom = zoom;
    console.log(`زوم پیش‌فرض به ${zoom} تغییر یافت`);
  } else {
    console.warn("زوم باید بین 1 تا 21 باشد");
  }
}

// تابع برای دریافت زوم پیش‌فرض فعلی
function getDefaultZoom() {
  return config.defaultZoom || 14;
}


;// ./src/js/utils.js
// src/js/utils.js


function loadTemplate(type, index) {
  let templateFile;
  if (type === "layer") {
    templateFile = "./templates/layer.html";
  } else {
    templateFile = `./templates/${type}.html`;
  }

  return new Promise((resolve, reject) => {
    $.ajax({
      url: templateFile,
      method: "GET",
      success: function (data) {
        if (type === "layer") {
          const layersConfig = config_getLayersConfig();
          const rendered = ejs.render(data, {
            index: index,
            layersConfig: layersConfig,
          });
          resolve(rendered);
        } else {
          resolve(data);
        }
      },
      error: function (jqXHR, textStatus, errorThrown) {
        console.error(`خطا در بارگیری تمپلیت ${templateFile}:`, textStatus, errorThrown);
        reject(new Error(`خطا در بارگیری ${templateFile}`));
      },
    });
  });
}

function urlFormatter(value, row, index) {
  if (typeof value === "string" && (value.startsWith("http://") || value.startsWith("https://"))) {
    return `<a href="${value}" target="_blank">${value}</a>`;
  }
  return value;
}


;// ./src/js/table.js
// src/js/table.js




// تابع ساخت تنظیمات جدول (ستون‌ها و فیلترها)
function table_buildConfig(config, properties) {
  const filters = [];
  const table = [
    {
      field: "action",
      title: "<i class='fa fa-gear'></i> عملیات",
      align: "center",
      valign: "middle",
      width: "75px",
      cardVisible: false,
      switchable: false,
      formatter: function (value, row) {
        const leafletId = row._leaflet_id;
        return [
          `<a class="zoom" href="javascript:void(0)" title="بزرگنمایی" data-leaflet-id="${leafletId}" style="margin-right: 10px;">`,
          '<i class="fa fa-search-plus"></i>',
          '</a>',
          `<a class="identify" href="javascript:void(0)" title="شناسایی" data-leaflet-id="${leafletId}">`,
          '<i class="fa fa-info-circle"></i>',
          '</a>'
        ].join("");
      },
      events: {
        "click .zoom": function (e, value, row) {
          const leafletId = row._leaflet_id;
          const layerName = config_getLayersConfig()[getCurrentLayerIndex()].name;
          const layer = config_getFeatureLayers()[layerName]?.getLayer(leafletId);
          if (layer) {
            const map = getMap();
            const highlightLayer = getHighlightLayer();

            highlightLayer.clearLayers();
            map.invalidateSize();

            if (typeof layer.getBounds === "function") {
              map.fitBounds(layer.getBounds(), {
                padding: [50, 50],
                maxZoom: 17,
                animate: true,
                duration: 1.0,
              });
            } else if (typeof layer.getLatLng === "function") {
              const currentZoom = map.getZoom();
              const targetZoom = Math.max(currentZoom, 17);
              map.setView(layer.getLatLng(), targetZoom, {
                animate: true,
                duration: 1.0,
              });
            } else {
              console.warn("نوع لایه پشتیبانی نمی‌شود برای زوم:", layer);
              return;
            }

            highlightLayer.addData(layer.toGeoJSON());
          } else {
            console.warn("لایه یا عارضه پیدا نشد برای ID:", leafletId);
          }
        },
        "click .identify": function (e, value, row) {
          const leafletId = row._leaflet_id;
          const layerName = config_getLayersConfig()[getCurrentLayerIndex()].name;
          const layer = config_getFeatureLayers()[layerName]?.getLayer(leafletId);
          if (layer) {
            identifyFeature(leafletId);
            getHighlightLayer().clearLayers();
            getHighlightLayer().addData(layer.toGeoJSON());
          } else {
            console.warn("لایه یا عارضه پیدا نشد برای ID:", leafletId);
          }
        },
      },
    },
  ];

  $.each(properties, function (index, value) {
    if (value.table) {
      table.push({
        field: value.value,
        title: value.label,
        sortable: true,
        searchable: true,
      });
    }
  });

  return { filters, table };
}

// تابع همگام‌سازی داده‌های جدول
function syncTable() {
  const tableId = `#table-${getCurrentLayerIndex()}`;
  const tableFeatures = config_getFeatures();


  const dataForTable = tableFeatures.map(feature => ({
    ...feature.properties,
    _leaflet_id: feature._leaflet_id,
  }));

  try {
    $(tableId).bootstrapTable("load", dataForTable);
  } catch (error) {
    console.error("خطا در لود داده‌ها به جدول:", error);
  }

  const featureCount = $(tableId).bootstrapTable("getData").length;
  $(`#feature-count-layer${getCurrentLayerIndex()}`).html(
    featureCount +
      (featureCount === 1 ? " عارضه قابل‌رویت" : " عوارض قابل‌رویت")
  );
}

// تابع به‌روزرسانی جدول با پشتیبانی از همه لایه‌ها
function updateTable(layerIndex) {
  const layersConfig = config_getLayersConfig();
  if (layerIndex < 0 || layerIndex >= layersConfig.length) {
    console.error(`اندیس ${layerIndex} خارج از محدوده layersConfig است`);
    return;
  }

  const layerConfig = layersConfig[layerIndex];
  setCurrentLayerIndex(layerIndex);

  const tableTitle = layerConfig.config.title || layerConfig.name;
  $(`#table-container-layer${layerIndex} .table-title`).html(tableTitle);

  const { filters, table } = table_buildConfig(layerConfig.config, layerConfig.properties);

  const tableId = `#table-${layerIndex}`;

  const features = config_getFeatures();
  const dataForTable = features.map(feature => ({
    ...feature.properties,
    _leaflet_id: feature._leaflet_id,
  }));


  try {
    $(tableId).bootstrapTable("destroy");
    $(tableId).bootstrapTable({
      cache: false,
      height: 400, // ارتفاع ثابت
      undefinedText: "",
      striped: false,
      pagination: true, // صفحه‌بندی فعال
      pageSize: 10, // ۱۰ ردیف در هر صفحه
      pageList: [10, 25, 50, 100, "All"], // گزینه‌های تعداد ردیف‌ها
      paginationVAlign: "bottom", // صفحه‌بندی فقط توی پایین
      paginationHAlign: "right", // تراز افقی صفحه‌بندی
      showPaginationSwitch: true, // دکمه نمایش/مخفی کردن صفحه‌بندی فعال
      minimumCountColumns: 1,
      sortName: layerConfig.config.sortProperty || table[1]?.field,
      sortOrder: layerConfig.config.sortOrder || "asc",
      toolbar: `#toolbar-layer${layerIndex}`,
      search: true,
      trimOnSearch: false,
      showColumns: true,
      showToggle: true,
      columns: table,
      data: dataForTable,
      onLoadSuccess: function (data) {
      },
      onLoadError: function (status, error) {
        console.error("خطا در لود جدول:", status, error);
      },
    });
  } catch (error) {
    console.error("خطا در به‌روزرسانی جدول:", error);
  }

  $(window).resize(function () {
    $(tableId).bootstrapTable("resetView", {
      height: 400,
    });
  });

  syncTable();
  setTimeout(() => {
    syncTable();
  }, 500);

  switchView("split");
}


;// ./src/js/filters.js
// src/js/filters.js


//import { buildConfig } from "@js/table.js"; // اضافه کردن برای دسترسی به buildConfig (در صورت نیاز)

function buildFilters(filters) {
  if (!filters || filters.length === 0) {
    console.warn("هیچ فیلتری برای QueryBuilder تعریف نشده است.");
    return;
  }

  // پردازش هر فیلتر برای اطمینان از وجود values
  const processedFilters = filters.map(filter => {
    if (!filter.values || filter.values.length === 0) {
      // اگر values خالی یا تعریف نشده است، از alasql برای استخراج مقادیر متمایز استفاده کن
      try {
        // اصلاح دستور alasql برای استخراج مقادیر متمایز
        const distinctValues = alasql(
          `SELECT DISTINCT [${filter.id}] AS value FROM ? ORDER BY value ASC`,
          [config_getLayersConfig()[getCurrentLayerIndex()].geojson.features || getFeatures()]
        );
        filter.values = distinctValues
          .map((result) => result.value)
          .filter((v) => v !== null && v !== undefined && v !== "")
          .map((v) => (typeof v === "string" ? v.trim() : v));
      } catch (error) {
        console.error(`خطا در گرفتن مقادیر متمایز برای ${filter.id}:`, error);
        filter.values = []; // در صورت خطا، لیست خالی می‌ذاریم
      }
    }
    return filter;
  });

  $("#query-builder").queryBuilder({
    allow_empty: true,
    filters: processedFilters,
  });
}

function applyFilter() {
  const query = "SELECT * FROM ?";
  const sql = $("#query-builder").queryBuilder("getSQL", false, false).sql;
  const fullQuery = sql.length > 0 ? `${query} WHERE ${sql}` : query;

  alasql(fullQuery, [config_getLayersConfig()[getCurrentLayerIndex()].geojson.features || getFeatures()], function (filteredFeatures) {
    const currentLayer = config_getFeatureLayers()[config_getLayersConfig()[getCurrentLayerIndex()].name];
    currentLayer.clearLayers();
    currentLayer.addData(filteredFeatures);
    syncTable(); // همگام‌سازی جدول با داده‌های فیلترشده
  });
}


;// ./src/js/layers.js
// src/js/layers.js






function loadLayers(index) {
  const layersConfig = config_getLayersConfig();

  if (!layersConfig.length) {
    console.error("لایه‌ها هنوز بارگیری نشده‌اند. منتظر لود config بمانید.");
    return;
  }
  if (typeof index !== "number" || index < 0 || index >= layersConfig.length) {
    console.error("اندیس نامعتبر است:", index);
    return;
  }

  setCurrentLayerIndex(index); // اندیس لایه فعلی رو تنظیم کن
  const layerConfig = layersConfig[index];

  const layer = config_getFeatureLayers()[layerConfig.name];
  window.toggleLayer(layerConfig.name, true); // روشن کردن لایه با تابع ui.js

  // لود تمپلیت جدول
  loadTemplate("layer", index)
    .then((tableTemplate) => {
      $("#table-container").html(tableTemplate);

      // چک کردن کش یا لود داده‌های GeoJSON
      if (geojsonLayers[layerConfig.name]) {
        const geojson = geojsonLayers[layerConfig.name];
        updateFeaturesWithLeafletIds(geojson.features, layer);
        if (config_getFeatures().length > 0) {
          updateTable(index); // اندیس رو به updateTable پاس بده
        } else {
          console.warn("هیچ داده‌ای برای این لایه وجود ندارد.");
        }
      } else {
        $.getJSON(layerConfig.geojson)
          .done((data) => {
            geojsonLayers[layerConfig.name] = data;
            updateFeaturesWithLeafletIds(data.features, layer);

            if (config_getFeatures().length > 0) {
              updateTable(index); // اندیس رو به updateTable پاس بده
            } else {
              console.warn("هیچ داده‌ای برای این لایه در GeoJSON وجود ندارد.");
            }
          })
          .fail((jqXHR, textStatus, errorThrown) => {
            console.error("خطا در بارگیری GeoJSON برای جدول:", textStatus, errorThrown);
          });
      }

      // رویداد دکمه بستن جدول
      $(".close-table-btn").off("click").on("click", function () {
        switchView("map");
      });

      // رویداد دکمه تمام‌صفحه
      $(".toggle-fullscreen-btn").off("click").on("click", function () {
        const currentState = getIsTableFullScreen();
        if (currentState) {
          switchView("split");
          $(this).find("i").removeClass("fa-compress").addClass("fa-expand");
        } else {
          switchView("table");
          $(this).find("i").removeClass("fa-expand").addClass("fa-compress");
        }
        setIsTableFullScreen(!currentState);
      });

      // رویداد دکمه فیلتر
      $(".filter-btn").off("click").on("click", function (e) {
        e.preventDefault();
        e.stopPropagation();
        loadTemplate("filterModal", null)
          .then((data) => {
            $("body").append(data);
            $("#filterModal").modal({
              show: true,
              backdrop: "static",
              keyboard: false,
            });

            const layerConfig = config_getLayersConfig()[getCurrentLayerIndex()];
            const { filters } = buildConfig(layerConfig.config, layerConfig.properties);
            buildFilters(filters);

            $("#view-sql-btn").off("click").on("click", function () {
              alert($("#query-builder").queryBuilder("getSQL", false, false).sql);
            });

            $("#apply-filter-btn").off("click").on("click", applyFilter);

            $("#reset-filter-btn").off("click").on("click", function () {
              $("#query-builder").queryBuilder("reset");
              applyFilter();
            });
          })
          .catch((error) => console.error("خطا در لود مودال فیلتر:", error));
      });

      // رویداد دکمه دانلود PDF
      $(".download-pdf-btn").off("click").on("click", function (e) {
        e.preventDefault();
        const tableId = `#table-${getCurrentLayerIndex()}`;
        if ($.fn.tableExport) {
          $(tableId).tableExport({
            type: "pdf",
            ignoreColumn: [0],
            fileName: `data-${layerConfig.name}`, // نام فایل با نام لایه
            jspdf: {
              format: "bestfit",
              margins: { left: 20, right: 10, top: 20, bottom: 20 },
              autotable: { extendWidth: false, overflow: "linebreak" },
            },
          });
        } else {
          console.error("پلاگین tableExport لود نشده است!");
        }
      });
    })
    .catch((error) => console.error("خطا در لود تمپلیت لایه:", error));
}

// تابع به‌روزرسانی features با _leaflet_id
function updateFeaturesWithLeafletIds(features, layer) {
  const layerFeatures = layer.getLayers();
  const updatedFeatures = features.map(feature => {
    const matchingLayerFeature = layerFeatures.find(lf => {
      const lfProps = lf.feature.properties;
      const fProps = feature.properties;
      return Object.keys(fProps).every(key => fProps[key] === lfProps[key]);
    });
    return {
      ...feature,
      _leaflet_id: matchingLayerFeature ? matchingLayerFeature._leaflet_id : null,
    };
  });
  setFeatures(updatedFeatures);
}

// تابع شناسایی عارضه (بدون تغییر بزرگ)
function identifyFeature(id, clickedLayer) {
  let layer;
  if (clickedLayer) {
    layer = clickedLayer;
  } else {
    const currentLayerName = config_getLayersConfig()[getCurrentLayerIndex()]?.name;
    if (!currentLayerName) {
      console.error("لایه فعلی پیدا نشد. currentLayerIndex:", getCurrentLayerIndex());
      return;
    }
    layer = config_getFeatureLayers()[currentLayerName]?.getLayer(id);
  }

  if (!layer) {
    console.warn("لایه یا عارضه‌ای با ID پیدا نشد:", id);
    return;
  }

  const featureProperties = layer.feature?.properties || {};
  const layerConfig = clickedLayer
    ? config_getLayersConfig().find(config => config.name === clickedLayer.options.layerName)
    : config_getLayersConfig()[getCurrentLayerIndex()];

  if (!layerConfig) {
    console.error("تنظیمات لایه پیدا نشد.");
    return;
  }

  // بررسی وجود عکس
  const imageProperty = featureProperties.image;
  let imageContent = "";

  if (imageProperty && typeof imageProperty === "string" && imageProperty.trim() !== "") {
    const imagePath = `./Data/${imageProperty}`;
    // استخراج شماره عکس از مسیر (مثل bank/3.jpg -> 3)
    const imageNumber = imageProperty.split('/')[1]?.split('.')[0];
    const layerFolder = imageProperty.split('/')[0];

    imageContent = `
      <div class="row mt-3">
        <div class="col-12">
          <div class="text-center">
            <div class="btn-group mb-2" role="group">
              <button type="button" class="btn btn-info btn-sm"
                      onclick="openImageGallery('${layerFolder}', '${imageNumber}')"
                      style="font-family: 'Nahid', Tahoma, Geneva, Verdana, sans-serif;">
                <i class="fas fa-images"></i> عکس‌های بیشتر
              </button>
              <button type="button" class="btn btn-outline-secondary btn-sm"
                      id="toggleImageBtn"
                      onclick="toggleMainImage()"
                      title="نمایش/مخفی کردن عکس"
                      style="font-family: 'Nahid', Tahoma, Geneva, Verdana, sans-serif;">
                <i class="fas fa-eye" id="toggleImageIcon"></i>
              </button>
            </div>
            <br>
            <div id="mainImageContainer" style="display: none;">
              <img src="${imagePath}"
                   alt="تصویر عارضه"
                   class="img-fluid img-thumbnail"
                   style="cursor: pointer; border: 2px solid #ddd; max-height: 300px;"
                   onclick="window.open('${imagePath}', '_blank')"
                   onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
              <div style="display: none; padding: 20px; text-align: center; color: #666; background: #f8f9fa; border: 1px solid #ddd; border-radius: 4px;">
                <i class="fas fa-image" style="font-size: 24px; margin-bottom: 10px;"></i>
                <br>تصویر موجود نیست
              </div>
            </div>
          </div>
        </div>
      </div>
    `;
  }

  // ساخت محتوای فرم
  let content = `
    <div class="container-fluid">
      <div class="row">
        <div class="col-12">
          <table class='table table-striped table-bordered table-condensed'>
  `;

  $.each(featureProperties, function (key, value) {
    if (!value) value = "";
    if (typeof value === "string" && (value.indexOf("http") === 0 || value.indexOf("https") === 0)) {
      value = `<a href="${value}" target="_blank">${value}</a>`;
    }
    $.each(layerConfig.properties, function (index, property) {
      if (key === property.value && property.info !== false) {
        content += `<tr><th>${property.label}</th><td>${value}</td></tr>`;
      }
    });
  });

  content += `
          </table>
        </div>
      </div>
      ${imageContent}
    </div>
  `;

  $("#feature-info").html(content);
  $("#featureModal").modal("show");
}

// تابع باز کردن گالری تصاویر
window.openImageGallery = function(layerFolder, imageNumber) {
  console.log(`باز کردن گالری برای ${layerFolder}/${imageNumber}`);

  // نمایش loading
  $("#galleryLoading").show();
  $("#noImagesMessage").hide();
  $("#imageCarousel").hide();
  $("#imageGalleryModal").modal("show");

  // درخواست لیست عکس‌ها از سرور
  const galleryPath = `./Data/${layerFolder}/${imageNumber}`;

  // شبیه‌سازی درخواست برای دریافت لیست عکس‌ها
  // در حالت واقعی باید از API استفاده کنید
  loadImageGallery(galleryPath, layerFolder, imageNumber);
};

// تابع toggle کردن نمایش عکس اصلی
window.toggleMainImage = function() {
  const imageContainer = $("#mainImageContainer");
  const toggleBtn = $("#toggleImageBtn");
  const toggleIcon = $("#toggleImageIcon");

  if (imageContainer.is(":visible")) {
    // مخفی کردن عکس
    imageContainer.hide();
    toggleIcon.removeClass("fa-eye-slash").addClass("fa-eye");
    toggleBtn.removeClass("btn-secondary").addClass("btn-outline-secondary");
    toggleBtn.attr("title", "نمایش عکس");
  } else {
    // نمایش عکس
    imageContainer.show();
    toggleIcon.removeClass("fa-eye").addClass("fa-eye-slash");
    toggleBtn.removeClass("btn-outline-secondary").addClass("btn-secondary");
    toggleBtn.attr("title", "مخفی کردن عکس");
  }
};

// تابع بارگیری گالری تصاویر
function loadImageGallery(galleryPath, layerFolder, imageNumber) {
  // لیست پسوندهای معمول عکس
  const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
  const imagePromises = [];

  // تست کردن وجود عکس‌ها با نام‌های مختلف
  const commonNames = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10',
                       'main', 'front', 'back', 'side', 'interior', 'exterior',
                       'view1', 'view2', 'view3', 'detail1', 'detail2'];

  let foundImages = [];
  let checkedCount = 0;
  const totalToCheck = commonNames.length * imageExtensions.length;

  commonNames.forEach(name => {
    imageExtensions.forEach(ext => {
      const imagePath = `${galleryPath}/${name}.${ext}`;
      const img = new Image();

      img.onload = function() {
        foundImages.push({
          path: imagePath,
          name: name,
          ext: ext
        });
        checkedCount++;
        if (checkedCount === totalToCheck) {
          displayImageGallery(foundImages);
        }
      };

      img.onerror = function() {
        checkedCount++;
        if (checkedCount === totalToCheck) {
          displayImageGallery(foundImages);
        }
      };

      img.src = imagePath;
    });
  });

  // Timeout برای جلوگیری از انتظار بی‌نهایت
  setTimeout(() => {
    if (checkedCount < totalToCheck) {
      displayImageGallery(foundImages);
    }
  }, 3000);
}

// تابع نمایش گالری تصاویر
function displayImageGallery(images) {
  $("#galleryLoading").hide();

  if (images.length === 0) {
    $("#noImagesMessage").show();
    return;
  }

  // مرتب کردن عکس‌ها
  images.sort((a, b) => {
    // اولویت با اعداد
    const aNum = parseInt(a.name);
    const bNum = parseInt(b.name);
    if (!isNaN(aNum) && !isNaN(bNum)) {
      return aNum - bNum;
    }
    // سپس بر اساس نام
    return a.name.localeCompare(b.name);
  });

  // ساخت indicators
  let indicatorsHTML = '';
  images.forEach((img, index) => {
    indicatorsHTML += `<li data-target="#imageCarousel" data-slide-to="${index}" ${index === 0 ? 'class="active"' : ''}></li>`;
  });
  $("#carouselIndicators").html(indicatorsHTML);

  // ساخت slides
  let slidesHTML = '';
  images.forEach((img, index) => {
    slidesHTML += `
      <div class="item ${index === 0 ? 'active' : ''}">
        <img src="${img.path}" alt="تصویر ${img.name}" style="width: 100%; height: 400px; object-fit: contain; background: #f8f9fa;">
        <div class="carousel-caption">
          <h4 style="font-family: 'Nahid', Tahoma, Geneva, Verdana, sans-serif; background: rgba(0,0,0,0.7); padding: 5px 10px; border-radius: 4px;">
            تصویر ${img.name}
          </h4>
        </div>
      </div>
    `;
  });
  $("#carouselInner").html(slidesHTML);

  $("#imageCarousel").show();
  console.log(`${images.length} تصویر در گالری بارگیری شد`);
}


;// ./src/js/ui.js




let baseLayers = {};
let overlayLayers = {};
let sidebar = null;
let drawControl = null;
let editableLayers = null;
let activeDrawTool = null;
let measureControl = null;
let measureLabels = null;
let rulerControl = null;
let printControl = null;
let coordinateMarkers = null;
let bookmarkControl = null;
let bookmarkMarkers = new Map();
let isPickingCoordinates = false; // وضعیت انتخاب مختصات

let searchControl = null; // کنترل جستجو در ناوبار
let currentSearchLayerName = null; // نام لایه فعال برای جستجو

var zoomMode = null;
var startPoint = null;
var boxDiv = null;

async function setupMap() {
  const googlesat = L.tileLayer(
    "https://mt1.google.com/vt/lyrs=s&x={x}&y={y}&z={z}",
    {
      maxZoom: 21,
      subdomains: ["mt0", "mt1", "mt2", "mt3"],
      attribution: "",
      zIndex: 0,
    }
  );

    const googlemap = L.tileLayer(
    "https://mt1.google.com/vt/lyrs=m&x={x}&y={y}&z={z}",
    {
      maxZoom: 21,
      subdomains: ["mt0", "mt1", "mt2", "mt3"],
      attribution: "",
      zIndex: 0,
    }
  );

  const osm = L.tileLayer("https://tile.openstreetmap.org/{z}/{x}/{y}.png", {
    maxZoom: 21,
    attribution: "",
    zIndex: 0,
  });

  const noBasemap = L.layerGroup([], {
    zIndex: 0,
  });

  const highlightLayer = L.geoJson(null, {
    pointToLayer: function (feature, latlng) {
      return L.circleMarker(latlng, {
        radius: 12,
        color: "#FF0000",
        weight: 4,
        opacity: 1,
        fillColor: "#FF0000",
        fillOpacity: 0.8,
        clickable: false,
      });
    },
    style: function (feature) {
      switch (feature.geometry.type) {
        case "LineString":
        case "MultiLineString":
          return {
            color: "#FF00FF",
            weight: 4,
            opacity: 1,
            clickable: false,
          };
        case "Polygon":
        case "MultiPolygon":
          return {
            color: "#FF00FF",
            weight: 4,
            opacity: 1,
            fillColor: "#FF00FF",
            fillOpacity: 0.5,
            clickable: false,
          };
        default:
          return {};
      }
    },
    zIndex: 100,
  });

  const map = L.map("map", {
    layers: [osm, highlightLayer],
  });

  setMap(map);
  setHighlightLayer(highlightLayer);

  var hash = new L.Hash(map);
  console.log("پلاگین Hash فعال شد:", hash);

  let isHashSet = false;

  const parsedHash = L.Hash.parseHash(window.location.hash);

  if (parsedHash) {
    map.setView(parsedHash.center, parsedHash.zoom);
    isHashSet = true;
  } else {
    fitToBounds();
  }

  map.on("moveend", function () {
    if (!isHashSet) {}
  });

  await __webpack_require__.e(/* import() */ 746).then(__webpack_require__.t.bind(__webpack_require__, 746, 23));

  editableLayers = new L.FeatureGroup([], {
    zIndex: 50,
  });
  map.addLayer(editableLayers);

  coordinateMarkers = new L.FeatureGroup([], {
    zIndex: 51,
  });
  map.addLayer(coordinateMarkers);

  // فعال‌سازی پلاگین
  var imagePreviewOptions = {
    imageServiceAddress: "./Data/a/testdata.json",
    limit: 20,
    openButtonTitle: "نمایش تصاویر",
    closeButtonTitle: "بستن تصاویر",
    map: map
  };
  $('#coordinatedImagePreviewControlContainer').CoordinatedImagePreviewControl(imagePreviewOptions);

  // ساخت کنترل سفارشی برای باز و بسته کردن
  const ImagePreviewControl = L.Control.extend({
    options: {
        position: "topleft"  // تغییر از topright به topleft تا با toolbar تداخل نکند
    },
    onAdd: function (map) {
        var container = L.DomUtil.create('div', 'leaflet-bar leaflet-control leaflet-control-custom');
        container.innerHTML = '<i class="fas fa-images"></i>';
        container.style.backgroundColor = 'white';
        container.style.width = '30px';
        container.style.height = '30px';
        container.style.display = 'flex';
        container.style.alignItems = 'center';
        container.style.justifyContent = 'center';
        container.style.cursor = 'pointer';
        // حذف title تا تولتیپ اضافی ظاهر نشود
        // container.title = 'نمایش/مخفی کردن تصاویر';

        let isOpen = false;
        container.onclick = function () {
            if (!isOpen) {
                // شبیه‌سازی کلیک دکمه باز کردن
                $("#coordinatedImagePreviewControlOpenButton").trigger("click");
                console.log("باکس تصاویر باز شد");
            } else {
                // شبیه‌سازی کلیک دکمه بستن
                $("#coordinatedImagePreviewControlCloseButton").trigger("click");
                console.log("باکس تصاویر بسته شد");
            }
            isOpen = !isOpen;
        };

        return container;
    }
  });

  map.addControl(new ImagePreviewControl());


  if (typeof L.Control.Bookmarks === "undefined") {
    console.error("پلاگین Leaflet.Bookmarks لود نشده است! لطفاً مسیر فایل را چک کنید.");
    return;
  }

  try {
    bookmarkControl = new L.Control.Bookmarks({
      position: "topright",
      localStorage: true,
      name: "my-map-bookmarks",
    }).addTo(map);
    console.log("کنترل بوکمارک با موفقیت به نقشه اضافه شد:", bookmarkControl);
    console.log("وضعیت _storage:", bookmarkControl._storage);

    if (bookmarkControl._container) {
      bookmarkControl._container.style.display = "none";
    }
  } catch (error) {
    console.error("خطا در اضافه کردن کنترل بوکمارک:", error);
  }

  const drawOptions = {
    position: "topright",
    draw: {
      polyline: {
        shapeOptions: {
          color: "red",
          weight: 4,
          opacity: 1,
        },
      },
      polygon: {
        allowIntersection: false,
        drawError: {
          color: "purple",
          message: "<strong>خطا!</strong> نمی‌تونید اینو بکشید!",
        },
        shapeOptions: {
          color: "orange",
          weight: 4,
          opacity: 1,
          fillColor: "#32CD32",
          fillOpacity: 0.5,
        },
      },
      circle: {
        shapeOptions: {
          color: "steelblue",
          weight: 4,
          opacity: 1,
          fillColor: "#8A2BE2",
          fillOpacity: 0.5,
        },
      },
      rectangle: {
        shapeOptions: {
          color: "green",
          weight: 4,
          opacity: 1,
          fillColor: "#FF4500",
          fillOpacity: 0.5,
        },
      },
      marker: {
        icon: new L.Icon.Default(),
      },
    },
    edit: {
      featureGroup: editableLayers,
      edit: true,
      remove: true,
    },
  };

  if (typeof L.Control.Draw === "undefined") {
    console.error("Leaflet.draw لود نشده یا مشکل داره!");
    return;
  }

  drawControl = new L.Control.Draw(drawOptions);
  map.addControl(drawControl);
  document.querySelector(".leaflet-draw").style.display = "none";

          map.on(L.Draw.Event.CREATED, function (e) {
          const layer = e.layer;
          editableLayers.addLayer(layer);
          if (activeDrawTool) {
            activeDrawTool.disable();
            activeDrawTool = null;
            window.activeDrawTool = null;
          }
          console.log(
            "مختصات لایه:",
            layer.getLatLngs ? layer.getLatLngs() : layer.getLatLng()
          );
          map.eachLayer(function (l) {
            if (l.editing) l.editing.disable();
          });
        });

          map.on(L.Draw.Event.DRAWSTOP, function (e) {
          console.log("ترسیم متوقف شد:", e.layerType);
          if (activeDrawTool) {
            activeDrawTool.disable();
            activeDrawTool = null;
            window.activeDrawTool = null;
          }
        });

  map.on(L.Draw.Event.DRAWSTART, function (e) {
    console.log("ترسیم شروع شد:", e.layerType);
  });

          map.on(L.Draw.Event.DELETED, function (e) {
          console.log("عارضه حذف شد");
          if (activeDrawTool) {
            activeDrawTool.disable();
            activeDrawTool = null;
            window.activeDrawTool = null;
          }
          map.eachLayer(function (l) {
            if (l.editing) l.editing.disable();
          });
        });

  overlayLayers = {};
  // تابع ساخت متن جستجو فقط از پراپرتی‌های قابل نمایش در tooltip
  // اول پراپرتی‌های متنی، بعد عددی
  function buildSearchText(properties, layerConfig) {
    try {
      if (!properties || !layerConfig || !layerConfig.properties) return "";
      const textParts = [];
      const numericParts = [];

      // جدا کردن پراپرتی‌های متنی و عددی
      layerConfig.properties.forEach(function(prop) {
        if (prop.info !== false && properties[prop.value]) {
          const val = properties[prop.value];
          if (val !== null && val !== undefined && val.toString().trim() !== '') {
            // بررسی نوع پراپرتی
            const isNumeric = prop.filter && (prop.filter.type === 'integer' || prop.filter.type === 'number');

            if (isNumeric) {
              numericParts.push(String(val));
            } else {
              textParts.push(String(val));
            }
          }
        }
      });

      // ترکیب: اول متنی‌ها، بعد عددی‌ها
      const allParts = [...textParts, ...numericParts];
      return allParts.join(" | ");
    } catch (err) {
      console.warn("خطا در ساخت متن جستجو:", err);
      return "";
    }
  }


  // بر اساس فیلد marker-color برای لایه‌های نقطه‌ای داره رنگ‌آمیزی می‌کنه
  config_getLayersConfig().forEach((layerConfig, index) => {
    const layer = L.geoJson(null, {
      filter: function (feature) {
        return (
          feature.geometry.coordinates[0] !== 0 &&
          feature.geometry.coordinates[1] !== 0
        );
      },
      pointToLayer: function (feature, latlng) {
        // اگر برای لایه نماد سفارشی تعریف شده باشد از Font Awesome استفاده می‌کنیم
        const configuredMarker = layerConfig.marker;
        const fallbackColor = (feature.properties && feature.properties["color"]) ? feature.properties["color"] : "#FF0000";
        if (configuredMarker && configuredMarker.type === "fa" && configuredMarker.class) {
          const iconColor = configuredMarker.color || fallbackColor;
          const iconSizePx = (configuredMarker.size && Number(configuredMarker.size)) || 20;
          const faIcon = L.divIcon({
            className: "fa-marker",
            html: `<i class="${configuredMarker.class}" style="color:${iconColor}; font-size:${iconSizePx}px;"></i>`,
            iconSize: [iconSizePx, iconSizePx],
            iconAnchor: [iconSizePx / 2, iconSizePx / 2],
          });
          const marker = L.marker(latlng, { icon: faIcon, layerName: layerConfig.name });
          marker.feature = feature;
          return marker;
        }
        // در غیر اینصورت از دایره پیش‌فرض استفاده می‌کنیم
        const markerColor = fallbackColor;
        const marker = L.circleMarker(latlng, {
          radius: 4,
          weight: 2,
          fillColor: markerColor,
          color: markerColor,
          opacity: 1,
          fillOpacity: 1,
          layerName: layerConfig.name,
        });
        marker.feature = feature;
        return marker;
      },
      style: function (feature) {
        const color = feature.properties && feature.properties["color"] ? feature.properties["color"] : "#FF0000";
        switch (feature.geometry.type) {
          case "LineString":
          case "MultiLineString":
            return {
              color: color,
              weight: 3,
              opacity: 1,
            };
          case "Polygon":
          case "MultiPolygon":
            return {
              color: color,
              weight: 1,
              opacity: 1,
              fillColor: color,
              fillOpacity: 0.5,
            };
          default:
            return {
              color: color,
              weight: 3,
              opacity: 1,
            };
        }
      },
      onEachFeature: function (feature, layer) {
        if (feature.properties) {
          // متنی برای جستجو فقط از پراپرتی‌های قابل نمایش بساز
          try {
            feature.properties._searchText = buildSearchText(feature.properties, layerConfig);
          } catch (e) {
            feature.properties._searchText = "";
          }
          layer.options.layerName = layerConfig.name;
          layer.on({
            click: function (e) {
              // اگر اندازه‌گیری فعال است، کلیک را نادیده بگیر
              if (window.isMeasuring || (window.measureControl && window.measureControl._locked)) {
                return;
              }

              console.log(
                "کلیک روی لایه:",
                layerConfig.name,
                "با ID:",
                L.stamp(layer)
              );
              identifyFeature(L.stamp(layer), layer);
              highlightLayer.clearLayers();
              highlightLayer.addData(layer.toGeoJSON());
            },
            mouseover: function (e) {
              if (layerConfig.config.hoverProperty) {
                $(".info-control").html(
                  feature.properties[layerConfig.config.hoverProperty]
                );
                $(".info-control").show();
              }
            },
            mouseout: function (e) {
              $(".info-control").hide();
            },
          });

          // برچسب‌گذاری عمومی بر اساس تنظیمات در config.json
          const labelCfg = layerConfig.config && layerConfig.config.label;
          // بررسی آیا چک‌باکس لیبل فعال است
          const layerIndex = config_getLayersConfig().findIndex(l => l.name === layerConfig.name);
          const labelCheckbox = document.getElementById(`label-${layerIndex}`);
          const isLabelEnabled = labelCheckbox ? labelCheckbox.checked : (labelCfg && labelCfg.enabled);

          if (labelCfg && isLabelEnabled && feature.properties) {

            // تابع کمکی برای اعمال رنگ لیبل
            const applyLabelColor = (tooltip, color) => {
              if (color && tooltip) {
                // اعمال رنگ به tooltip
                if (tooltip.getElement) {
                  const element = tooltip.getElement();
                  if (element) {
                    element.style.color = color;
                    element.style.fontWeight = 'bold';
                  }
                }
                // اعمال رنگ به tooltip options
                if (tooltip.options) {
                  tooltip.options.className = (tooltip.options.className || '') + ' custom-color-label';
                }
              }
            };
            const propName = labelCfg.property;
            const labelText = propName ? feature.properties[propName] : null;
            if (labelText !== undefined && labelText !== null && labelText !== "") {
              try {
                const direction = labelCfg.direction || "center";
                const defaultOffset = (function(){
                  switch(direction){
                    case "top": return [0, -6];
                    case "bottom": return [0, 6];
                    case "left": return [-6, 0];
                    case "right": return [6, 0];
                    default: return [0, 0];
                  }
                })();
                const configuredOffset = (Array.isArray(labelCfg.offset) && labelCfg.offset.length === 2)
                  ? labelCfg.offset
                  : defaultOffset;

                 // بررسی آیا لیبل باید در امتداد مسیر باشد
                 if (labelCfg.followPath && (feature.geometry.type === "LineString" || feature.geometry.type === "MultiLineString")) {
                   // برای خطوط، لیبل را در وسط مسیر قرار می‌دهیم
                   const latlngs = feature.geometry.type === "LineString" ? feature.geometry.coordinates : feature.geometry.coordinates[0];
                   const midPoint = Math.floor(latlngs.length / 2);
                   const centerLatLng = L.latLng(latlngs[midPoint][1], latlngs[midPoint][0]);

                   const pathLabel = L.tooltip({
                     permanent: true,
                     direction: "center",
                     className: labelCfg.className || "layer-label",
                     sticky: false,
                     offset: L.point(0, 0)
                   }).setContent(String(labelText));

                   // اعمال رنگ لیبل از کانفیگ
                   applyLabelColor(pathLabel, labelCfg.color);

                   pathLabel.setLatLng(centerLatLng);
                   layer.bindTooltip(pathLabel);
                 } else {
                   // برای نقاط و پلیگان‌ها، لیبل معمولی
                   const tooltipOptions = {
                  permanent: true,
                  direction: direction,
                  className: labelCfg.className || "layer-label",
                  sticky: false,
                  offset: L.point(configuredOffset[0], configuredOffset[1]),
                   };

                   const tooltip = layer.bindTooltip(String(labelText), tooltipOptions);

                   // اعمال رنگ لیبل از کانفیگ
                   applyLabelColor(tooltip, labelCfg.color);

                   // اعمال رنگ بعد از باز شدن tooltip (برای اطمینان)
                   tooltip.on('tooltipopen', function(e) {
                     applyLabelColor(e.tooltip, labelCfg.color);
                   });

                   tooltip.openTooltip();
                 }
              } catch (err) {
                console.warn("خطا در برچسب‌گذاری فیچر:", err);
              }
            }
          }

        }
      },
      zIndex: 10,
    });

    $.getJSON(layerConfig.geojson, function (data) {
      layer.addData(data);
      console.log(`لایه ${layerConfig.name} با موفقیت لود شد`);
      // پس از بارگذاری داده، اگر این لایه هدف جستجو است، ایندکس جستجو را به‌روزرسانی کن
      try {
        if (searchControl && currentSearchLayerName === layerConfig.name) {
          if (typeof searchControl.setLayer === "function") {
            searchControl.setLayer(layer);
          } else if (typeof searchControl.indexFeatures === "function") {
            searchControl._recordsCache = {};
            searchControl.indexFeatures();
          }
        }
      } catch (reidxErr) {
        console.warn("به‌روزرسانی ایندکس جستجو با خطا مواجه شد:", reidxErr);
      }
    }).fail(function (jqXHR, textStatus, errorThrown) {
      console.error(
        `خطا در بارگیری ${layerConfig.geojson}:`,
        textStatus,
        errorThrown
      );
    });

    featureLayers[layerConfig.name] = layer;
    overlayLayers[`<span>${layerConfig.name}</span>`] = layer;

    // افزودن اولیه لایه به نقشه بر اساس فیلد visible در config (اگر false نباشد)
    if (layerConfig.visible !== false) {
      map.addLayer(layer);
    }
  });

  // مقداردهی اولیه کنترل جستجو در ناوبار
  function getLayerByName(name) {
    return config_getFeatureLayers()[name];
  }

  function initSearchControl(layerName) {
    const targetLayer = getLayerByName(layerName);
    if (!targetLayer || !getMap()) return;

    // پاکسازی محتوای قبلی کانتینر
    const containerEl = document.getElementById("navbar-search-container");
    if (containerEl) containerEl.innerHTML = "";

    // حذف کنترل قبلی در صورت وجود
    if (searchControl) {
      try {
        getMap().removeControl(searchControl);
      } catch (e) {}
      searchControl = null;
    }

    currentSearchLayerName = layerName;
    searchControl = new L.Control.Search({
      layer: targetLayer,
      propertyName: "_searchText",
      marker: false,
      container: "navbar-search-container",
      textPlaceholder: "جستجو در " + layerName,
      collapsed: false,
      autoCollapse: false,
      initial: false,
      minLength: 1,
      tooltipLimit: 8, // کاهش تعداد نتایج
      delayType: 200, // کاهش تاخیر
      autoType: false,
      tipAutoSubmit: true,
      firstTipSubmit: false,
      autoResize: false,
      hideMarkerOnCollapse: true,
      zoom: 16,

      moveToLocation: function(latlng, title, map) {
        // زوم هوشمند بر اساس نوع عارضه
        try {
          const layer = this._layer;
          if (layer && layer.getBounds && typeof layer.getBounds === "function") {
            const bounds = layer.getBounds();
            if (bounds && bounds.isValid()) {
              map.fitBounds(bounds.pad(0.1), { maxZoom: 18 });
            } else if (layer.getLatLng) {
              map.setView(layer.getLatLng(), Math.max(map.getZoom(), 16));
            }
          } else if (layer && layer.getLatLng && typeof layer.getLatLng === "function") {
            map.setView(layer.getLatLng(), Math.max(map.getZoom(), 16));
          }
        } catch (err) {
          console.warn("خطا در زوم روی نتیجه جستجو:", err);
        }
      }
    });

    getMap().addControl(searchControl);

    // اضافه کردن tooltip به نتایج سرچ
    const originalShowTooltip = searchControl.showTooltip;
    searchControl.showTooltip = function(records) {
      const result = originalShowTooltip.call(this, records);
      setTimeout(function() {
        addTooltipToSearchResults();
      }, 50);
      return result;
    };

    // پاک کردن tooltip ها در زمان شروع جستجوی جدید
    searchControl.on("search:start", function (e) {
      clearSearchTooltips();
      clearMapTooltips();
      clearExternalTooltips();
    });

    // پاک کردن tooltip ها در زمان تایپ کردن در جستجو
    searchControl.on("search:input", function (e) {
      clearSearchTooltips();
      clearMapTooltips();
      clearExternalTooltips();
    });

    // پاک کردن tooltip ها در زمان بسته شدن نتایج جستجو
    searchControl.on("search:collapsed", function (e) {
      clearSearchTooltips();
      clearMapTooltips();
      clearExternalTooltips();
    });

    searchControl.on("search:locationfound", function (e) {
      const foundLayer = e.layer;
      const map = getMap();
      if (!foundLayer || !map) return;

      // پاک کردن tooltip های قبلی جستجو، نقشه و خارج از نقشه
      clearSearchTooltips();
      clearMapTooltips();
      clearExternalTooltips();

      // پاک کردن tooltip های لایه‌های feature
      try {
        const featureLayers = config_getFeatureLayers();
        Object.keys(featureLayers).forEach(function(layerName) {
          const layer = featureLayers[layerName];
          if (layer && layer.eachLayer) {
            layer.eachLayer(function(featureLayer) {
              try {
                if (featureLayer.getTooltip && featureLayer.getTooltip()) {
                  featureLayer.closeTooltip();
                  featureLayer.unbindTooltip();
                }
              } catch (err) {
                console.warn("خطا در پاک کردن tooltip feature:", err);
              }
            });
          }
        });
        console.log("Tooltip های لایه‌های feature پاک شدند");
      } catch (err) {
        console.warn("خطا در پاک کردن tooltip های feature:", err);
      }

      // پاک کردن مجدد tooltip ها با تاخیر برای اطمینان
      setTimeout(function() {
        clearMapTooltips();
        clearExternalTooltips();
      }, 100);

      // پاک کردن مجدد tooltip ها با تاخیر بیشتر
      setTimeout(function() {
        clearMapTooltips();
        clearExternalTooltips();
      }, 500);

      // زوم مناسب بر اساس نوع عارضه
      try {
        if (foundLayer.getBounds && typeof foundLayer.getBounds === "function") {
          const b = foundLayer.getBounds();
          if (b && b.isValid()) {
            map.fitBounds(b.pad(0.2));
          } else if (foundLayer.getLatLng) {
            map.setView(foundLayer.getLatLng(), Math.max(map.getZoom(), 16));
          }
        } else if (foundLayer.getLatLng && typeof foundLayer.getLatLng === "function") {
          map.setView(foundLayer.getLatLng(), Math.max(map.getZoom(), 16));
        }
      } catch (err) {
        console.warn("خطا در زوم روی نتیجه جستجو:", err);
      }

      // هایلایت کردن عارضه انتخابی
      try {
        getHighlightLayer().clearLayers();
        getHighlightLayer().addData(foundLayer.toGeoJSON());

        // پاک کردن tooltip های highlight layer
        getHighlightLayer().eachLayer(function(layer) {
          try {
            if (layer.getTooltip && layer.getTooltip()) {
              layer.closeTooltip();
              layer.unbindTooltip();
            }
          } catch (err) {
            console.warn("خطا در پاک کردن tooltip highlight layer:", err);
          }
        });
      } catch (err) {
        console.warn("خطا در هایلایت نتیجه جستجو:", err);
      }

      // در صورت وجود پاپ‌آپ آن را باز کن
      if (foundLayer.getPopup && foundLayer.getPopup()) {
        try { foundLayer.openPopup(); } catch (err) {}
      }
    });
  }

  // تابع پاک کردن tooltip های جستجو
  function clearSearchTooltips() {
    try {
      const searchTips = document.querySelectorAll('.search-tip');
      searchTips.forEach(function(tip) {
        if (tip.hasAttribute('data-bs-toggle')) {
          // غیرفعال کردن tooltip
          if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
            const tooltipInstance = bootstrap.Tooltip.getInstance(tip);
            if (tooltipInstance) {
              tooltipInstance.dispose();
            }
          } else if (typeof $ !== 'undefined' && $.fn.tooltip) {
            $(tip).tooltip('dispose');
          }
          // حذف attribute های tooltip
          tip.removeAttribute('data-bs-toggle');
          tip.removeAttribute('data-bs-placement');
          tip.removeAttribute('data-bs-html');
          tip.removeAttribute('title');
        }
      });
      console.log("Tooltip های جستجو پاک شدند");
    } catch (err) {
      console.warn("خطا در پاک کردن tooltip های جستجو:", err);
    }
  }

  // تابع پاک کردن tooltip های نقشه
  function clearMapTooltips() {
    try {
      const map = getMap();
      if (!map) return;

      // پاک کردن tooltip های Leaflet از تمام لایه‌ها
      map.eachLayer(function(layer) {
        try {
          // اگر لایه tooltip دارد
          if (layer.getTooltip && layer.getTooltip()) {
            layer.closeTooltip();
            layer.unbindTooltip();
          }

          // اگر لایه feature دارد و tooltip دارد
          if (layer.feature && layer.getTooltip) {
            layer.closeTooltip();
            layer.unbindTooltip();
          }

          // اگر لایه tooltip options دارد
          if (layer.options && layer.options.tooltip) {
            layer.closeTooltip();
            layer.unbindTooltip();
          }
        } catch (err) {
          console.warn("خطا در پاک کردن tooltip لایه:", err);
        }
      });

      // پاک کردن tooltip های DOM که ممکن است روی نقشه باقی مانده باشند
      const searchContainer = map.getContainer();

      // پاک کردن تمام tooltip های Leaflet
      const leafletTooltips = searchContainer.querySelectorAll('.leaflet-tooltip');
      leafletTooltips.forEach(function(tooltip) {
        try {
          tooltip.remove();
        } catch (err) {
          console.warn("خطا در حذف tooltip از DOM:", err);
        }
      });

      // پاک کردن tooltip های Bootstrap
      const bootstrapTooltips = searchContainer.querySelectorAll('[data-bs-toggle="tooltip"]');
      bootstrapTooltips.forEach(function(tooltip) {
        try {
          if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
            const tooltipInstance = bootstrap.Tooltip.getInstance(tooltip);
            if (tooltipInstance) {
              tooltipInstance.dispose();
            }
          }
        } catch (err) {
          console.warn("خطا در پاک کردن tooltip Bootstrap:", err);
        }
      });

      // پاک کردن tooltip های jQuery
      const jqueryTooltips = searchContainer.querySelectorAll('[data-toggle="tooltip"]');
      jqueryTooltips.forEach(function(tooltip) {
        try {
          if (typeof $ !== 'undefined' && $.fn.tooltip) {
            $(tooltip).tooltip('dispose');
          }
        } catch (err) {
          console.warn("خطا در پاک کردن tooltip jQuery:", err);
        }
      });

      // پاک کردن tooltip های عمومی
      const allTooltips = searchContainer.querySelectorAll('[title]');
      allTooltips.forEach(function(element) {
        try {
          element.removeAttribute('title');
          element.removeAttribute('data-bs-toggle');
          element.removeAttribute('data-toggle');
        } catch (err) {
          console.warn("خطا در پاک کردن tooltip عمومی:", err);
        }
      });

      console.log("Tooltip های نقشه پاک شدند");
    } catch (err) {
      console.warn("خطا در پاک کردن tooltip های نقشه:", err);
    }
  }

  // تابع پاک کردن tooltip های خارج از نقشه (مثل tooltip های جستجو)
  function clearExternalTooltips() {
    try {
      // پاک کردن tooltip های Bootstrap از کل صفحه
      const allBootstrapTooltips = document.querySelectorAll('[data-bs-toggle="tooltip"]');
      allBootstrapTooltips.forEach(function(tooltip) {
        try {
          if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
            const tooltipInstance = bootstrap.Tooltip.getInstance(tooltip);
            if (tooltipInstance) {
              tooltipInstance.dispose();
            }
          }
        } catch (err) {
          console.warn("خطا در پاک کردن tooltip Bootstrap خارجی:", err);
        }
      });

      // پاک کردن tooltip های jQuery از کل صفحه
      const allJqueryTooltips = document.querySelectorAll('[data-toggle="tooltip"]');
      allJqueryTooltips.forEach(function(tooltip) {
        try {
          if (typeof $ !== 'undefined' && $.fn.tooltip) {
            $(tooltip).tooltip('dispose');
          }
        } catch (err) {
          console.warn("خطا در پاک کردن tooltip jQuery خارجی:", err);
        }
      });

      // پاک کردن tooltip های عمومی از کل صفحه
      const allGeneralTooltips = document.querySelectorAll('[title]');
      allGeneralTooltips.forEach(function(element) {
        try {
          // فقط tooltip هایی که مربوط به جستجو هستند را پاک کن
          if (element.closest('.leaflet-control-search') ||
              element.closest('#navbar-search-container') ||
              element.classList.contains('search-tip')) {
            element.removeAttribute('title');
            element.removeAttribute('data-bs-toggle');
            element.removeAttribute('data-toggle');
          }
        } catch (err) {
          console.warn("خطا در پاک کردن tooltip عمومی خارجی:", err);
        }
      });

      // پاک کردن tooltip های CSS که ممکن است با z-index بالا نمایش داده شوند
      const cssTooltips = document.querySelectorAll('.tooltip, .bs-tooltip');
      cssTooltips.forEach(function(tooltip) {
        try {
          if (tooltip.style && tooltip.style.zIndex && parseInt(tooltip.style.zIndex) > 1000) {
            tooltip.remove();
          }
        } catch (err) {
          console.warn("خطا در پاک کردن tooltip CSS:", err);
        }
      });

      // پاک کردن tooltip های با z-index بالا از کل صفحه
      const highZIndexElements = document.querySelectorAll('*');
      highZIndexElements.forEach(function(element) {
        try {
          const zIndex = window.getComputedStyle(element).zIndex;
          if (zIndex && zIndex !== 'auto' && parseInt(zIndex) > 1000) {
            // بررسی آیا این element tooltip است
            if (element.classList.contains('tooltip') ||
                element.classList.contains('bs-tooltip') ||
                element.getAttribute('data-bs-toggle') === 'tooltip' ||
                element.getAttribute('data-toggle') === 'tooltip') {
              element.remove();
            }
          }
        } catch (err) {
          // نادیده گرفتن خطاها
        }
      });

      console.log("Tooltip های خارجی پاک شدند");
    } catch (err) {
      console.warn("خطا در پاک کردن tooltip های خارجی:", err);
    }
  }

  // تابع اضافه کردن tooltip به نتایج سرچ
  function addTooltipToSearchResults() {
    const searchTips = document.querySelectorAll('.search-tip');
    searchTips.forEach(function(tip) {
      // حذف tooltip قبلی در صورت وجود
      if (tip.hasAttribute('data-bs-toggle')) {
        return; // قبلاً tooltip اضافه شده
      }

      const tipText = tip.textContent || tip.innerText;
      const layerConfig = config_getLayersConfig().find(cfg => cfg.name === currentSearchLayerName);

      if (layerConfig && currentSearchLayerName) {
        const targetLayer = getLayerByName(currentSearchLayerName);
        if (targetLayer) {
          // پیدا کردن feature مربوطه
          let tooltipContent = '';
          targetLayer.eachLayer(function(layer) {
            if (layer.feature && layer.feature.properties &&
                layer.feature.properties._searchText &&
                layer.feature.properties._searchText.includes(tipText.split(':')[1]?.trim() || tipText)) {

              const props = layer.feature.properties;
              layerConfig.properties.forEach(prop => {
                if (prop.info !== false && props[prop.value]) {
                  const value = props[prop.value];
                  if (value && value.toString().trim() !== '') {
                    tooltipContent += `${prop.label}: ${value}\n`;
                  }
                }
              });
              return false; // خروج از حلقه
            }
          });

          if (tooltipContent) {
            // اضافه کردن tooltip ساده
            tip.setAttribute('title', tooltipContent.trim());

            // فعال‌سازی tooltip ساده
            if (typeof $ !== 'undefined' && $.fn.tooltip) {
              $(tip).tooltip({
                html: false,
                placement: 'right',
                container: 'body'
              });
            }
          }
        }
      }
    });
  }

  // پر کردن کمبوباکس لایه جستجو در ناوبار و ست اولیه
  const navbarSelect = document.getElementById("navbar-layer-select");
  if (navbarSelect) {
    // بازسازی گزینه‌ها
    navbarSelect.innerHTML = '<option value="" disabled selected>انتخاب لایه جستجو</option>';
    
    config_getLayersConfig().forEach(function (cfg) {
      // حذف لایه‌های "مرز شهر سهند" و "مرز شهر اسکو" از لیست جستجو
      if (cfg.name !== "مرز شهر سهند" && cfg.name !== "مرز شهر اسکو") {
        const opt = document.createElement("option");
        opt.value = cfg.name;
        opt.textContent = cfg.name;
        navbarSelect.appendChild(opt);
      }
    });

    // انتخاب پیش‌فرض: اولین لایه (یا اولین لایه visible) - بدون لایه‌های مرز
    let defaultLayerName = null;
    const firstVisible = config_getLayersConfig().find(function (c) { 
      return c.visible !== false && c.name !== "مرز شهر سهند" && c.name !== "مرز شهر اسکو"; 
    });
    defaultLayerName = (firstVisible && firstVisible.name) || 
                      config_getLayersConfig().find(function (c) { 
                        return c.name !== "مرز شهر سهند" && c.name !== "مرز شهر اسکو"; 
                      })?.name || null;
    
    // اگر لایه پیش‌فرض پیدا شد، کنترل جستجو رو راه‌اندازی کن
    if (defaultLayerName && defaultLayerName !== "مرز شهر سهند" && defaultLayerName !== "مرز شهر اسکو") {
      // تاخیر کوتاه برای اطمینان از لود شدن کامل
      setTimeout(function() {
        // تغییر عنوان placeholder به نام لایه انتخاب شده
        const placeholderOption = navbarSelect.querySelector('option[value=""]');
        if (placeholderOption) {
          placeholderOption.textContent = defaultLayerName;
          placeholderOption.value = defaultLayerName;
          placeholderOption.selected = true;
        }
        initSearchControl(defaultLayerName);
      }, 100);
    } else {
      // اگر لایه پیش‌فرضی نیست یا لایه‌های مرز هستند، فقط placeholder رو نگه دار
      const placeholderOption = navbarSelect.querySelector('option[value=""]');
      if (placeholderOption) {
        placeholderOption.textContent = "انتخاب لایه جستجو";
        placeholderOption.selected = true;
      }
    }

    navbarSelect.onchange = function () {
      const name = this.value;
      if (name) initSearchControl(name);
    };
  }

  baseLayers = {
    "تصویر گوگل": googlesat,
    "اوپن استریت": osm,
    "گوگل مپ": googlemap,
    "بدون تصویر": noBasemap,
  };

  L.control
    .scale({
      metric: true,
      imperial: false,
      position: "bottomleft",
    })
    .addTo(map);

  L.control
    .locate({
      position: "topleft",
      drawCircle: true,
      follow: true,
      setView: "always",
      keepCurrentZoomLevel: false,
      markerClass: L.circleMarker,
      circleStyle: {},
      markerStyle: {},
      followCircleStyle: {},
      followMarkerStyle: {},
      stopFollowingOnDrag: false,
      locateOptions: {
        maxZoom: 16,
        watch: true,
        enableHighAccuracy: false,
      },
    })
    .addTo(map);

  L.Control.Watermark = L.Control.extend({
    onAdd: function (map) {
      var img = L.DomUtil.create("img");
      img.src = "./images/logo.png";
      img.style.width = "100px";
      return img;
    },
  });

  L.control.watermark = function (opts) {
    return new L.Control.Watermark(opts);
  };

  L.control.watermark({ position: "bottomleft" }).addTo(map);

  // var geocoder = new L.Control.Geocoder.Nominatim({
  //   geocodingQueryParams: { countrycodes: "IR" },
  // });
  // L.Control.geocoder({
  //   geocoder: geocoder,
  //   position: "topleft",
  //   collapsed: true,
  //   placeholder: "جستجو...",
  //   defaultMarkGeocode: true,
  // }).addTo(map);

  const info = L.control({ position: "bottomleft" });
  info.onAdd = function () {
    this._div = L.DomUtil.create("div", "info-control");
    this.update();
    return this._div;
  };
  info.update = function () {
    this._div.innerHTML = "";
  };
  info.addTo(map);
  $(".info-control").hide();

  map.on("click", function () {
    // اگر اندازه‌گیری فعال است، کلیک را نادیده بگیر
    if (window.isMeasuring || (window.measureControl && window.measureControl._locked)) {
      return;
    }
    getHighlightLayer().clearLayers();
  });

  function activateBoxZoom(mode) {
    zoomMode = mode;
    map.dragging.disable();
    document.body.classList.add("no-select");
    map.getContainer().classList.add(
      mode === "in" ? "zoom-in-cursor" : "zoom-out-cursor"
    );
  }

  function deactivateBoxZoom() {
    zoomMode = null;
    map.dragging.enable();
    document.body.classList.remove("no-select");
    map.getContainer().classList.remove("zoom-in-cursor", "zoom-out-cursor");
  }

  map.on("mousedown", function (e) {
    if (!zoomMode) return;
    if (!map.getContainer().contains(e.originalEvent.target)) return;

    startPoint = e.containerPoint;

    boxDiv = L.DomUtil.create("div", "leaflet-zoom-box", map.getContainer());
    boxDiv.style.left = startPoint.x + "px";
    boxDiv.style.top = startPoint.y + "px";
    boxDiv.style.width = "0px";
    boxDiv.style.height = "0px";

    map.on("mousemove", onMouseMove);
    map.on("mouseup", onMouseUp);
  });

  function onMouseMove(e) {
    if (!startPoint || !boxDiv) return;
    var endPoint = e.containerPoint;

    var left = Math.min(startPoint.x, endPoint.x);
    var top = Math.min(startPoint.y, endPoint.y);
    var width = Math.abs(startPoint.x - endPoint.x);
    var height = Math.abs(startPoint.y - endPoint.y);

    boxDiv.style.left = left + "px";
    boxDiv.style.top = top + "px";
    boxDiv.style.width = width + "px";
    boxDiv.style.height = height + "px";
  }

  function onMouseUp(e) {
    if (!startPoint || !boxDiv) return;

    var bounds = L.latLngBounds(
      map.containerPointToLatLng(startPoint),
      map.containerPointToLatLng(e.containerPoint)
    );

    if (zoomMode === "in") {
      map.fitBounds(bounds);
    } else if (zoomMode === "out") {
      var currentZoom = map.getZoom();
      var newZoom = Math.max(currentZoom - 2, 1);
      map.setView(bounds.getCenter(), newZoom);
    }

    L.DomUtil.remove(boxDiv);
    boxDiv = null;
    startPoint = null;

    map.off("mousemove", onMouseMove);
    map.off("mouseup", onMouseUp);

    deactivateBoxZoom();
  }

  L.Control.CustomBoxZoom = L.Control.extend({
    onAdd: function (map) {
      var container = L.DomUtil.create("div", "leaflet-control leaflet-bar");

      var zoomInBtn = L.DomUtil.create(
        "a",
        "leaflet-control-box-zoom-in",
        container
      );
      zoomInBtn.innerHTML = '<i class="fas fa-search-plus"></i>';
      zoomInBtn.href = "#";
      zoomInBtn.title = "بزرگنمایی";

      var zoomOutBtn = L.DomUtil.create(
        "a",
        "leaflet-control-box-zoom-out",
        container
      );
      zoomOutBtn.innerHTML = '<i class="fas fa-search-minus"></i>';
      zoomOutBtn.href = "#";
      zoomOutBtn.title = "کوچک نمایی";

      L.DomEvent.on(zoomInBtn, "click", function (e) {
        L.DomEvent.stopPropagation(e);
        L.DomEvent.preventDefault(e);
        activateBoxZoom("in");
      });

      L.DomEvent.on(zoomOutBtn, "click", function (e) {
        L.DomEvent.stopPropagation(e);
        L.DomEvent.preventDefault(e);
        activateBoxZoom("out");
      });

      return container;
    },
  });

  new L.Control.CustomBoxZoom({ position: "topleft" }).addTo(map);

  L.control.navbar().addTo(map);

  printControl = L.control
    .browserPrint({ position: "topleft", title: "خروجی " })
    .addTo(map);
  document.querySelector(".leaflet-control-browser-print").style.display =
    "none";

  const { default: mousePosition } = await __webpack_require__.e(/* import() */ 392).then(__webpack_require__.bind(__webpack_require__, 392));
  map.addControl(mousePosition({
    numDigits: 5,    // 5 رقم اعشار برای مختصات جغرافیایی
    utmDigits: 5     // 5 رقم اعشار برای مختصات UTM
  }));

  sidebar = L.control
    .sidebar({
      container: "sidebar",
      position: "right",
      autopan: true,
    })
    .addTo(map);

  sidebar.addPanel({
      id: "maps",
      tab: '<i class="fas fa-layer-group"></i>',
      title: "نقشه‌ها",
      pane: `
        <div class="large-content">
          <h2 style="color: gray;">نقشه های موضوعی</h2>
          <!-- اضافه کردن چک‌باکس برای نمایش همه لایه‌ها -->
          <div class="form-group mb-3">
            <label>
              <input type="checkbox" id="toggle-all-layers" onchange="toggleAllLayers(this.checked)">
               <span style="font-size: 2.7em; color: brown;">خاموش کردن همه</span>
            </label>
          </div>
          <div style="font-size: 16px;" id="layer-checkboxes">${generateLayerCheckboxes()}</div>
        </div>
      `,
    });

  sidebar.addPanel({
    id: "basemaps",
    tab: '<i class="fas fa-satellite satellite-icon"></i>',
    title: "تصاویر ماهواره",
    pane: `
      <div class="large-content">
        <h2 style="color: gray;">تصاویر ماهواره</h2>
        <div style="font-size: 16px;">
          <input type="radio" id="base-googlesat" name="base-layer" value="googlesat" onchange="window.switchBaseLayer('googlesat')">
            <label for="base-googlesat" class="large-text">تصویر گوگل</label><br>
          <input type="radio" id="base-googlemap" name="base-layer" value="googlemap" onchange="window.switchBaseLayer('googlemap')">
             <label for="base-googlemap" class="large-text">گوگل مپ</label><br>
          <input type="radio" id="base-osm" name="base-layer" value="osm" checked onchange="window.switchBaseLayer('osm')">
             <label for="base-osm" class="large-text">اوپن استریت</label><br>
          <input type="radio" id="base-nobasemap" name="base-layer" value="nobasemap" onchange="window.switchBaseLayer('nobasemap')">
             <label for="base-nobasemap" class="large-text">بدون تصویر</label>
        </div>
      </div>
    `,
  });

  sidebar.addPanel({
    id: "bookmarks",
    tab: '<i class="fas fa-bookmark"></i>',
    title: "بوک مارک",
    pane: `<div id="bookmarks-container" class="large-content">
             <h2>بوکمارک‌ها</h2>
             <button id="add-bookmark-btn" class="btn btn-primary">اضافه کردن بوکمارک</button>
             <div id="bookmarks-list"></div>
           </div>`,
  });

  sidebar.addPanel({
    id: "descriptive-search",
    tab: '<i class="fa fa-table"></i>',
    title: "اطلاعات توصیفی",
    pane: `
      <div class="large-content">
        <h2>اطلاعات توصیفی</h2>
        <div style="margin-top: 10px;">
          <label for="layer-dropdown" style="font-size: 16px;">انتخاب لایه:</label>
          <select id="layer-dropdown" style="width: 100%; padding: 5px; font-size: 16px;">
            <option value="" disabled selected>لایه را انتخاب کنید</option>
          </select>
        </div>
        <div id="layer-table-container" style="margin-top: 20px;"></div>
      </div>
    `,
  });


  sidebar.addPanel({
    id: "coordinates",
    tab: '<i class="fas fa-map-marker-alt"></i>',
    title: "مختصات",
    pane: `
      <div class="large-content">
        <h2>مبدل مختصات</h2>
        <form id="latlng-to-utm" class="coordinate-form">
          <div class="form-group">
            <label>عرض جغرافیایی (Latitude):</label>
            <input type="text" id="lat" value="0" size="10">
          </div>
          <div class="form-group">
            <label>طول جغرافیایی (Longitude):</label>
            <input type="text" id="lng" value="0" size="10">
          </div>
          <button type="button" id="to-utm-btn" class="btn-coordinate">تبدیل به UTM</button>
          <p>نتیجه UTM: <span id="utm-result">---</span></p>
        </form>
        <div class="form-spacer"></div>
        <form id="utm-to-latlng" class="coordinate-form">
          <div class="form-group">
            <label>عرض (X):</label>
            <input type="text" id="utm-x" value="" size="9">
          </div>
          <div class="form-group">
            <label>طول (Y):</label>
            <input type="text" id="utm-y" value="" size="9">
          </div>
          <div class="form-group">
            <label>زون (Zone):</label>
            <input type="text" id="utm-zone" value="" size="2" maxlength="2">
          </div>
          <div class="form-group">
            <label>باند (Band):</label>
            <input type="text" id="utm-band" value="" size="2" maxlength="2">
          </div>
          <div class="form-group">
            <label>North Hemi (اختیاری):</label>
            <input type="text" id="utm-southhemi" value="" size="5" maxlength="5" title="فقط اگر Band خالی باشد">
          </div>
          <button type="button" id="to-latlng-btn" class="btn-coordinate">تبدیل به LatLng</button>
          <p>نتیجه LatLng: <span id="latlng-result">---</span></p>
        </form>
        <button type="button" id="pick-coordinate-btn" class="btn-coordinate btn-tool" title="انتخاب مختصات با کلیک">
          <i class="fas fa-crosshairs"></i> نمایش مختصات
        </button>
        <button type="button" id="delete-markers-btn" class="btn-coordinate btn-delete">حذف نشانگرها</button>
      </div>
    `,
  });

  sidebar.addPanel({
    id: "measure",
    tab: '<i class="fa fa-ruler"></i>',
    title: "اندازه‌گیری",
    pane: `
      <div class="large-content">
        <h2>ابزار اندازه‌گیری</h2>
        <div class="measure-tools">
          <button id="measure-start" title="شروع/پایان اندازه‌گیری" style="font-size: 24px;">
            <i class="fas fa-ruler-combined"></i>
          </button>
          <select id="unit-length" style="margin-left: 10px; font-size: 16px;">
            <option value="kilometers" selected>کیلومتر</option>
            <option value="meters">متر</option>
            <option value="miles">مایل</option>
          </select>
          <select id="unit-area" style="margin-left: 10px; font-size: 16px;">
            <option value="sqmeters" selected>متر مربع</option>
            <option value="sqkilometers">کیلومتر مربع</option>
            <option value="hectares">هکتار</option>
            <option value="sqmiles">مایل مربع</option>
          </select>
        </div>
        <p style="margin-top: 10px; font-size: 18px;">نکته: برای شروع، روی دکمه کلیک کنید و نقاط را روی نقشه انتخاب کنید. برای پایان، دابل‌کلیک کنید.</p>
        <div id="measure-results" style="margin-top: 10px; font-size: 16px;"></div>
      </div>
    `,
  });

  sidebar.addPanel({
    id: "draw",
    tab: '<i class="fa fa-pencil-alt"></i>',
    title: "ترسیم",
    pane: `
      <div class="large-content">
        <h2>ابزارهای ترسیم</h2>
        <div class="draw-tools">
          <button id="draw-polyline" title="خط"><i class="fas fa-slash"></i></button>
          <button id="draw-polygon" title="چندضلعی"><i class="fas fa-draw-polygon"></i></button>
          <button id="draw-circle" title="دایره"><i class="fas fa-circle"></i></button>
          <button id="draw-rectangle" title="مستطیل"><i class="fas fa-square"></i></button>
          <button id="draw-marker" title="نشانگر"><i class="fas fa-map-marker-alt"></i></button>
          <button id="edit-layers" title="ویرایش"><i class="fas fa-edit"></i></button>
          <button id="remove-layers" title="حذف"><i class="fas fa-trash"></i></button>
          <button id="save-edit" title="ذخیره ویرایش" style="display: none;"><i class="fas fa-save"></i></button>
        </div>
      </div>
    `,
  });

  sidebar.addPanel({
    id: "ruler",
    tab: '<i class="fas fa-ruler-horizontal"></i>',
    title: "خط‌کش",
    pane: `
      <div class="large-content">
        <h2>ابزار خط‌کش</h2>
        <div class="ruler-tools">
          <button id="ruler-start" title="شروع/پایان خط‌کش" style="font-size: 24px;">
            <i class="fas fa-ruler-horizontal"></i>
          </button>
        </div>
        <p style="margin-top: 10px; font-size: 18px;">نکته: برای شروع، روی دکمه کلیک کنید و نقاط را روی نقشه انتخاب کنید. برای پایان، دابل‌کلیک کنید.</p>
      </div>
    `,
  });


  sidebar.addPanel({
    id: "export",
    tab: '<i class="fa fa-print"></i>',
    title: "خروجی",
    pane: `
      <div class="large-content">
        <h2>ابزارهای خروجی</h2>
        <div class="print-tools">
          <button id="print-landscape" title="لندسکیپ"><i class="fas fa-arrows-alt-h"></i></button>
          <button id="print-portrait" title="پورتریت"><i class="fas fa-arrows-alt-v"></i></button>
          <button id="print-auto" title="خودکار"><i class="fas fa-sync-alt"></i></button>
          <button id="print-a4" title="A4"><i class="fas fa-file"></i></button>
          <button id="print-a3" title="A3"><i class="fas fa-file-alt"></i></button>
        </div>
      </div>
    `,
  });


  sidebar.addPanel({
    id: "filtering",
    tab: '<i class="fas fa-search"></i>',
    title: "پرس و جوی توصیفی",
    pane: `<div id="filter-container" class="large-content">
             <h2>جستجو بر اساس اطلاعات توصیفی ...  هنوز آماده نشده است</h2>
           </div>`,
    disabled: true,
  });

  sidebar.addPanel({
    id: "spatilaquery",
    tab: '<i class="fas fa-search-location"></i>',
    title: "پرس و جوی مکانی",
    pane: `<div id="spatial-container" class="large-content">
             <h2>جستجوی مکانی ...  هنوز آماده نشده است</h2>
           </div>`,
   disabled: true,
  });

  sidebar.addPanel({
    id: "charting",
    tab: '<i class="fas fa-bar-chart"></i>',
    title: "نمودار",
    pane: `<div id="charting-container" class="large-content">
             <h2>نمودارها هنوز آماده نشده است </h2>
           </div>`,
    disabled: true,
  });

  sidebar.addPanel({
    id: "user-panel",
    tab: '<i class="fas fa-user"></i>',
    title: "پنل کاربر",
    pane:
      '<div class="large-content"><p>این پنل در آینده فعال خواهد شد.</p></div>',
    //disabled: true,
  });

  sidebar.addPanel({
    id: "settings",
    tab: '<i class="fa fa-fw fa fa-cog"></i>',
    title: "تنظیمات",
    pane:
      '<div class="large-content"><p>این پنل در آینده فعال خواهد شد.</p></div>',
    position: "bottom",
    disabled: true,
  });

  L.Control.Measure.include({
    _setCaptureMarkerIcon: function () {
      this._captureMarker.options.autoPanOnFocus = false;
      this._captureMarker.setIcon(
        L.divIcon({
          iconSize: this._map.getSize().multiplyBy(2),
        })
      );
    },
  });

  measureControl = L.control.measure({
    position: "topright",
    primaryLengthUnit: "kilometers",
    secondaryLengthUnit: null,
    primaryAreaUnit: "sqmeters",
    secondaryAreaUnit: null,
    activeColor: "#ed3833",
    completedColor: "#63aabc",
    captureZIndex: 10000,
    showMeasurements: true,
    showSegmentLengths: true,
    popupOptions: {
      className: "leaflet-measure-resultpopup",
      autoPanPadding: [10, 10],
    },
  });
  measureControl.addTo(map);
  document.querySelector(".leaflet-control-measure").style.display = "none";

  rulerControl = L.control.ruler({
    position: "topright",
    circleMarker: {
      color: "green",
      radius: 5,
      fillColor: "yellow",
      fillOpacity: 0.8,
    },
    lineStyle: {
      color: "blue",
      weight: 3,
      dashArray: "5,5",
    },
    lengthUnit: {
      display: "کیلومتر",
      decimal: 2,
      factor: null,
      label: "فاصله:",
    },
    angleUnit: {
      display: "درجه",
      decimal: 2,
      factor: null,
      label: "زاویه:",
    },
  });
  map.addControl(rulerControl);
  document.querySelector(".leaflet-ruler").style.display = "none";

  measureLabels = L.layerGroup().addTo(map);

  const style = document.createElement("style");
  style.innerHTML = `
    .leaflet-measure-resultpopup {
      font-size: 16px !important;
    }
    .leaflet-measure-resultpopup span {
      font-size: 16px !important;
    }
    .measure-label {
      font-size: 16px !important;
      font-weight: bold;
      color: #000000;
      text-shadow: 1px 1px 2px #ffffff, -1px -1px 2px #ffffff;
      background: none;
      border: none;
      padding: 2px 5px;
    }
    .measure-area-label {
      font-size: 18px !important;
      font-weight: bold;
      color: #000000;
      text-shadow: 1px 1px 2px #ffffff, -1px -1px 2px #ffffff;
      background: none;
      border: none;
      padding: 2px 5px;
    }
    .layer-label {
      font-size: 12px;
      font-weight: 600;
      color: #111;
      text-shadow: 1px 1px 2px #fff;
      background: rgba(255,255,255,0.6);
      border-radius: 4px;
      padding: 1px 4px;
      border: 1px solid rgba(0,0,0,0.1);
    }

    /* اعمال رنگ‌های سفارشی برای لیبل‌ها */
    .layer-label[style*="color"] {
      text-shadow: 1px 1px 2px #fff, -1px -1px 2px #fff;
    }

    /* بهبود نمایش رنگ‌های سفارشی */
    .leaflet-tooltip {
      background: rgba(255,255,255,0.9);
      border: 1px solid rgba(0,0,0,0.2);
      border-radius: 4px;
      padding: 2px 6px;
      font-weight: 600;
      text-shadow: 1px 1px 2px #fff, -1px -1px 2px #fff;
    }

    /* استایل برای لیبل‌های با رنگ سفارشی */
    .custom-color-label {
      font-weight: bold !important;
      text-shadow: 1px 1px 2px #fff, -1px -1px 2px #fff !important;
    }

    /* بهبود نمایش لیبل‌های رنگی */
    .leaflet-tooltip.custom-color-label {
      background: rgba(255,255,255,0.95);
      border: 2px solid rgba(0,0,0,0.3);
      box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }

    /* استایل برای info-control (نمایش hoverProperty) */
    .info-control {
      background: rgba(255, 255, 255, 0.9) !important;
      border: 3px solid #dc3545 !important;
      border-radius: 4px !important;
      padding: 6px 10px !important;
      font-family: "Nahid", Tahoma, Geneva, Verdana, sans-serif !important;
      font-size: 14px !important;
      font-weight: bold !important;
      color: #333 !important;
      max-width: 250px !important;
      word-wrap: break-word !important;
    }

    /* استایل برای modal اطلاعات توصیفی با عکس */
    #featureModal .modal-body {
      max-height: 70vh !important;
      overflow-y: auto !important;
    }

    #featureModal .modal-body img {
      max-width: 100% !important;
      max-height: 300px !important;
      width: auto !important;
      height: auto !important;
      object-fit: contain !important;
      transition: transform 0.2s ease !important;
    }

    #featureModal .modal-body img:hover {
      transform: scale(1.05) !important;
    }

    /* راست‌چین کردن عناوین پراپرتی‌ها در جدول */
    #featureModal .modal-body table th {
      text-align: right !important;
      direction: rtl !important;
      font-family: "Nahid", Tahoma, Geneva, Verdana, sans-serif !important;
      background-color: #f8f9fa !important;
      font-weight: bold !important;
      padding: 8px 12px !important;
    }

    #featureModal .modal-body table td {
      text-align: right !important;
      direction: rtl !important;
      font-family: "Nahid", Tahoma, Geneva, Verdana, sans-serif !important;
      padding: 8px 12px !important;
    }

    /* استایل برای modal گالری تصاویر */
    #imageGalleryModal .modal-dialog {
      max-width: 90% !important;
    }

    #imageGalleryModal .carousel-inner {
      background: #f8f9fa;
      border-radius: 4px;
    }

    #imageGalleryModal .carousel-control {
      background: rgba(0,0,0,0.3) !important;
      width: 50px !important;
    }

    #imageGalleryModal .carousel-control:hover {
      background: rgba(0,0,0,0.5) !important;
    }

    #imageGalleryModal .carousel-indicators {
      bottom: -40px !important;
    }

    #imageGalleryModal .carousel-indicators li {
      background-color: #007bff !important;
      border: 1px solid #007bff !important;
    }

    #imageGalleryModal .carousel-indicators .active {
      background-color: #0056b3 !important;
    }

    /* استایل‌های جستجو حالا در app.css تعریف شده‌اند */
  `;
  document.head.appendChild(style);

// تابع کمکی برای ریست کامل اندازه‌گیری
function resetMeasureControl() {
  try {
    // اگر در حال اندازه‌گیری است، ابتدا آن را متوقف کنیم
    if (measureControl && measureControl._locked) {
      measureControl._finishMeasure();
    }

    // پاک کردن تمام لایه‌ها و حالت‌ها
    if (measureControl && measureControl._layer) {
      measureControl._layer.clearLayers();
    }

    // ریست کردن وضعیت measureControl
    if (measureControl && measureControl._latlngs) {
      measureControl._latlngs = [];
    }

    // فعال کردن مجدد تعاملات نقشه
    const map = getMap();
    if (map) {
      map.dragging.enable();
      map.scrollWheelZoom.enable();
      map.doubleClickZoom.enable();
      map.touchZoom.enable();
      map.boxZoom.enable();
    }

    // پاک کردن نتایج اندازه‌گیری
    if (measureLabels) {
      measureLabels.clearLayers();
    }

    // ریست کردن متغیرهای سراسری
    isMeasuring = false;
    window.isMeasuring = false;

    // پاک کردن نتایج از UI
    const resultsDiv = document.getElementById("measure-results");
    const toolbarResultsDiv = document.getElementById("toolbar-measure-results");
    if (resultsDiv) resultsDiv.innerHTML = "";
    if (toolbarResultsDiv) toolbarResultsDiv.innerHTML = "";

    console.log("اندازه‌گیری کاملاً ریست شد");
    return true;
  } catch (error) {
    console.error('خطا در ریست اندازه‌گیری:', error);

    // در صورت خطا، حداقل متغیرها را ریست کنیم
    isMeasuring = false;
    window.isMeasuring = false;
    return false;
  }
}

// در دسترس قرار دادن تابع برای سایر فایل‌ها
window.resetMeasureControl = resetMeasureControl;
  let isMeasuring = false;
let selectedLengthUnit = "kilometers";
let selectedAreaUnit = "sqmeters";
let finalShape = null;
let isRulerActive = false;

// متغیرهای سراسری برای نوار تولبار
window.isMeasuring = isMeasuring;
window.selectedLengthUnit = selectedLengthUnit;
window.selectedAreaUnit = selectedAreaUnit;
window.isRulerActive = isRulerActive;
window.measureControl = measureControl;
window.rulerControl = rulerControl;
window.activeDrawTool = activeDrawTool;
window.editableLayers = editableLayers;
window.printControl = printControl;
window.coordinateMarkers = coordinateMarkers;
window.bookmarkControl = bookmarkControl;
window.bookmarkMarkers = bookmarkMarkers;

  function updateBookmarksList(container, control) {
    if (!control || !control._storage) {
      console.error("کنترل یا _storage تعریف نشده است!");
      container.innerHTML = "<p>خطا: بوکمارک‌ها لود نشدند.</p>";
      return;
    }

    control._storage.getAllItems(function (bookmarks) {
      container.innerHTML = "";
      if (!bookmarks || bookmarks.length === 0) {
        container.innerHTML = "<p>هیچ بوکمارکی وجود ندارد.</p>";
        return;
      }

      const ul = document.createElement("ul");
      bookmarks.forEach(function (bookmark) {
        const li = document.createElement("li");
        li.innerHTML = `
          <span>${bookmark.name || "بدون نام"}</span>
          <span>(${bookmark.latlng[0].toFixed(6)}, ${bookmark.latlng[1].toFixed(6)})</span>
          <button class="zoom-to" data-lat="${bookmark.latlng[0]}" data-lng="${bookmark.latlng[1]}">بزرگ‌نمایی</button>
          <button class="remove" data-id="${bookmark.id}">حذف</button>
        `;
        ul.appendChild(li);

        if (!bookmarkMarkers.has(bookmark.id)) {
          const marker = L.marker([bookmark.latlng[0], bookmark.latlng[1]], {
            riseOnHover: true,
          }).addTo(getMap());
          bookmarkMarkers.set(bookmark.id, marker);
        }
      });
      container.appendChild(ul);

      container.querySelectorAll(".zoom-to").forEach(function (btn) {
        btn.onclick = function () {
          const lat = parseFloat(btn.dataset.lat);
          const lng = parseFloat(btn.dataset.lng);
          getMap().setView([lat, lng], 13);
        };
      });

      container.querySelectorAll(".remove").forEach(function (btn) {
        btn.onclick = function () {
          const id = btn.dataset.id;
          if (!id) {
            console.error("شناسه بوکمارک نامعتبر است!");
            return;
          }
          const marker = bookmarkMarkers.get(id);
          if (marker) {
            getMap().removeLayer(marker);
            bookmarkMarkers.delete(id);
          }
          const bookmarkToRemove = bookmarks.find((b) => b.id === id);
          if (bookmarkToRemove) {
            getMap().fire("bookmark:remove", { data: bookmarkToRemove });
          } else {
            console.warn("بوکمارک برای حذف پیدا نشد!");
            getMap().fire("bookmark:removed", { bookmark: { id } });
          }
        };
      });
    });
  }

  sidebar.on("content", function (ev) {
    switch (ev.id) {
      case "measure":
        const startButton = document.getElementById("measure-start");
        startButton.style.fontSize = "24px";

        startButton.onclick = function () {
          if (!isMeasuring) {
            measureControl.options.primaryLengthUnit = selectedLengthUnit;
            measureControl.options.primaryAreaUnit = selectedAreaUnit;
            measureControl._startMeasure();
            isMeasuring = true;
            window.isMeasuring = true;
            console.log("اندازه‌گیری شروع شد از سایدبار");
          } else {
            try {
              // اگر در حال اندازه‌گیری است، ابتدا آن را متوقف کنیم
              if (measureControl._locked) {
                measureControl._finishMeasure();
              }

              // پاک کردن تمام لایه‌ها و حالت‌ها
              if (measureControl._layer) {
                measureControl._layer.clearLayers();
              }

              // ریست کردن وضعیت measureControl
              if (measureControl._latlngs) {
                measureControl._latlngs = [];
              }

              // فعال کردن مجدد تعاملات نقشه
              map.dragging.enable();
              map.scrollWheelZoom.enable();
              map.doubleClickZoom.enable();
              map.touchZoom.enable();
              map.boxZoom.enable();

              // پاک کردن نتایج اندازه‌گیری
              if (measureLabels) {
                measureLabels.clearLayers();
              }

              // ریست کردن متغیرهای سراسری
              isMeasuring = false;
              window.isMeasuring = false;

              // پاک کردن نتایج
              const resultsDiv = document.getElementById("measure-results");
              const toolbarResultsDiv = document.getElementById("toolbar-measure-results");
              if (resultsDiv) resultsDiv.innerHTML = "";
              if (toolbarResultsDiv) toolbarResultsDiv.innerHTML = "";

              console.log("اندازه‌گیری کاملاً متوقف شد از سایدبار");
            } catch (error) {
              console.error('خطا در متوقف کردن اندازه‌گیری:', error);

              // در صورت خطا، حداقل متغیرها را ریست کنیم
              isMeasuring = false;
              window.isMeasuring = false;
            }
          }
        };

        document.getElementById("unit-length").onchange = function () {
          selectedLengthUnit = this.value;
          window.selectedLengthUnit = selectedLengthUnit;
          measureControl.options.primaryLengthUnit = selectedLengthUnit;
          console.log("واحد طول تغییر کرد به:", selectedLengthUnit);
        };

        document.getElementById("unit-area").onchange = function () {
          selectedAreaUnit = this.value;
          window.selectedAreaUnit = selectedAreaUnit;
          measureControl.options.primaryAreaUnit = selectedAreaUnit;
          console.log("واحد مساحت تغییر کرد به:", selectedAreaUnit);
        };
        break;

      case "draw":
          if (activeDrawTool) {
            activeDrawTool.disable();
            activeDrawTool = null;
            window.activeDrawTool = null;
          }

          const saveButton = document.getElementById("save-edit");

          document.getElementById("draw-polyline").onclick = function () {
            const map = getMap(); // استفاده از getMap به جای map مستقیم
            if (!map) {
              console.error("نقشه هنوز آماده نیست، لطفاً صبر کنید");
              return;
            }
            if (activeDrawTool) activeDrawTool.disable();
            activeDrawTool = new L.Draw.Polyline(map, drawOptions.draw.polyline);
            window.activeDrawTool = activeDrawTool;
            activeDrawTool.enable();
            saveButton.style.display = "none";
          };

          document.getElementById("draw-polygon").onclick = function () {
            const map = getMap();
            if (!map) {
              console.error("نقشه هنوز آماده نیست، لطفاً صبر کنید");
              return;
            }
            if (activeDrawTool) activeDrawTool.disable();
            activeDrawTool = new L.Draw.Polygon(map, drawOptions.draw.polygon);
            window.activeDrawTool = activeDrawTool;
            activeDrawTool.enable();
            saveButton.style.display = "none";
          };

          document.getElementById("draw-circle").onclick = function () {
            const map = getMap();
            if (!map) {
              console.error("نقشه هنوز آماده نیست، لطفاً صبر کنید");
              return;
            }
            if (activeDrawTool) activeDrawTool.disable();
            activeDrawTool = new L.Draw.Circle(map, drawOptions.draw.circle);
            window.activeDrawTool = activeDrawTool;
            activeDrawTool.enable();
            saveButton.style.display = "none";
          };

          document.getElementById("draw-rectangle").onclick = function () {
            const map = getMap();
            if (!map) {
              console.error("نقشه هنوز آماده نیست، لطفاً صبر کنید");
              return;
            }
            if (activeDrawTool) activeDrawTool.disable();
            activeDrawTool = new L.Draw.Rectangle(map, drawOptions.draw.rectangle);
            window.activeDrawTool = activeDrawTool;
            activeDrawTool.enable();
            saveButton.style.display = "none";
          };

          document.getElementById("draw-marker").onclick = function () {
            const map = getMap();
            if (!map) {
              console.error("نقشه هنوز آماده نیست، لطفاً صبر کنید");
              return;
            }
            if (activeDrawTool) activeDrawTool.disable();
            activeDrawTool = new L.Draw.Marker(map, drawOptions.draw.marker);
            window.activeDrawTool = activeDrawTool;
            activeDrawTool.enable();
            saveButton.style.display = "none";
          };

          document.getElementById("edit-layers").onclick = function () {
            const map = getMap();
            if (!map) {
              console.error("نقشه هنوز آماده نیست، لطفاً صبر کنید");
              return;
            }
            if (activeDrawTool) activeDrawTool.disable();
            activeDrawTool = new L.EditToolbar.Edit(map, {
              featureGroup: editableLayers,
            });
            window.activeDrawTool = activeDrawTool;
            activeDrawTool.enable();
            saveButton.style.display = "inline-block";
          };

          document.getElementById("remove-layers").onclick = function () {
            const map = getMap();
            if (!map) {
              console.error("نقشه هنوز آماده نیست، لطفاً صبر کنید");
              return;
            }
            if (activeDrawTool) activeDrawTool.disable();
            activeDrawTool = new L.EditToolbar.Delete(map, {
              featureGroup: editableLayers,
            });
            window.activeDrawTool = activeDrawTool;
            activeDrawTool.enable();
            saveButton.style.display = "none";
                    map.once("click", function () {
          setTimeout(() => {
            if (activeDrawTool) {
              activeDrawTool.disable();
              activeDrawTool = null;
              window.activeDrawTool = null;
              console.log("ابزار حذف بعد از کلیک غیرفعال شد");
            }
          }, 100);
        });
          };

          document.getElementById("save-edit").onclick = function () {
            if (activeDrawTool) {
              activeDrawTool.save();
              activeDrawTool.disable();
              activeDrawTool = null;
              window.activeDrawTool = null;
              saveButton.style.display = "none";
              console.log("تغییرات ذخیره شد و ابزار غیرفعال شد");
            }
          };
          break;

      case "ruler":
        const rulerStartButton = document.getElementById("ruler-start");

        rulerStartButton.onclick = function () {
          if (!isRulerActive) {
            rulerControl._toggleMeasure();
            isRulerActive = true;
            window.isRulerActive = true;
            console.log("خط‌کش شروع شد از سایدبار");
          } else {
            rulerControl._toggleMeasure();
            isRulerActive = false;
            window.isRulerActive = false;
            console.log("خط‌کش متوقف شد از سایدبار");
          }
        };
        break;

      case "export":
        document.getElementById("print-landscape").onclick = function () {
          const mode = L.BrowserPrint.Mode.Landscape("A4", {
            title: "چاپ افقی",
            header: {
              enabled: true,
              text: "عنوان هدر فارسی",
              size: "10mm",
              overTheMap: false,
            },
            footer: {
              enabled: true,
              text: "عنوان فوتر فارسی",
              size: "10mm",
              overTheMap: false,
            },
          });
          printControl.browserPrint.print(mode);
          console.log("پرینت لندسکیپ انتخاب شد");
        };
        document.getElementById("print-portrait").onclick = function () {
          const mode = L.BrowserPrint.Mode.Portrait("A4", { title: "چاپ عمودی" });
          printControl.browserPrint.print(mode);
          console.log("پرینت پورتریت انتخاب شد");
        };
        document.getElementById("print-auto").onclick = function () {
          const mode = L.BrowserPrint.Mode.Auto("A4", { title: "چاپ خودکار" });
          printControl.browserPrint.print(mode);
          console.log("پرینت خودکار انتخاب شد");
        };
        document.getElementById("print-a4").onclick = function () {
          const mode = L.BrowserPrint.Mode.Auto("A4", { title: "چاپ A4" });
          printControl.browserPrint.print(mode);
          console.log("پرینت A4 انتخاب شد");
        };
        document.getElementById("print-a3").onclick = function () {
          const mode = L.BrowserPrint.Mode.Auto("A3", { title: "چاپ A3" });
          printControl.browserPrint.print(mode);
          console.log("پرینت A3 انتخاب شد");
        };
        break;

      case "coordinates":
          const latInput = document.getElementById("lat");
          const lngInput = document.getElementById("lng");
          const utmXInput = document.getElementById("utm-x");
          const utmYInput = document.getElementById("utm-y");
          const utmZoneInput = document.getElementById("utm-zone");
          const utmBandInput = document.getElementById("utm-band");
          const utmSouthHemiInput = document.getElementById("utm-southhemi");
          const utmResult = document.getElementById("utm-result");
          const latlngResult = document.getElementById("latlng-result");

          let isPickingCoordinates = false;

          // تابع کمکی برای غیرفعال کردن حالت انتخاب مختصات
          const disableCoordinatePicking = () => {
            isPickingCoordinates = false;
            const pickCoordinateBtn = document.getElementById("pick-coordinate-btn");
            pickCoordinateBtn.classList.remove("active");
            const map = getMap();
            map.getContainer().style.cursor = "";
            map.off("click", onMapClick);
            console.log("ابزار انتخاب مختصات غیرفعال شد");
          };

          document.getElementById("to-utm-btn").onclick = function () {
            utmResult.innerHTML = "---";
            const ll = L.latLng(latInput.value, lngInput.value);
            const utm = ll.utm();
            utmResult.innerHTML = `${utm.x.toFixed(1)}, ${utm.y.toFixed(1)}, Zone: ${utm.zone}, Band: ${utm.band}, Hemi: ${utm.southHemi ? "S" : "N"}`;
            utmXInput.value = utm.x.toFixed(1);
            utmYInput.value = utm.y.toFixed(1);
            utmZoneInput.value = utm.zone;
            utmBandInput.value = utm.band;
            utmSouthHemiInput.value = utm.southHemi ? "S" : "N";

            const map = getMap();
            const marker = L.marker(ll).addTo(coordinateMarkers);
            marker
              .bindPopup(`UTM: ${utm.toString()}<br>LatLng: ${ll}`)
              .openPopup();
            map.setView(ll, map.getZoom());
          };

          document.getElementById("to-latlng-btn").onclick = function () {
            latlngResult.innerHTML = "---";
            const sh = utmSouthHemiInput.value.toUpperCase();
            const southHemi = ["S", "TRUE", "Y", "YES", "1"].indexOf(sh) > -1;
            const utm = L.utm(
              utmXInput.value,
              utmYInput.value,
              utmZoneInput.value,
              utmBandInput.value,
              southHemi
            );
            const ll = utm.latLng();
            if (ll) {
              latlngResult.innerHTML = `Lat: ${ll.lat.toFixed(
                6
              )}, Lng: ${ll.lng.toFixed(6)}`;
              latInput.value = ll.lat.toFixed(6);
              lngInput.value = ll.lng.toFixed(6);

              const map = getMap();
              const marker = L.marker(ll).addTo(coordinateMarkers);
              marker
                .bindPopup(`UTM: ${utm.toString()}<br>LatLng: ${ll}`)
                .openPopup();
              map.setView(ll, map.getZoom());
            }
          };

          document.getElementById("delete-markers-btn").onclick = function () {
            coordinateMarkers.clearLayers();
            console.log("همه نشانگرها حذف شدند");
            // غیرفعال کردن حالت انتخاب مختصات بعد از حذف نشانگرها
            if (isPickingCoordinates) {
              disableCoordinatePicking();
            }
          };

          const pickCoordinateBtn = document.getElementById("pick-coordinate-btn");
          pickCoordinateBtn.onclick = function () {
            const map = getMap();
            if (!isPickingCoordinates) {
              isPickingCoordinates = true;
              pickCoordinateBtn.classList.add("active");
              map.getContainer().style.cursor = "crosshair";

              map.on("click", onMapClick);
              console.log("ابزار انتخاب مختصات فعال شد");
            } else {
              disableCoordinatePicking();
            }
          };

          function onMapClick(e) {
            // متوقف کردن انتشار رویداد به لایه‌های دیگر
            L.DomEvent.stopPropagation(e);

            const ll = e.latlng;
            const utm = ll.utm();
            const map = getMap();

            const marker = L.marker(ll).addTo(coordinateMarkers);
            marker
              .bindPopup(
                `UTM: ${utm.toString()}<br>LatLng: ${ll.lat.toFixed(6)}, ${ll.lng.toFixed(6)}`
              )
              .openPopup();

            console.log(
              `کلیک روی: LatLng: ${ll.lat}, ${ll.lng} | UTM: ${utm.toString()}`
            );

            // غیرفعال کردن حالت انتخاب مختصات بعد از زدن نقطه
            disableCoordinatePicking();
          }
          break;

      case "bookmarks":
        const map = getMap();
        if (!bookmarkControl) {
          console.error("کنترل بوکمارک تعریف نشده است!");
          document.getElementById("bookmarks-list").innerHTML =
            "<p>خطا: بوکمارک‌ها لود نشدند.</p>";
          return;
        }

        const bookmarksList = document.getElementById("bookmarks-list");
        updateBookmarksList(bookmarksList, bookmarkControl);

        const addBookmarkBtn = document.getElementById("add-bookmark-btn");
        if (addBookmarkBtn) {
          addBookmarkBtn.onclick = function () {
            const latlng = map.getCenter();
            map.fire("bookmark:new", { latlng });
          };
        }

        map.on("contextmenu", function (e) {
          const activeTab = document.querySelector(
            ".leaflet-sidebar-tabs .active"
          );
          if (activeTab && activeTab.getAttribute("data-panel") === "bookmarks") {
            map.fire("bookmark:new", { latlng: e.latlng });
          }
        });

        map.on(
          "bookmark:add bookmark:removed bookmark:remove",
          function (e) {
            console.log("رویداد دریافت شد:", e.type, e.data);
            updateBookmarksList(bookmarksList, bookmarkControl);
          }
        );
        break;

      case "user-panel":
          // دریافت نام کاربر از localStorage
          const username = localStorage.getItem("username") || "کاربر";
          const isAdmin = username === "admin"; // بررسی آیا کاربر ادمین است یا خیر

          // ایجاد محتوای پنل کاربر
          const userGreeting = isAdmin
            ? `<p style="color: green; font-size: 16px; font-weight: bold;">سلام ${username}</p>
               <p style="color: green; font-size: 14px;">کاربر ادمین</p>`
            : `<p style="font-size: 16px;">سلام ${username}</p>
               <p style="font-size: 14px;">کاربر عادی</p>`;

          // اضافه کردن محتوا به پنل کاربر
          const userPanelContent = `
            <div class="user-panel-content">
              ${userGreeting}
              <hr style="margin: 10px 0;">
              <p style="font-size: 14px;">امکانات کاربری:</p>
              <ul style="list-style-type: none; padding: 0;">
                <li><button id="change-password-btn" class = "change-password-btn">تغییر رمز عبور</button></li>
                <li><button id="logout-btn-sidebar" class ="logout-btn-sidebar">خروج از سیستم</button></li>
              </ul>
            </div>
          `;

          // اضافه کردن محتوا به پنل
          const userPanel = document.getElementById("user-panel");
          if (userPanel) {
            userPanel.innerHTML = userPanelContent;
          }

          // اضافه کردن رویداد به دکمه تغییر رمز عبور
          const changePasswordBtn = document.getElementById("change-password-btn");
          if (changePasswordBtn) {
            changePasswordBtn.onclick = function () {
              alert("این قابلیت در آینده اضافه خواهد شد.");
            };
          }

          // اضافه کردن رویداد به دکمه خروج از سیستم
          const logoutBtnSidebar = document.getElementById("logout-btn-sidebar");
          if (logoutBtnSidebar) {
            logoutBtnSidebar.onclick = function () {
              localStorage.removeItem("loggedIn");
              localStorage.removeItem("username");
              window.location.href = "/index.html";
            };
          }
          break;

      case "basemaps":
        // هماهنگ‌سازی انتخاب فعلی تصویر ماهواره هنگام باز شدن پنل
        setTimeout(() => {
          syncCurrentBasemapSelection();
        }, 100);
        console.log("پنل تصاویر ماهواره باز شد - هماهنگ‌سازی انجام شد");
        break;

    }
  });

  map.on("measurestart", function () {
    console.log("اندازه‌گیری شروع شد");
    // حذف غیرفعال‌سازی dragging و سایر قابلیت‌ها - کاربر باید بتواند نقشه را جابجا کند
    measureLabels.clearLayers();
    finalShape = null;

    // پاک کردن لیبل‌ها و شکل‌های موقت - حذف شده، toolbar مدیریت می‌کند

    // به‌روزرسانی متغیر سراسری - همگام با _locked
    window.isMeasuring = true;
    isMeasuring = true;

    // نمایش دکمه غیرفعال‌سازی در toolbar
    const measureStopBtn = document.getElementById('toolbar-measure-stop');
    if (measureStopBtn) measureStopBtn.style.display = "inline-block";

    // غیرفعال کردن سایر ابزارها
    if (window.isRulerActive && window.rulerControl) {
      window.rulerControl._toggleMeasure();
      window.isRulerActive = false;
      isRulerActive = false;
    }
  });

  // Event handler های اندازه‌گیری به toolbar منتقل شدند

  // Event handler کلیک اندازه‌گیری به toolbar منتقل شد

  map.on("measurefinish", function (e) {
    console.log("اندازه‌گیری پایان یافت");
    // حذف enable کردن‌های غیرضروری - این قابلیت‌ها هرگز غیرفعال نشده‌اند

    // به‌روزرسانی متغیرهای سراسری
    window.isMeasuring = false;
    window.isMeasuring = false;

    // مخفی کردن دکمه غیرفعال‌سازی در toolbar
    const measureStopBtn = document.getElementById('toolbar-measure-stop');
    if (measureStopBtn) measureStopBtn.style.display = "none";

    const resultsDiv = document.getElementById("measure-results");
    const toolbarResultsDiv = document.getElementById("toolbar-measure-results");
    let resultHTML = "<h3>نتایج اندازه‌گیری:</h3>";

    measureLabels.clearLayers();

    // پاک کردن لیبل‌ها و شکل‌های موقت - حذف شده، toolbar مدیریت می‌کند

    if (e.points && e.points.length > 1) {
      let totalLength = 0;
      finalShape = L.polygon(e.points, {
        color: "#63aabc",
        weight: 2,
        fillOpacity: 0.2,
      }).addTo(measureLabels);

      for (let i = 1; i < e.points.length; i++) {
        const latLng1 = e.points[i - 1];
        const latLng2 = e.points[i];
        const distance =
          latLng1.distanceTo(latLng2) /
          (selectedLengthUnit === "kilometers"
            ? 1000
            : selectedLengthUnit === "meters"
            ? 1
            : 1609.34);
        totalLength += distance;
        const midPoint = L.latLng(
          (latLng1.lat + latLng2.lat) / 2,
          (latLng1.lng + latLng2.lng) / 2
        );
        L.marker(midPoint, {
          icon: L.divIcon({
            className: "measure-label",
            html: `${distance.toFixed(2)} ${
              selectedLengthUnit === "kilometers"
                ? "کیلومتر"
                : selectedLengthUnit === "meters"
                ? "متر"
                : "مایل"
            }`,
          }),
        }).addTo(measureLabels);
      }

      if (e.points.length > 2) {
        const bounds = L.latLngBounds(e.points);
        const center = bounds.getCenter();
        const area =
          e.area /
          (selectedAreaUnit === "sqkilometers"
            ? 1e6
            : selectedAreaUnit === "sqmeters"
            ? 1
            : selectedAreaUnit === "hectares"
            ? 1e4
            : 2.59e6);
        L.marker(center, {
          icon: L.divIcon({
            className: "measure-area-label",
            html: `${area.toFixed(2)} ${
              selectedAreaUnit === "sqkilometers"
                ? "کیلومتر مربع"
                : selectedAreaUnit === "sqmeters"
                ? "متر مربع"
                : selectedAreaUnit === "hectares"
                ? "هکتار"
                : "مایل مربع"
            }`,
          }),
        }).addTo(measureLabels);
        resultHTML += `<p>طول کل: ${totalLength.toFixed(2)} ${
          selectedLengthUnit === "kilometers"
            ? "کیلومتر"
            : selectedLengthUnit === "meters"
            ? "متر"
            : "مایل"
        }</p>`;
        resultHTML += `<p>مساحت: ${area.toFixed(2)} ${
          selectedAreaUnit === "sqkilometers"
            ? "کیلومتر مربع"
            : selectedAreaUnit === "sqmeters"
            ? "متر مربع"
            : selectedAreaUnit === "hectares"
            ? "هکتار"
            : "مایل مربع"
        }</p>`;
      }
    }

    if (resultsDiv) resultsDiv.innerHTML = resultHTML;
    if (toolbarResultsDiv) toolbarResultsDiv.innerHTML = resultHTML;
    console.log("نتایج اندازه‌گیری:", e);

    if (e.points && e.points.length > 1) {
      const bounds = L.latLngBounds(e.points);
      const currentZoom = map.getZoom();
      map.fitBounds(bounds, {
        maxZoom: Math.max(currentZoom + 2, 15),
        padding: [50, 50],
      });
    }
  });

  map.on("popupopen", function (e) {
    const deleteLink = document.querySelector(".js-deletemarkup");
    if (deleteLink) {
      deleteLink.onclick = function () {
        measureLabels.clearLayers();
        document.getElementById("measure-results").innerHTML = "";
        console.log("محدوده حذف شد - نتایج ریست شد");
      };
    }
  });

  // رویداد کلیک برای آیتم‌های منوی دراپ‌داون
  document.addEventListener('click', function (e) {
    if (e.target && e.target.getAttribute('data-action') === 'descriptive-table') {
      const selectedIndex = parseInt(e.target.getAttribute('data-layer-index'), 10);
      console.log("کلیک روی جدول توصیفی از منو برای اندیس:", selectedIndex);
      const layerName = config_getLayersConfig()[selectedIndex].name;
      window.toggleLayer(layerName, true); // لایه رو فعال می‌کنه
      loadLayers(selectedIndex); // جدول رو لود می‌کنه
      console.log(`لایه ${layerName} از منوی دراپ‌داون انتخاب شد`);
      switchView("split"); // نمایش جدول و نقشه به‌صورت اسپلیت
      // به‌روزرسانی انتخاب در کمبو‌باکس (اختیاری)
      const layerDropdown = $('#layer-dropdown');
      layerDropdown.val(selectedIndex).trigger('change');
    }
  });

  // هماهنگ‌سازی اولیه تصاویر ماهواره
  setTimeout(() => {
    // ابتدا وضعیت فعلی نقشه را بررسی کن
    syncCurrentBasemapSelection();

    // هماهنگ‌سازی اضافی برای تولبار
    setTimeout(() => {
      if (window.syncToolbarBasemap) {
        window.syncToolbarBasemap('osm'); // پیش‌فرض اوپن استریت
      }
    }, 500);
  }, 1000);

}

//// تعریف توابع از اینجا



window.toggleAllLayers = function (checked) {
  const layersConfig = config_getLayersConfig();
  layersConfig.forEach((layerConfig, index) => {
    window.toggleLayer(layerConfig.name, checked); // لایه را خاموش یا روشن کنید
  });
  console.log(`همه لایه‌ها ${checked ? "روشن" : "خاموش"} شدند.`);

  // هماهنگ‌سازی کامل همه UI ها
  setTimeout(() => {
    window.syncAllLayerStates();
  }, 100);
};

function updateToggleAllLabel() {
  const layersConfig = config_getLayersConfig();
  const allLayersOn = layersConfig.every((layerConfig, index) => {
    const checkbox = document.getElementById(`layer-${index}`);
    return checkbox ? checkbox.checked : false;
  });

  const toggleAllCheckbox = document.getElementById("toggle-all-layers");
  const toggleAllLabel = toggleAllCheckbox?.nextElementSibling;

  if (toggleAllLabel) {
    toggleAllLabel.textContent = allLayersOn ? "خاموش کردن همه" : "روشن کردن همه";
  }

  // به‌روزرسانی چک‌باکس تولبار
  const toolbarToggleAllCheckbox = document.getElementById("toolbar-toggle-all-layers");
  if (toolbarToggleAllCheckbox) {
    toolbarToggleAllCheckbox.checked = allLayersOn;
  }
}

function updateHeaderToggleAllLabel() {
  const layersConfig = config_getLayersConfig();
  const allLayersOn = layersConfig.every((layerConfig, index) => {
    const checkbox = document.getElementById(`layer-${index}`);
    return checkbox ? checkbox.checked : false;
  });

  const headerToggleAllIcon = document.querySelector('#toggle-all-layers-header i');
  if (headerToggleAllIcon) {
    headerToggleAllIcon.className = allLayersOn ? 'fas fa-check-square' : 'fas fa-square';
  }

  // به‌روزرسانی چک‌باکس تولبار
  const toolbarToggleAllCheckbox = document.getElementById("toolbar-toggle-all-layers");
  if (toolbarToggleAllCheckbox) {
    toolbarToggleAllCheckbox.checked = allLayersOn;
  }
}

// تابع برای هماهنگ‌سازی کامل بین منوی هدر و سایدبار
window.syncHeaderWithSidebar = function() {
  config_getLayersConfig().forEach((layer, index) => {
    const sidebarCheckbox = document.getElementById(`layer-${index}`);
    const headerIcon = document.querySelector(`#layer-header-${index} i`);
    const toolbarCheckbox = document.getElementById(`toolbar-layer-${index}`);

    if (sidebarCheckbox && headerIcon) {
      const isChecked = sidebarCheckbox.checked;
      headerIcon.className = isChecked ? 'fas fa-check-square' : 'fas fa-square';

      // هماهنگ‌سازی با تولبار
      if (toolbarCheckbox) {
        toolbarCheckbox.checked = isChecked;
      }
    }
  });
  updateHeaderToggleAllLabel();
}

// تابع هماهنگ‌سازی بر اساس وضعیت واقعی لایه‌ها در نقشه
window.syncAllLayerStates = function() {
  const map = getMap();
  if (!map) return;

  config_getLayersConfig().forEach((layer, index) => {
    const layerObj = config_getFeatureLayers()[layer.name];
    const isLayerOnMap = layerObj && map.hasLayer(layerObj);

    // به‌روزرسانی sidebar
    const sidebarCheckbox = document.getElementById(`layer-${index}`);
    if (sidebarCheckbox && sidebarCheckbox.checked !== isLayerOnMap) {
      sidebarCheckbox.checked = isLayerOnMap;
    }

    // به‌روزرسانی navbar header
    const headerIcon = document.querySelector(`#layer-header-${index} i`);
    if (headerIcon) {
      headerIcon.className = isLayerOnMap ? 'fas fa-check-square' : 'fas fa-square';
    }

    // به‌روزرسانی toolbar
    const toolbarCheckbox = document.getElementById(`toolbar-layer-${index}`);
    if (toolbarCheckbox && toolbarCheckbox.checked !== isLayerOnMap) {
      toolbarCheckbox.checked = isLayerOnMap;
    }
  });

  updateHeaderToggleAllLabel();
  updateToggleAllLabel();
}

// تابع کلیک روی نقشه برای انتخاب مختصات
function onMapClick(e) {
  if (!isPickingCoordinates) return;

  const ll = e.latlng;
  if (L && ll.utm && coordinateMarkers) {
    const utm = ll.utm();

    const marker = L.marker(ll).addTo(coordinateMarkers);
    marker.bindPopup(
      `UTM: ${utm.toString()}<br>LatLng: ${ll.lat.toFixed(6)}, ${ll.lng.toFixed(6)}`
    ).openPopup();

    console.log(
      `کلیک روی: LatLng: ${ll.lat}, ${ll.lng} | UTM: ${utm.toString()}`
    );

    // غیرفعال کردن حالت انتخاب مختصات بعد از زدن نقطه
    disableCoordinatePicking();
  }
}

window.disableCoordinatePicking = function() {
  isPickingCoordinates = false; // غیرفعال‌سازی انتخاب مختصات
  const pickCoordinateBtn = document.getElementById("pick-coordinate-btn");
  if (pickCoordinateBtn) {
    pickCoordinateBtn.classList.remove("active");
  }
  const map = getMap();
  if (map) {
    map.getContainer().style.cursor = "";
    map.off("click", onMapClick);
  }
  console.log("ابزار انتخاب مختصات غیرفعال شد");
}


function generateLayerCheckboxes() {
  let layersHTML = "";
  config_getLayersConfig().forEach((layer, index) => {
    const isChecked = (layer.visible !== false) ? "checked" : "";

    // بررسی آیا لایه دارای تنظیمات لیبل است (صرف نظر از enabled)
    const hasLabel = layer.config && layer.config.label && layer.config.label.property;
    const labelChecked = (hasLabel && layer.config.label.enabled) ? "checked" : "";

    layersHTML += `
      <div class="mb-3 layer-item">
        <div class="d-flex align-items-center" style="flex-wrap: nowrap;">
          <div class="form-check d-flex align-items-center flex-grow-1">
            <input type="checkbox" id="layer-${index}" ${isChecked} onchange="window.toggleLayer('${layer.name}', this.checked)" style="margin-top: 0;">
            <label for="layer-${index}" class="form-check-label large-text ms-2">
              <i class="fa ${layer.icon}"></i> ${layer.name}
            </label>
          </div>
          <div class="dropdown ms-2">
            <button class="btn btn-default dropdown-toggle" type="button" id="dropdownMenuButton-${index}" data-toggle="dropdown" aria-expanded="false" style="padding: 2px 8px; font-size: 12px; line-height: 1;">
              <i class="fas fa-ellipsis-v"></i>
            </button>
            <ul class="dropdown-menu dropdown-menu-right" role="menu" aria-labelledby="dropdownMenuButton-${index}">
              <li><a class="dropdown-item" href="#" data-action="descriptive-table" data-layer-index="${index}">جدول توصیفی</a></li>
              <li class="divider"></li>
              ${hasLabel ? `
              <li>
                <div class="dropdown-item" style="padding: 5px;">
                  <label style="display: flex; align-items: center; margin: 0;">
                    <input type="checkbox" id="label-${index}" ${labelChecked} onchange="window.toggleLayerLabels('${layer.name}', this.checked)" style="margin-left: 5px;">
                    <span>نمایش لیبل</span>
                  </label>
                </div>
              </li>
              <li class="divider"></li>
              ` : ''}
              <li>
                <div class="dropdown-item opacity-slider-container" style="display: ${isChecked ? 'block' : 'none'}; padding: 5px;">
                  <label for="opacity-${index}" class="form-label">شفافیت: <span id="opacity-value-${index}">1.0</span></label>
                  <input type="range" class="form-range" id="opacity-${index}" min="0" max="1" step="0.1" value="1" onchange="window.updateOpacity('${layer.name}', this.value)">
                </div>
              </li>
            </ul>
          </div>
        </div>
      </div>
    `;
  });
  return layersHTML;
}

// تابع هماهنگ‌سازی انتخاب تصاویر ماهواره بین ناوبار و تولبار
function syncBasemapSelection(layerName, skipToolbarSync = false) {
  // نقشه نام‌های لایه به متن فارسی
  const basemapNames = {
    googlesat: 'تصویر گوگل',
    googlemap: 'گوگل مپ',
    osm: 'اوپن استریت',
    nobasemap: 'بدون تصویر'
  };

  // هماهنگ‌سازی رادیو باتن‌های ناوبار
  const navbarRadios = [
    'base-googlesat', 'base-googlemap',
    'base-osm',  'base-nobasemap'
  ];

  // هماهنگ‌سازی رادیو باتن‌های تولبار
  const toolbarRadios = [
    'toolbar-base-googlesat', 'toolbar-base-googlemap',
    'toolbar-base-osm', 'toolbar-base-nobasemap'
  ];

  // هماهنگ‌سازی رادیو باتن‌های سایدبار
  const sidebarRadios = [
    'base-googlesat', 'base-googlemap',
    'base-osm',  'base-nobasemap'
  ];

  // پاک کردن همه انتخاب‌ها
  [...navbarRadios, ...toolbarRadios, ...sidebarRadios].forEach(radioId => {
    const radio = document.getElementById(radioId);
    if (radio) {
      radio.checked = false;
    }
  });

  // انتخاب رادیو باتن مناسب بر اساس layerName
  const targetRadioSuffix = layerName;

  // فعال کردن رادیو باتن‌های مناسب
  const navbarRadio = document.getElementById(`base-${targetRadioSuffix}`);
  const toolbarRadio = document.getElementById(`toolbar-base-${targetRadioSuffix}`);

  if (navbarRadio) {
    navbarRadio.checked = true;
  }
  if (toolbarRadio) {
    toolbarRadio.checked = true;
  }

  // 🆕 به‌روزرسانی عنوان منوی ناوبار
  updateNavbarBasemapTitle(layerName, basemapNames[layerName]);

  // 🆕 اطلاع‌رسانی به تولبار برای هماهنگ‌سازی (فقط اگر از تولبار نیامده)
  if (!skipToolbarSync) {
    setTimeout(() => {
      if (window.syncToolbarBasemap) {
        window.syncToolbarBasemap(layerName);
      }
    }, 50);
  }

  console.log(`هماهنگ‌سازی تصاویر ماهواره: ${layerName} انتخاب شد`);
}

// تابع به‌روزرسانی عنوان منوی ناوبار
function updateNavbarBasemapTitle(layerName, displayName) {
  const navbarBasemapText = document.getElementById('navbar-basemap-text');
  if (navbarBasemapText) {
    navbarBasemapText.textContent = displayName || 'تصاویر ماهواره';
    console.log(`عنوان منوی ناوبار به "${displayName}" تغییر کرد`);
  }
}

// تابع هماهنگ‌سازی انتخاب فعلی تصویر ماهواره (برای سایدبار)
function syncCurrentBasemapSelection() {
  // نقشه نام‌های لایه به متن فارسی
  const basemapNames = {
    googlesat: 'تصویر گوگل',
    googlemap: 'گوگل مپ',
    osm: 'اوپن استریت',
    nobasemap: 'بدون تصویر'
  };

  // بررسی کدام تصویر ماهواره در حال حاضر فعال است
  const map = getMap();
  if (!map) return;

  // پیدا کردن لایه فعال فعلی
  let currentBasemap = 'osm'; // پیش‌فرض نقشه شهرداری تهران

  // بررسی لایه‌های موجود در نقشه
  let foundBasemap = false;
  map.eachLayer(function(layer) {
    if (layer instanceof L.TileLayer && !foundBasemap) {
      // بررسی URL برای تشخیص نوع تصویر ماهواره
      const url = layer._url || '';
      console.log('بررسی URL لایه در ui.js:', url);

      if (url.includes('lyrs=s') || url.includes('/sat/')) {
        currentBasemap = 'googlesat';
        foundBasemap = true;
      } else if (url.includes('lyrs=m') || url.includes('/map/')) {
        currentBasemap = 'googlemap';
        foundBasemap = true;
      } else if (url.includes('/osm/') || url.includes('openstreetmap.org')) {
        currentBasemap = 'osm';
        foundBasemap = true;
      }
    }
  });

  // اگر هیچ TileLayer پیدا نشد، بدون تصویر است
  if (!foundBasemap) {
    let hasEmptyLayerGroup = false;
    map.eachLayer(function(layer) {
      if (layer instanceof L.LayerGroup && layer.getLayers().length === 0) {
        hasEmptyLayerGroup = true;
      }
    });
    if (hasEmptyLayerGroup) {
      currentBasemap = 'nobasemap';
    }
  }

  // تنظیم رادیو باتن مناسب در سایدبار
  const sidebarRadio = document.getElementById(`base-${currentBasemap}`);
  if (sidebarRadio) {
    sidebarRadio.checked = true;
  }

  // 🆕 تنظیم رادیو باتن مناسب در تولبار
  const toolbarRadio = document.getElementById(`toolbar-base-${currentBasemap}`);
  if (toolbarRadio) {
    toolbarRadio.checked = true;
  }

  // 🆕 به‌روزرسانی عنوان منوی ناوبار
  updateNavbarBasemapTitle(currentBasemap, basemapNames[currentBasemap]);

  console.log(`تصویر ماهواره فعلی در سایدبار: ${currentBasemap}`);
}

window.switchBaseLayer = function (layerName, fromToolbar = false) {
  const baseLayerMap = {
    googlesat: baseLayers["تصویر گوگل"],
    osm : baseLayers["اوپن استریت"],
    googlemap: baseLayers["گوگل مپ"],
    nobasemap: baseLayers["بدون تصویر"],
  };
  const selectedLayer = baseLayerMap[layerName];
  if (selectedLayer && getMap()) {
    const activeLayers = [];
    config_getLayersConfig().forEach((layerConfig, index) => {
      const checkbox = document.getElementById(`layer-${index}`);
      if (checkbox && checkbox.checked) {
        activeLayers.push(layerConfig.name);
      }
    });

    console.log("zIndex بیس‌مپ:", selectedLayer.options.zIndex);
    activeLayers.forEach(function (layerName) {
      console.log(
        "zIndex لایه:",
        layerName,
        config_getFeatureLayers()[layerName].options.zIndex
      );
    });

    getMap().eachLayer(function (layer) {
      if (layer instanceof L.TileLayer || layer instanceof L.LayerGroup) {
        getMap().removeLayer(layer);
      }
    });

    selectedLayer.setZIndex(0);
    getMap().addLayer(selectedLayer);

    activeLayers.forEach(function (layerName) {
      const layer = config_getFeatureLayers()[layerName];
      if (layer) {
        if (!getMap().hasLayer(layer)) {
          layer.setZIndex(10);
          getMap().addLayer(layer);
        }
        layer.bringToFront();
      }
    });

    if (!getMap().hasLayer(getHighlightLayer())) {
      getHighlightLayer().setZIndex(100);
      getMap().addLayer(getHighlightLayer());
    } else {
      getHighlightLayer().bringToFront();
    }

    if (!getMap().hasLayer(editableLayers)) {
      editableLayers.setZIndex(50);
      getMap().addLayer(editableLayers);
    } else {
      editableLayers.bringToFront();
    }

    // هماهنگ‌سازی رادیو باتن‌های ناوبار و تولبار
    syncBasemapSelection(layerName, fromToolbar);

    console.log("لایه‌های فعال بعد از تغییر:", activeLayers);
  }
};

window.toggleLayer = function (layerName, checked) {
  const layer = config_getFeatureLayers()[layerName];
  const layerIndex = config_getLayersConfig().findIndex(l => l.name === layerName);

  // به‌روزرسانی چک‌باکس سایدبار
  const sidebarCheckbox = document.querySelector(`input[id^="layer-"][onchange*="${layerName}"]`);
  const sliderContainer = sidebarCheckbox?.parentElement?.parentElement?.querySelector(`.opacity-slider-container`);
  const slider = document.getElementById(`opacity-${layerIndex}`);

  // به‌روزرسانی چک‌باکس تولبار
  const toolbarCheckbox = document.getElementById(`toolbar-layer-${layerIndex}`);

  // به‌روزرسانی آیکون منوی هدر
  const headerIcon = document.querySelector(`#layer-header-${layerIndex} i`);

  if (layer && getMap()) {
    if (checked) {
      layer.setZIndex(10);
      getMap().addLayer(layer);
      layer.bringToFront();

      // به‌روزرسانی سایدبار
      if (sidebarCheckbox) sidebarCheckbox.checked = true;
      if (sliderContainer) sliderContainer.style.display = 'block';
      if (slider) {
        layer.setStyle({
          opacity: slider.value,
          fillOpacity: slider.value * 0.5,
        });
      }

      // به‌روزرسانی تولبار
      if (toolbarCheckbox) {
        toolbarCheckbox.checked = true;
        console.log(`تولبار چک‌باکس ${layerName} روشن شد`);
      } else {
        console.log(`تولبار چک‌باکس ${layerName} پیدا نشد`);
      }

      // به‌روزرسانی منوی هدر
      if (headerIcon) {
        headerIcon.className = 'fas fa-check-square';
        console.log(`آیکون هدر ${layerName} روشن شد`);
      } else {
        console.log(`آیکون هدر ${layerName} پیدا نشد`);
      }
    } else {
      getMap().removeLayer(layer);

      // به‌روزرسانی سایدبار
      if (sidebarCheckbox) sidebarCheckbox.checked = false;
      if (sliderContainer) sliderContainer.style.display = 'none';
      layer.setStyle({
        opacity: 1,
        fillOpacity: 0.5,
      });

      // به‌روزرسانی تولبار
      if (toolbarCheckbox) {
        toolbarCheckbox.checked = false;
        console.log(`تولبار چک‌باکس ${layerName} خاموش شد`);
      } else {
        console.log(`تولبار چک‌باکس ${layerName} پیدا نشد`);
      }

      // به‌روزرسانی منوی هدر
      if (headerIcon) {
        headerIcon.className = 'fas fa-square';
        console.log(`آیکون هدر ${layerName} خاموش شد`);
      } else {
        console.log(`آیکون هدر ${layerName} پیدا نشد`);
      }
    }
    console.log(`لایه ${layerName} ${checked ? "روشن" : "خاموش"} شد`);

    // هماهنگ‌سازی کامل همه UI ها
    setTimeout(() => {
      window.syncAllLayerStates();

      // هماهنگ‌سازی اضافی toolbar اگر موجود است
      if (window.widgetToolbar && typeof window.widgetToolbar.syncWithCurrentLayerStates === 'function') {
        console.log('🔄 هماهنگ‌سازی اضافی toolbar از ui.js');
        window.widgetToolbar.syncWithCurrentLayerStates();
      }
    }, 50);
  }
};

window.updateOpacity = function (layerName, opacity) {
  const layer = config_getFeatureLayers()[layerName];
  if (layer && getMap().hasLayer(layer)) {
    layer.setStyle({
      opacity: opacity, // برای خطوط و مرزها
      fillOpacity: opacity * 0.5, // برای پر کردن (مثلاً پلیگان‌ها)
    });
    document.getElementById(`opacity-value-${config_getLayersConfig().findIndex(l => l.name === layerName)}`).textContent = opacity;
    console.log(`شفافیت لایه ${layerName} به ${opacity} تنظیم شد`);
  }
};

// تابع کنترل نمایش لیبل‌های لایه
window.toggleLayerLabels = function (layerName, showLabels) {
  const layer = config_getFeatureLayers()[layerName];
  const layerConfig = config_getLayersConfig().find(l => l.name === layerName);

  if (layer && layerConfig && layerConfig.config && layerConfig.config.label) {
    layer.eachLayer(function(featureLayer) {
      if (showLabels) {
        // نمایش لیبل
        const labelCfg = layerConfig.config.label;
        const feature = featureLayer.feature;

        if (feature && feature.properties) {
          const propName = labelCfg.property;
          const labelText = propName ? feature.properties[propName] : null;

          if (labelText !== undefined && labelText !== null && labelText !== "") {
            const direction = labelCfg.direction || "center";
            const defaultOffset = (function(){
              switch(direction){
                case "top": return [0, -6];
                case "bottom": return [0, 6];
                case "left": return [-6, 0];
                case "right": return [6, 0];
                default: return [0, 0];
              }
            })();
            const configuredOffset = (Array.isArray(labelCfg.offset) && labelCfg.offset.length === 2)
              ? labelCfg.offset
              : defaultOffset;

            const tooltipOptions = {
              permanent: true,
              direction: direction,
              className: labelCfg.className || "layer-label",
              sticky: false,
              offset: L.point(configuredOffset[0], configuredOffset[1]),
            };

            const tooltip = featureLayer.bindTooltip(String(labelText), tooltipOptions);

            // اعمال رنگ لیبل از کانفیگ
            if (labelCfg.color && tooltip) {
              tooltip.on('tooltipopen', function(e) {
                const element = e.tooltip.getElement();
                if (element) {
                  element.style.color = labelCfg.color;
                  element.style.fontWeight = 'bold';
                }
              });
            }

            tooltip.openTooltip();
          }
        }
      } else {
        // مخفی کردن لیبل
        if (featureLayer.getTooltip()) {
          featureLayer.unbindTooltip();
        }
      }
    });

    console.log(`لیبل‌های لایه ${layerName} ${showLabels ? "نمایش داده شد" : "مخفی شد"}`);
  }
};

// تابع به‌روزرسانی دکمه toggle all در تولبار
function updateToolbarToggleAllButton() {
  const toggleAllBtn = document.getElementById('toolbar-toggle-all-layers');
  if (!toggleAllBtn) return;

  const layersConfig = getLayersConfig();
  if (!layersConfig) return;

  // بررسی وضعیت همه لایه‌ها
  let allChecked = true;
  let anyChecked = false;

  layersConfig.forEach((layer, index) => {
    const checkbox = document.getElementById(`layer-${index}`);
    if (checkbox) {
      if (checkbox.checked) {
        anyChecked = true;
      } else {
        allChecked = false;
      }
    }
  });

  // به‌روزرسانی دکمه
  const icon = toggleAllBtn.querySelector('i');
  const span = toggleAllBtn.querySelector('span');

  if (allChecked && anyChecked) {
    // همه فعال
    toggleAllBtn.setAttribute('data-state', 'on');
    if (icon) icon.className = 'fas fa-toggle-on';
    if (span) span.textContent = 'غیرفعال کردن همه';
  } else {
    // همه یا بعضی غیرفعال
    toggleAllBtn.setAttribute('data-state', 'off');
    if (icon) icon.className = 'fas fa-toggle-off';
    if (span) span.textContent = 'فعال کردن همه';
  }
}

function getLayerCheckboxState() {
  const checkboxStates = {};
  config_getLayersConfig().forEach((layer, index) => {
    const checkbox = document.getElementById(`layer-${index}`);
    checkboxStates[layer.name] = checkbox ? checkbox.checked : false;
  });
  return checkboxStates;
}

function setupUI() {

    // عملکرد دکمه ذره‌بین - toggle search container
    $(document).off("click", ".navbar-toggle").on("click", ".navbar-toggle", function(e) {
      e.preventDefault();
      e.stopPropagation();
      console.log("Search toggle clicked!");

      const $searchContainer = $("#map-search-overlay");

      if ($searchContainer.hasClass("active")) {
        $searchContainer.removeClass("active");
        console.log("Search container hidden");
      } else {
        $searchContainer.addClass("active");
        console.log("Search container shown");

        // فوکوس روی select اگر وجود داشت
        setTimeout(() => {
          $("#navbar-layer-select").focus();
        }, 300);
      }

      return false;
    });

    // تست اضافی برای اطمینان
    $(document).ready(function() {
      console.log("Navbar toggle elements found:", $(".navbar-toggle").length);
      console.log("Search container found:", $("#map-search-overlay").length);

      // اضافه کردن event listener مستقیم
      document.addEventListener('click', function(e) {
        if (e.target.closest('.navbar-toggle')) {
          console.log("Direct click detected on navbar-toggle");
        }
      });
    });

    $("#about-btn")
    .off("click")
    .on("click", function () {
      $("#aboutModal").modal("show");
      $(".navbar-collapse.in").collapse("hide");
      return false;
    });

    $("#about-btn1")
    .off("click")
    .on("click", function () {
      $("#aboutModal1").modal("show");
      $(".navbar-collapse.in").collapse("hide");
      return false;
    });

  function createLayerDropdown() {
    const layerMenu = document.getElementById('layer-dropdown-menu');
    if (!layerMenu) return;

    layerMenu.innerHTML = '';

    // اضافه کردن تیک همه
    const toggleAllItem = document.createElement('li');
    toggleAllItem.innerHTML = `
      <a class="dropdown-item" href="#" id="toggle-all-layers-header">
        <i class="fas fa-check-square"></i> همه لایه‌ها
      </a>
    `;
    layerMenu.appendChild(toggleAllItem);

    // اضافه کردن خط جداکننده
    const divider = document.createElement('li');
    divider.className = 'divider';
    layerMenu.appendChild(divider);

    // اضافه کردن لایه‌ها
    config_getLayersConfig().forEach((layer, index) => {
      const layerItem = document.createElement('li');
      // بررسی وضعیت اولیه لایه بر اساس config
      const isInitiallyVisible = layer.visible !== false;
      const iconClass = isInitiallyVisible ? 'fa-check-square' : 'fa-square';
      layerItem.innerHTML = `
        <a class="dropdown-item" href="#" id="layer-header-${index}">
          <i class="fas ${iconClass}"></i> ${layer.name}
        </a>
      `;
      layerMenu.appendChild(layerItem);
    });
  }

  // فراخوانی تابع ایجاد منو
  createLayerDropdown();

  // هماهنگ‌سازی اولیه منوی هدر با وضعیت واقعی لایه‌ها
  setTimeout(() => {
    window.syncHeaderWithSidebar();
  }, 500);

// تنظیم وضعیت اولیه واحدهای اندازه‌گیری
  // setTimeout(() => {
  //   updateMeasureUnitDisplay();
  // }, 1000);

  // رویدادهای هدر - نقشه‌ها
  $("#toggle-all-layers-header").off("click").on("click", function () {
    const allChecked = $('.dropdown-item[id^="layer-header-"] i').hasClass('fa-check-square');
    const newState = !allChecked;

    // تغییر وضعیت لایه‌ها (این کار آیکون‌ها را نیز به‌روزرسانی می‌کند)
    config_getLayersConfig().forEach((layer, index) => {
      window.toggleLayer(layer.name, newState);
    });

    // هماهنگ‌سازی اضافی toolbar با تاخیر
    setTimeout(() => {
      window.forceToolbarSync();
    }, 200);

    $(".navbar-collapse.in").collapse("hide");
  });

  // رویدادهای هدر - لایه‌های فردی
  config_getLayersConfig().forEach((layer, index) => {
    $(`#layer-header-${index}`).off("click").on("click", function () {
      const icon = $(this).find('i');
      const isChecked = icon.hasClass('fa-check-square');
      const newState = !isChecked;

      // تغییر وضعیت لایه (این کار آیکون‌ها را نیز به‌روزرسانی می‌کند)
      window.toggleLayer(layer.name, newState);

      // هماهنگ‌سازی اضافی toolbar با تاخیر
      setTimeout(() => {
        window.forceToolbarSync();
      }, 100);

      $(".navbar-collapse.in").collapse("hide");
    });
  });



  // رویدادهای هدر - ابزارهای ترسیم
  $("#draw-polyline").off("click").on("click", function () {
    console.log("کلیک روی ترسیم خط");
    const map = getMap();
    if (!map) {
      console.error("نقشه هنوز آماده نیست");
      return;
    }
    console.log("نقشه پیدا شد:", map);
            if (activeDrawTool) {
          console.log("غیرفعال کردن ابزار قبلی");
          activeDrawTool.disable();
        }
        console.log("ایجاد ابزار ترسیم خط");
        activeDrawTool = new L.Draw.Polyline(map, {
          shapeOptions: {
            color: "red",
            weight: 4,
            opacity: 1,
          },
        });
        window.activeDrawTool = activeDrawTool;
        console.log("فعال کردن ابزار ترسیم");
        activeDrawTool.enable();
        $(".navbar-collapse.in").collapse("hide");
  });

  $("#draw-polygon").off("click").on("click", function () {
    const map = getMap();
    if (!map) {
      console.error("نقشه هنوز آماده نیست");
      return;
    }
    if (activeDrawTool) activeDrawTool.disable();
    activeDrawTool = new L.Draw.Polygon(map, {
      allowIntersection: false,
      drawError: {
        color: "purple",
        message: "<strong>خطا!</strong> نمی‌تونید اینو بکشید!",
      },
      shapeOptions: {
        color: "orange",
        weight: 4,
        opacity: 1,
        fillColor: "#32CD32",
        fillOpacity: 0.5,
      },
    });
    window.activeDrawTool = activeDrawTool;
    activeDrawTool.enable();
    $(".navbar-collapse.in").collapse("hide");
  });

  $("#draw-circle").off("click").on("click", function () {
    const map = getMap();
    if (!map) {
      console.error("نقشه هنوز آماده نیست");
      return;
    }
    if (activeDrawTool) activeDrawTool.disable();
    activeDrawTool = new L.Draw.Circle(map, {
      shapeOptions: {
        color: "steelblue",
        weight: 4,
        opacity: 1,
        fillColor: "#8A2BE2",
        fillOpacity: 0.5,
      },
    });
    window.activeDrawTool = activeDrawTool;
    activeDrawTool.enable();
    $(".navbar-collapse.in").collapse("hide");
  });

  $("#draw-rectangle").off("click").on("click", function () {
    const map = getMap();
    if (!map) {
      console.error("نقشه هنوز آماده نیست");
      return;
    }
    if (activeDrawTool) activeDrawTool.disable();
    activeDrawTool = new L.Draw.Rectangle(map, {
      shapeOptions: {
        color: "green",
        weight: 4,
        opacity: 1,
        fillColor: "#FF4500",
        fillOpacity: 0.5,
      },
    });
    window.activeDrawTool = activeDrawTool;
    activeDrawTool.enable();
    $(".navbar-collapse.in").collapse("hide");
  });

  $("#draw-marker").off("click").on("click", function () {
    const map = getMap();
    if (!map) {
      console.error("نقشه هنوز آماده نیست");
      return;
    }
    if (activeDrawTool) activeDrawTool.disable();
    activeDrawTool = new L.Draw.Marker(map, {
      icon: new L.Icon.Default(),
    });
    window.activeDrawTool = activeDrawTool;
    activeDrawTool.enable();
    $(".navbar-collapse.in").collapse("hide");
  });

  $("#edit-layers").off("click").on("click", function () {
    const map = getMap();
    if (!map) {
      console.error("نقشه هنوز آماده نیست");
      return;
    }
    if (activeDrawTool) activeDrawTool.disable();
    activeDrawTool = new L.EditToolbar.Edit(map, {
      featureGroup: editableLayers,
    });
    window.activeDrawTool = activeDrawTool;
    activeDrawTool.enable();
    $(".navbar-collapse.in").collapse("hide");
  });

  $("#remove-layers").off("click").on("click", function () {
    const map = getMap();
    if (!map) {
      console.error("نقشه هنوز آماده نیست");
      return;
    }
    if (activeDrawTool) activeDrawTool.disable();
    activeDrawTool = new L.EditToolbar.Delete(map, {
      featureGroup: editableLayers,
    });
    window.activeDrawTool = activeDrawTool;
    activeDrawTool.enable();
    $(".navbar-collapse.in").collapse("hide");
  });

  // رویدادهای هدر - تصاویر ماهواره
  $("#base-googlesat").off("click").on("click", function () {
    console.log("تغییر به تصویر گوگل");
    if (window.switchBaseLayer) {
      window.switchBaseLayer('googlesat');
    } else {
      console.error("تابع switchBaseLayer موجود نیست");
    }
    $(".navbar-collapse.in").collapse("hide");
  });

  $("#base-osm").off("click").on("click", function () {
    console.log("تغییر به تصویر OSM");
    if (window.switchBaseLayer) {
      window.switchBaseLayer('osm');
    } else {
      console.error("تابع switchBaseLayer موجود نیست");
    }
    $(".navbar-collapse.in").collapse("hide");
  });

  $("#base-googlemap").off("click").on("click", function () {
    console.log("تغییر به googlemap");
    if (window.switchBaseLayer) {
      window.switchBaseLayer('googlemap');
    } else {
      console.error("تابع switchBaseLayer موجود نیست");
    }
    $(".navbar-collapse.in").collapse("hide");
  });

  $("#base-nobasemap").off("click").on("click", function () {
    console.log("حذف تصویر پس‌زمینه");
    if (window.switchBaseLayer) {
      window.switchBaseLayer('nobasemap');
    } else {
      console.error("تابع switchBaseLayer موجود نیست");
    }
    $(".navbar-collapse.in").collapse("hide");
  });

    const layersConfig = config_getLayersConfig();
    const layerDropdown = $("#layer-dropdown");
    layerDropdown.empty();
    layerDropdown.append('<option value="" disabled selected>لایه را انتخاب کنید</option>');
    layersConfig.forEach((layer, index) => {
      layerDropdown.append(
        `<option value="${index}">${layer.name}</option>` // حذف تگ <i>
      );
    });

  let initialCheckboxStates = getLayerCheckboxState();

  layerDropdown.on("change", function (e) {
    const selectedIndex = parseInt($(this).val(), 10);
    if (!isNaN(selectedIndex)) {
      console.log("انتخاب اندیس از کمبو‌باکس:", selectedIndex);
      const layerName = config_getLayersConfig()[selectedIndex].name;
      window.toggleLayer(layerName, true); // لایه رو فعال می‌کنه
      loadLayers(selectedIndex); // جدول رو لود می‌کنه
      console.log(`لایه ${layerName} انتخاب و روشن شد`);
      switchView("split"); // نمایش جدول و نقشه به‌صورت اسپلیت
    } else {
      console.error("اندیس نامعتبر از کمبوباکس:", $(this).val());
    }
  });

  // رویداد کلیک برای دکمه‌های "جدول توصیفی"
  document.querySelectorAll('.btn-descriptive-table').forEach(button => {
    button.addEventListener('click', function () {
      const selectedIndex = parseInt(this.getAttribute('data-layer-index'), 10);
      console.log("کلیک روی دکمه جدول توصیفی برای اندیس:", selectedIndex);
      const layerName = config_getLayersConfig()[selectedIndex].name;
      window.toggleLayer(layerName, true); // لایه رو فعال می‌کنه
      loadLayers(selectedIndex); // جدول رو لود می‌کنه
      console.log(`لایه ${layerName} از دکمه جدول توصیفی انتخاب شد`);
      switchView("split"); // نمایش جدول و نقشه به‌صورت اسپلیت
      // به‌روزرسانی انتخاب در کمبو‌باکس (اختیاری)
      layerDropdown.val(selectedIndex).trigger('change');
    });
  });

  $("#table-container").on("click", ".close-table-btn", function () {
    const currentLayerName = config_getLayersConfig()[getCurrentLayerIndex()]?.name;
    if (currentLayerName) {
      const wasChecked = initialCheckboxStates[currentLayerName];
      window.toggleLayer(currentLayerName, wasChecked);
      getHighlightLayer().clearLayers();
      console.log(`جدول بسته شد، لایه ${currentLayerName} به حالت ${wasChecked ? "روشن" : "خاموش"} برگشت`);
    }
    switchView("map");
    initialCheckboxStates = getLayerCheckboxState();
  });
}

function switchView(view) {
  if (view === "split") {
    $("#table-container").css({ display: "block", height: "55%" });
    $("#searchContainer").css({ display: "block", height: "45%" });
    $(window).resize();
    if (getMap()) getMap().invalidateSize();
    const tableId = "#table-" + getCurrentLayerIndex();
    $(tableId).bootstrapTable("resetView");
  } else if (view === "map") {
    $("#searchContainer").css({ display: "block", height: "100%" });
    $("#table-container").css("display", "none");
    if (getMap()) getMap().invalidateSize();
  } else if (view === "table") {
    $("#table-container").css({ display: "block", height: "100%" });
    $("#searchContainer").css("display", "none");
    $(window).resize();
    const tableId = "#table-" + getCurrentLayerIndex();
    $(tableId).bootstrapTable("resetView");
  }
}




// تابع به‌روزرسانی تایم
function updateTime() {
    const now = new Date();
    const persianDate = now.toLocaleDateString('fa-IR');
    const time = now.toLocaleTimeString('fa-IR');
    const timeElement = document.getElementById('current-time');
    if (timeElement) {
        timeElement.textContent = `${persianDate} - ${time}`;
    }
}

// شروع به‌روزرسانی تایم هر ثانیه
setInterval(updateTime, 1000);

// سرکوب کردن هشدارهای deprecated و touchleave برای بهبود تجربه کاربری
const originalWarn = console.warn;
console.warn = function(...args) {
    if (args[0] && typeof args[0] === 'string' &&
        (args[0].includes('Deprecated include of L.Mixin.Events') ||
         args[0].includes('wrong event specified: touchleave'))) {
        return; // نادیده گرفتن این هشدارهای خاص
    }
    originalWarn.apply(console, args);
};

// تابع global برای هماهنگ‌سازی toolbar
window.forceToolbarSync = function() {
  console.log('🚀 اجبار هماهنگ‌سازی toolbar از window');
  if (window.widgetToolbar && typeof window.widgetToolbar.syncWithCurrentLayerStates === 'function') {
    window.widgetToolbar.syncWithCurrentLayerStates();
  } else {
    console.warn('❌ widgetToolbar موجود نیست یا تابع syncWithCurrentLayerStates ندارد');
  }
};

// اضافه کردن توابع به window برای دسترسی تولبار
window.generateLayerCheckboxes = generateLayerCheckboxes;
window.updateNavbarBasemapTitle = updateNavbarBasemapTitle;
window.loadLayers = loadLayers;
window.switchView = switchView;
window.getFeatureLayers = config_getFeatureLayers;
window.getMap = getMap;
window.getLayersConfig = config_getLayersConfig;

// تابع کمکی برای هماهنگ‌سازی تصاویر ماهواره از طرف ui.js
window.syncToolbarBasemap = function(layerName) {
  if (window.widgetToolbar && window.widgetToolbar.setBasemapSelection) {
    // مستقیماً تنظیم انتخاب تولبار بدون بررسی نقشه
    window.widgetToolbar.setBasemapSelection(layerName);
  }
};
;// ./src/js/widget-toolbar.js
// مدیریت نوار تولبار ویجت‌ها


// متغیرهای مشترک برای مختصات
let widget_toolbar_isPickingCoordinates = false;

// تابع کلیک روی نقشه برای انتخاب مختصات
function widget_toolbar_onMapClick(e) {
  if (!widget_toolbar_isPickingCoordinates) return;

  const ll = e.latlng;
  if (window.L && ll.utm && window.coordinateMarkers) {
    const utm = ll.utm();

    const marker = window.L.marker(ll).addTo(window.coordinateMarkers);
    marker.bindPopup(
      `UTM: ${utm.toString()}<br>LatLng: ${ll.lat.toFixed(6)}, ${ll.lng.toFixed(6)}`
    ).openPopup();

    console.log(
      `کلیک روی: LatLng: ${ll.lat}, ${ll.lng} | UTM: ${utm.toString()}`
    );

    // غیرفعال کردن حالت انتخاب مختصات بعد از زدن نقطه
    disableCoordinatePickingToolbar();
  }
}

// تابع غیرفعال‌سازی مختصات برای تولبار
function disableCoordinatePickingToolbar() {
  widget_toolbar_isPickingCoordinates = false;
  const pickCoordinateBtn = document.getElementById("toolbar-pick-coordinate-btn");
  const coordinatesStopBtn = document.getElementById("toolbar-coordinates-stop");

  if (pickCoordinateBtn) pickCoordinateBtn.classList.remove("active");
  if (coordinatesStopBtn) coordinatesStopBtn.style.display = "none";

  const map = getMap();
  if (map) {
    map.getContainer().style.cursor = "";
    map.off("click", widget_toolbar_onMapClick);
  }
  console.log("ابزار انتخاب مختصات غیرفعال شد");
}

class WidgetToolbar {
  constructor() {
    this.toolbar = null;
    this.activeWidget = null;
    this.widgetForms = new Map();
    this.isDragging = false;
    this.dragOffset = { x: 0, y: 0 };

    this.widgets = [
      {
        id: 'maps',
        icon: 'fas fa-layer-group',
        title: 'نقشه‌ها',
        template: 'maps-widget'
      },
      {
        id: 'basemaps',
        icon: 'fas fa-satellite',
        title: 'تصاویر ماهواره',
        template: 'basemaps-widget'
      },
      {
        id: 'bookmarks',
        icon: 'fas fa-bookmark',
        title: 'بوک مارک',
        template: 'bookmarks-widget'
      },
      {
        id: 'descriptive-search',
        icon: 'fa fa-table',
        title: 'اطلاعات توصیفی',
        template: 'descriptive-search-widget'
      },
      {
        id: 'coordinates',
        icon: 'fas fa-map-marker-alt',
        title: 'مختصات',
        template: 'coordinates-widget'
      },
      {
        id: 'measure',
        icon: 'fa fa-ruler',
        title: 'اندازه‌گیری',
        template: 'measure-widget'
      },
      {
        id: 'draw',
        icon: 'fa fa-pencil-alt',
        title: 'ترسیم',
        template: 'draw-widget'
      },
      {
        id: 'ruler',
        icon: 'fas fa-ruler-horizontal',
        title: 'خط‌کش زاویه‌دار',
        template: 'ruler-widget'
      }
      // ,
      // {
      //   id: 'export',
      //   icon: 'fa fa-print',
      //   title: 'خروجی',
      //   template: 'export-widget'
      // }
      // ,
      // {
      //   id: 'user-panel',
      //   icon: 'fas fa-user',
      //   title: 'پنل کاربر',
      //   template: 'user-panel-widget'
      // }
    ];
  }

  // ایجاد نوار تولبار
  createToolbar() {
    this.toolbar = document.createElement('div');
    this.toolbar.className = 'widget-toolbar';
    this.toolbar.id = 'widget-toolbar';

    this.widgets.forEach(widget => {
      const button = this.createWidgetButton(widget);
      this.toolbar.appendChild(button);
    });

    document.body.appendChild(this.toolbar);
    this.setupEventListeners();

    // تنظیم موقعیت اولیه
    this.adjustToolbarPosition();

    // اضافه کردن کلاس برای انیمیشن ورود
    setTimeout(() => {
      this.toolbar.classList.add('toolbar-loaded');
    }, 100);
  }

  // ایجاد دکمه ویجت
  createWidgetButton(widget) {
    const button = document.createElement('button');
    button.className = 'widget-button';
    button.id = `widget-${widget.id}`;
    button.setAttribute('data-widget', widget.id); // اضافه کردن data-widget برای رنگ‌بندی

    // حذف title attribute در صورت وجود
    button.removeAttribute('title');

    button.innerHTML = `
      <i class="${widget.icon}"></i>
      <span class="tooltip">${widget.title}</span>
    `;

    button.addEventListener('click', () => this.toggleWidget(widget));

    return button;
  }

  // تنظیم رویدادها
  setupEventListeners() {
    // حذف رویداد کلیک خارج از فرم - فرم‌ها فقط با دکمه X بسته می‌شوند

    // رویدادهای کیبورد - فقط Escape برای بستن همه فرم‌ها
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        this.closeAllWidgets();
      }
    });

    // رویدادهای resize برای بهبود نمایش
    window.addEventListener('resize', () => {
      this.adjustToolbarPosition();
    });
  }

  // تنظیم موقعیت تولبار بر اساس اندازه صفحه
  adjustToolbarPosition() {
    if (this.toolbar) {
      const toolbarRect = this.toolbar.getBoundingClientRect();
      const windowWidth = window.innerWidth;

      // اگر تولبار از سمت راست خارج شده، آن را تنظیم کن
      if (toolbarRect.right > windowWidth - 10) {
        this.toolbar.style.right = '10px';
      }

      // اگر تولبار از سمت چپ خارج شده، آن را تنظیم کن
      if (toolbarRect.left < 10) {
        this.toolbar.style.left = '10px';
        this.toolbar.style.right = 'auto';
      }

      // تنظیم موقعیت فرم‌های فعال
      this.adjustActiveFormPosition();
    }
  }

  // تنظیم موقعیت فرم‌های فعال
  adjustActiveFormPosition() {
    if (this.activeWidget) {
      const form = this.widgetForms.get(this.activeWidget);
      if (form && form.classList.contains('active')) {
        const windowWidth = window.innerWidth;

        // تنظیم موقعیت بر اساس اندازه صفحه
        if (windowWidth < 1200) {
          form.style.right = '20px';
          form.style.left = 'auto';
        } else {
          form.style.right = '20px';
          form.style.left = 'auto';
        }

        // اطمینان از اینکه فرم در دید باشد
        form.style.top = '120px';
      }
    }
  }

  // باز/بسته کردن ویجت
  toggleWidget(widget) {
    if (this.activeWidget === widget.id) {
      this.closeWidget(widget.id);
    } else {
      // اجازه باز بودن چندین فرم همزمان - فقط فرم جدید را باز کن
      this.openWidget(widget);
    }
  }

  // باز کردن ویجت
  openWidget(widget) {
    this.activeWidget = widget.id;

    // فعال کردن دکمه
    const button = document.getElementById(`widget-${widget.id}`);
    if (button) button.classList.add('active');

    // ایجاد یا نمایش فرم
    this.showWidgetForm(widget);

    // تلاش برای بارگیری مجدد محتوا اگر خالی است
    setTimeout(() => {
      this.ensureWidgetContent(widget.id);
    }, 200);
  }

  // اطمینان از وجود محتوای ویجت
  ensureWidgetContent(widgetId) {
    const contentDiv = document.getElementById(`content-${widgetId}`);
    if (!contentDiv) return;

    // بررسی اینکه آیا محتوا خالی است یا نه
    const content = contentDiv.innerHTML.trim();
    if (content.includes('در حال بارگیری') || content.includes('هیچ') || content.length < 100) {
      console.log(`بارگیری مجدد محتوای ویجت: ${widgetId}`);
      this.loadWidgetContent(widgetId);
    }
  }

  // بستن ویجت
  closeWidget(widgetId) {
    // غیرفعال‌سازی خودکار ابزارهای تعاملی
    this.disableInteractiveTools(widgetId);

    this.activeWidget = null;

    // غیرفعال کردن دکمه
    const button = document.getElementById(`widget-${widgetId}`);
    if (button) button.classList.remove('active');

    // مخفی کردن فرم
    this.hideWidgetForm(widgetId);
  }

  // غیرفعال‌سازی ابزارهای تعاملی هنگام بسته شدن فرم
  disableInteractiveTools(widgetId) {
    switch (widgetId) {
      case 'measure':
        // فقط متوقف کردن اندازه‌گیری فعال - بدون حذف measureControl
        if (window.measureControl && window.measureControl._locked) {
          try {
            const cancelButton = window.measureControl._container.querySelector('.js-cancel');
            if (cancelButton) {
              cancelButton.click();
            }
          } catch (error) {
            console.warn('خطا در متوقف کردن اندازه‌گیری:', error);
          }
        }
        window.isMeasuring = false;
        const measureStopBtn = document.getElementById('toolbar-measure-stop');
        if (measureStopBtn) measureStopBtn.style.display = "none";
        console.log('اندازه‌گیری خودکار غیرفعال شد');
        break;

      case 'draw':
        if (window.activeDrawTool) {
          window.activeDrawTool.disable();
          window.activeDrawTool = null;
          console.log('ابزار ترسیم خودکار غیرفعال شد');
        }
        break;

      case 'ruler':
        if (window.isRulerActive && window.rulerControl) {
          window.rulerControl._toggleMeasure();
          window.isRulerActive = false;
          console.log('خط‌کش خودکار غیرفعال شد');
        }
        break;

      case 'coordinates':
        // غیرفعال کردن انتخاب مختصات از تولبار
        disableCoordinatePickingToolbar();
        console.log('انتخاب مختصات خودکار غیرفعال شد');
        break;
    }
  }

  // بستن همه ویجت‌ها
  closeAllWidgets() {
    // بستن همه فرم‌های باز
    this.widgetForms.forEach((form, widgetId) => {
      if (form.classList.contains('active')) {
        this.closeWidget(widgetId);
      }
    });
  }

  // کوچک کردن ویجت
  minimizeWidget(widgetId) {
    const form = this.widgetForms.get(widgetId);
    const button = document.getElementById(`widget-${widgetId}`);

    if (form && form.classList.contains('active')) {
      // کوچک کردن فرم
      form.classList.add('minimized');
      form.classList.remove('active');

      // تغییر وضعیت دکمه به نیمه فعال
      if (button) {
        button.classList.remove('active');
        button.classList.add('minimized');
      }

      // ایجاد آیکون کوچک در گوشه پایین چپ
      this.createMinimizedIcon(widgetId);

      console.log(`ویجت ${widgetId} کوچک شد`);
    }
  }

  // ایجاد آیکون کوچک شده
  createMinimizedIcon(widgetId) {
    // حذف آیکون قبلی اگر وجود دارد
    const existingIcon = document.getElementById(`minimized-${widgetId}`);
    if (existingIcon) {
      existingIcon.remove();
    }

    const widget = this.widgets.find(w => w.id === widgetId);
    if (!widget) return;

    // محاسبه موقعیت بر اساس تعداد آیکون‌های موجود
    const existingIcons = document.querySelectorAll('.minimized-widget-icon');
    const iconIndex = existingIcons.length;

    const icon = document.createElement('div');
    icon.id = `minimized-${widgetId}`;
    icon.className = 'minimized-widget-icon';
    icon.style.left = `${20 + (iconIndex * 60)}px`; // فاصله 60 پیکسل بین آیکون‌ها
    icon.innerHTML = `
      <i class="${widget.icon}"></i>
      <span class="minimized-tooltip">${widget.title}</span>
    `;

    icon.addEventListener('click', () => {
      this.restoreWidget(widgetId);
    });

    document.body.appendChild(icon);

    // اضافه کردن انیمیشن ورود
    setTimeout(() => {
      icon.style.transform = 'scale(1)';
    }, 10);
  }

  // بازگردانی ویجت از حالت کوچک
  restoreWidget(widgetId) {
    const form = this.widgetForms.get(widgetId);
    const button = document.getElementById(`widget-${widgetId}`);
    const minimizedIcon = document.getElementById(`minimized-${widgetId}`);

    if (form) {
      form.classList.remove('minimized');
      form.classList.add('active');

      if (button) {
        button.classList.remove('minimized');
        button.classList.add('active');
      }

      if (minimizedIcon) {
        minimizedIcon.remove();
      }

      this.activeWidget = widgetId;
      console.log(`ویجت ${widgetId} بازگردانی شد`);
    }
  }

  // نمایش فرم ویجت
  showWidgetForm(widget) {
    console.log(`نمایش فرم ویجت: ${widget.id}`);

    let form = this.widgetForms.get(widget.id);

    if (!form) {
      console.log(`ایجاد فرم جدید برای ویجت: ${widget.id}`);
      form = this.createWidgetForm(widget);
      this.widgetForms.set(widget.id, form);
      document.body.appendChild(form);
    }

    form.classList.add('active');

    // تنظیم موقعیت فرم
    this.adjustFormPosition(form);

    // فعال کردن ویجت مربوطه
    this.activateWidget(widget.id);

    // بررسی محتوای فرم
    const contentDiv = form.querySelector('.form-content');
    if (contentDiv) {
      console.log(`محتوای فرم ${widget.id}:`, contentDiv.innerHTML.substring(0, 200) + '...');
    }

    // اگر ویجت نقشه‌ها است، هماهنگ‌سازی فوری انجام بده
    if (widget.id === 'maps') {
      console.log('🎯 ویجت maps باز شد، شروع هماهنگ‌سازی...');
      // چندین تلاش با timeout های مختلف
      setTimeout(() => {
        console.log('🔄 تلاش اول هماهنگ‌سازی (100ms)');
        this.syncWithCurrentLayerStates();
      }, 100);

      setTimeout(() => {
        console.log('🔄 تلاش دوم هماهنگ‌سازی (300ms)');
        this.syncWithCurrentLayerStates();
      }, 300);

      setTimeout(() => {
        console.log('🔄 تلاش سوم هماهنگ‌سازی (500ms)');
        this.syncWithCurrentLayerStates();
      }, 500);
    }
  }

  // تنظیم موقعیت فرم
  adjustFormPosition(form) {
    const windowWidth = window.innerWidth;
    const formWidth = 400; // عرض پیش‌فرض فرم

    // تنظیم موقعیت افقی - اگر فرم از سمت راست خارج شد
    if (windowWidth < 1200) {
      form.style.right = '20px';
      form.style.left = 'auto';
    } else {
      form.style.right = '20px';
      form.style.left = 'auto';
    }

    // تنظیم موقعیت عمودی - زیر ناوبار و تولبار
    form.style.top = '120px';
    form.style.marginTop = '0';
  }

  // مخفی کردن فرم ویجت
  hideWidgetForm(widgetId) {
    const form = this.widgetForms.get(widgetId);
    if (form) {
      form.classList.add('closing');
      setTimeout(() => {
        form.classList.remove('active', 'closing');
        // غیرفعال کردن ویجت مربوطه
        this.deactivateWidget(widgetId);
      }, 300);
    }
  }

  // ایجاد فرم ویجت
  createWidgetForm(widget) {
    const form = document.createElement('div');
    form.className = 'widget-form';
    form.id = `form-${widget.id}`;
    form.setAttribute('data-widget', widget.id);

    form.innerHTML = `
      <div class="form-header" data-widget-id="${widget.id}">
        <h3>${widget.title}</h3>
        <div class="header-buttons">
          <button class="minimize-btn" onclick="window.widgetToolbar.minimizeWidget('${widget.id}')" title="کوچک کردن">
            <i class="fas fa-minus"></i>
          </button>
          <button class="close-btn" onclick="window.widgetToolbar.closeWidget('${widget.id}')" title="بستن">
            <i class="fas fa-times"></i>
          </button>
        </div>
      </div>
      <div class="form-content" id="content-${widget.id}">
        <!-- محتوای ویجت اینجا لود می‌شود -->
      </div>
    `;

    // اضافه کردن قابلیت جابجایی
    this.makeDraggable(form);

    // لود کردن محتوای ویجت
    this.loadWidgetContent(widget.id);

    return form;
  }

  // قابلیت جابجایی فرم
  makeDraggable(form) {
    const header = form.querySelector('.form-header');

    header.addEventListener('mousedown', (e) => {
      // جلوگیری از تداخل با عناصر نقشه
      e.preventDefault();
      e.stopPropagation();

      this.isDragging = true;
      const rect = form.getBoundingClientRect();
      this.dragOffset.x = e.clientX - rect.left;
      this.dragOffset.y = e.clientY - rect.top;

      // اضافه کردن کلاس برای نشان دادن حالت کشیدن
      form.classList.add('dragging');
      document.body.style.userSelect = 'none'; // جلوگیری از انتخاب متن

      const mouseMoveHandler = this.onMouseMove.bind(this);
      const mouseUpHandler = this.onMouseUp.bind(this);

      document.addEventListener('mousemove', mouseMoveHandler);
      document.addEventListener('mouseup', mouseUpHandler);

      // ذخیره handlers برای حذف بعدی
      this.currentMouseMoveHandler = mouseMoveHandler;
      this.currentMouseUpHandler = mouseUpHandler;
    });
  }

  onMouseMove(e) {
    if (!this.isDragging) return;

    // جلوگیری از تداخل با عناصر نقشه
    e.preventDefault();
    e.stopPropagation();

    const form = document.querySelector('.widget-form.dragging');
    if (form) {
      const x = e.clientX - this.dragOffset.x;
      const y = e.clientY - this.dragOffset.y;

      // محدود کردن حرکت در محدوده صفحه
      const maxX = window.innerWidth - form.offsetWidth;
      const maxY = window.innerHeight - form.offsetHeight;

      const constrainedX = Math.max(0, Math.min(x, maxX));
      const constrainedY = Math.max(0, Math.min(y, maxY));

      form.style.left = constrainedX + 'px';
      form.style.top = constrainedY + 'px';
      form.style.right = 'auto';
      form.style.transform = 'none';
    }
  }

  onMouseUp() {
    this.isDragging = false;

    // حذف کلاس dragging و بازگردانی انتخاب متن
    const form = document.querySelector('.widget-form.dragging');
    if (form) {
      form.classList.remove('dragging');
    }
    document.body.style.userSelect = '';

    // حذف event listeners
    if (this.currentMouseMoveHandler) {
      document.removeEventListener('mousemove', this.currentMouseMoveHandler);
    }
    if (this.currentMouseUpHandler) {
      document.removeEventListener('mouseup', this.currentMouseUpHandler);
    }
  }

  // لود کردن محتوای ویجت
  loadWidgetContent(widgetId) {
    const contentDiv = document.getElementById(`content-${widgetId}`);
    if (!contentDiv) return;

    // بر اساس نوع ویجت، محتوای مناسب را لود می‌کنیم
    switch (widgetId) {
      case 'maps':
        this.loadMapsContent(contentDiv);
        // تنظیم رویدادها بعد از لود محتوا
        setTimeout(() => {
          this.setupMapsEvents();
          this.setupToggleAllButton();
        }, 100);
        break;
      case 'basemaps':
        this.loadBasemapsContent(contentDiv);
        // تنظیم رویدادها بعد از لود محتوا
        setTimeout(() => {
          this.setupBasemapsEvents();
          // هماهنگ‌سازی فوری با وضعیت فعلی
          this.syncCurrentBasemapSelection();
        }, 100);
        break;
      case 'bookmarks':
        this.loadBookmarksContent(contentDiv);
        // تنظیم رویدادها بعد از لود محتوا
        setTimeout(() => this.setupBookmarksEvents(), 100);
        break;
      case 'descriptive-search':
        this.loadDescriptiveSearchContent(contentDiv);
        break;
      case 'coordinates':
        this.loadCoordinatesContent(contentDiv);
        // تنظیم رویدادها بعد از لود محتوا
        setTimeout(() => this.setupCoordinatesEvents(), 100);
        break;
      case 'measure':
        console.log('شروع لود ویجت measure، contentDiv:', !!contentDiv);
        this.loadMeasureContent(contentDiv);
        // تنظیم رویدادها بعد از لود محتوا
        setTimeout(() => this.setupMeasureEvents(), 100);
        break;
      case 'draw':
        this.loadDrawContent(contentDiv);
        // تنظیم رویدادها بعد از لود محتوا
        setTimeout(() => this.setupDrawEvents(), 100);
        break;
      case 'ruler':
        this.loadRulerContent(contentDiv);
        // تنظیم رویدادها بعد از لود محتوا
        setTimeout(() => this.setupRulerEvents(), 100);
        break;
      case 'export':
        this.loadExportContent(contentDiv);
        // تنظیم رویدادها بعد از لود محتوا
        setTimeout(() => this.setupExportEvents(), 100);
        break;
      case 'user-panel':
        this.loadUserPanelContent(contentDiv);
        // تنظیم رویدادها بعد از لود محتوا
        setTimeout(() => this.setupUserPanelEvents(), 100);
        break;
    }
  }

  // لود کردن محتوای نقشه‌ها
  loadMapsContent(contentDiv) {
    // استفاده مستقیم از تابع global generateLayerCheckboxes مثل ui.js
    contentDiv.innerHTML = `
      <div class="large-content">
        <!-- کنترل همه لایه‌ها -->
        <div class="toggle-all-container" style="margin-bottom: 15px;">
          <button id="toolbar-toggle-all-layers" class="toggle-all-btn" data-state="on">
            <i class="fas fa-toggle-on"></i>
            <span>غیرفعال کردن همه</span>
          </button>
        </div>
        <div id="toolbar-layer-checkboxes">${this.getLayerCheckboxesHTML()}</div>
      </div>
    `;

    // هماهنگ‌سازی فوری وضعیت چک‌باکس‌ها با وضعیت واقعی لایه‌ها
    setTimeout(() => {
      this.syncWithCurrentLayerStates();
    }, 100);
  }

  // دریافت HTML چک‌باکس‌های لایه (مشابه ui.js)
  getLayerCheckboxesHTML() {
    // اگر تابع global موجود است، از آن استفاده کن
    if (window.generateLayerCheckboxes && typeof window.generateLayerCheckboxes === 'function') {
      try {
        const html = window.generateLayerCheckboxes();
        // تغییر ID ها برای تولبار
        return html.replace(/id="layer-/g, 'id="toolbar-layer-');
      } catch (error) {
        console.error('خطا در استفاده از generateLayerCheckboxes global:', error);
      }
    }

    // اگر تابع global موجود نیست، خودمان تولید کنیم
    if (config_getLayersConfig && typeof config_getLayersConfig === 'function') {
      try {
        const layersConfig = config_getLayersConfig();
        if (layersConfig && layersConfig.length > 0) {
          let layersHTML = "";
          layersConfig.forEach((layer, index) => {
            // بررسی وضعیت واقعی لایه در نقشه
            let isChecked = "";
            if (window.getMap && window.getFeatureLayers) {
              const map = window.getMap();
              const featureLayers = window.getFeatureLayers();
              const layerObj = featureLayers[layer.name];
              if (map && layerObj && map.hasLayer(layerObj)) {
                isChecked = "checked";
              }
            } else {
              // fallback به کانفیگ اولیه
              isChecked = (layer.visible !== false) ? "checked" : "";
            }

            layersHTML += `
              <div class="mb-3 layer-item">
                <div class="d-flex align-items-center" style="flex-wrap: nowrap;">
                  <div class="form-check d-flex align-items-center flex-grow-1">
                    <input type="checkbox" id="toolbar-layer-${index}" ${isChecked} onchange="window.toggleLayer('${layer.name}', this.checked)" style="margin-top: 0;">
                    <label for="toolbar-layer-${index}" class="form-check-label large-text ms-2">
                      <i class="fa ${layer.icon}"></i> ${layer.name}
                    </label>
                  </div>
                </div>
              </div>
            `;
          });
          return layersHTML;
        }
      } catch (error) {
        console.error('خطا در تولید چک‌باکس‌های لایه:', error);
      }
    }

    return '<p>لایه‌ها هنوز بارگیری نشده‌اند...</p>';
  }

  // لود کردن محتوای تصاویر ماهواره
  loadBasemapsContent(contentDiv) {
    contentDiv.innerHTML = `
      <div class="large-content">
        <div class="basemap-options">

          <div class="basemap-item">
            <input type="radio" id="toolbar-base-googlesat" name="toolbar-base-layer" value="googlesat">
            <label for="toolbar-base-googlesat">تصویر گوگل</label>
          </div>
          <div class="basemap-item">
            <input type="radio" id="toolbar-base-googlemap" name="toolbar-base-layer" value="googlemap">
            <label for="toolbar-base-googlemap">گوگل مپ</label>
          </div>

          <div class="basemap-item">
            <input type="radio" id="toolbar-base-osm" name="toolbar-base-layer" value="osm">
            <label for="toolbar-base-osm">اوپن استریت</label>
          </div>

          <div class="basemap-item">
            <input type="radio" id="toolbar-base-nobasemap" name="toolbar-base-layer" value="nobasemap">
            <label for="toolbar-base-nobasemap">بدون تصویر</label>
          </div>

        </div>
      </div>
    `;

    // هماهنگ‌سازی فوری انتخاب فعلی بعد از لود محتوا
    setTimeout(() => {
      this.syncCurrentBasemapSelection();
    }, 50);

    // هماهنگ‌سازی اضافی با تاخیر بیشتر
    setTimeout(() => {
      this.syncCurrentBasemapSelection();
    }, 200);
  }

  // لود کردن محتوای بوکمارک
  loadBookmarksContent(contentDiv) {
    contentDiv.innerHTML = `
      <div id="toolbar-bookmarks-container" class="large-content">

         <button id="toolbar-add-bookmark-btn" class="btn btn-primary bookmark-btn">اضافه کردن بوکمارک</button>
         <div id="toolbar-bookmarks-list"></div>
       </div>
    `;

    // اضافه کردن رویدادها
    this.setupBookmarksEvents();
  }

  // لود کردن محتوای اطلاعات توصیفی
  loadDescriptiveSearchContent(contentDiv) {
    contentDiv.innerHTML = `
      <div class="large-content">

        <div style="margin-top: 10px;">
          <label for="toolbar-layer-dropdown" style="font-size: 14px; color: #555;">انتخاب لایه:</label>
          <select id="toolbar-layer-dropdown" style="width: 100%; padding: 8px; font-size: 14px; border: 1px solid #ddd; border-radius: 4px;">
            <option value="" disabled selected>در حال بارگیری...</option>
          </select>
        </div>
        <div id="toolbar-layer-tabledown" style="margin-top: 20px;"></div>
      </div>
    `;

    // پر کردن کمبوباکس لایه‌ها با تأخیر
    setTimeout(() => {
      this.populateLayerDropdown();
    }, 500);
  }

  // لود کردن محتوای مختصات
  loadCoordinatesContent(contentDiv) {
    contentDiv.innerHTML = `
      <div class="large-content">

        <form id="toolbar-latlng-to-utm" class="coordinate-form">
          <div class="form-group">
            <label>عرض جغرافیایی (Latitude):</label>
            <input type="text" id="toolbar-lat" value="0" size="10">
          </div>
          <div class="form-group">
            <label>طول جغرافیایی (Longitude):</label>
            <input type="text" id="toolbar-lng" value="0" size="10">
          </div>
          <button type="button" id="toolbar-to-utm-btn" class="btn-coordinate">تبدیل به UTM</button>
          <p>نتیجه UTM: <span id="toolbar-utm-result">---</span></p>
        </form>
        <div class="form-spacer"></div>
        <form id="toolbar-utm-to-latlng" class="coordinate-form">
          <div class="form-group">
            <label>عرض (X):</label>
            <input type="text" id="toolbar-utm-x" value="" size="9">
          </div>
          <div class="form-group">
            <label>طول (Y):</label>
            <input type="text" id="toolbar-utm-y" value="" size="9">
          </div>
          <div class="form-group">
            <label>زون (Zone):</label>
            <input type="text" id="toolbar-utm-zone" value="" size="2" maxlength="2">
          </div>
          <div class="form-group">
            <label>باند (Band):</label>
            <input type="text" id="toolbar-utm-band" value="" size="2" maxlength="2">
          </div>
          <div class="form-group">
            <label>North Hemi (اختیاری):</label>
            <input type="text" id="toolbar-utm-southhemi" value="" size="5" maxlength="5" title="فقط اگر Band خالی باشد">
          </div>
          <button type="button" id="toolbar-to-latlng-btn" class="btn-coordinate">تبدیل به LatLng</button>
          <p>نتیجه LatLng: <span id="toolbar-latlng-result">---</span></p>
        </form>
        <button type="button" id="toolbar-pick-coordinate-btn" class="btn-coordinate btn-tool" title="انتخاب مختصات با کلیک">
          <i class="fas fa-crosshairs"></i> نمایش مختصات
        </button>
        <button type="button" id="toolbar-coordinates-stop" class="btn btn-danger" style="display:none;" title="غیرفعال کردن انتخاب مختصات">
          <i class="fas fa-stop"></i> غیرفعال
        </button>
        <button type="button" id="toolbar-delete-markers-btn" class="btn-coordinate btn-delete">حذف نشانگرها</button>
      </div>
    `;

    // اضافه کردن رویدادها
    this.setupCoordinatesEvents();
  }

  // لود کردن محتوای اندازه‌گیری
  loadMeasureContent(contentDiv) {
    console.log('شروع لود محتوای اندازه‌گیری...');

    const htmlContent = `
      <div class="large-content">
        <div class="measure-tools">
          <button id="toolbar-measure-start" title="شروع/پایان اندازه‌گیری" style="font-size: 24px;">
            <i class="fas fa-ruler-combined"></i>
          </button>
          <button id="toolbar-measure-stop" class="btn btn-danger" style="display:none; margin-left: 10px;" title="غیرفعال کردن اندازه‌گیری">
            <i class="fas fa-stop"></i> غیرفعال
          </button>
          <select id="toolbar-unit-length" style="margin-left: 10px; font-size: 16px;">
            <option value="kilometers" selected>کیلومتر</option>
            <option value="meters">متر</option>
            <option value="miles">مایل</option>
          </select>
          <select id="toolbar-unit-area" style="margin-left: 10px; font-size: 16px;">
            <option value="sqmeters" selected>متر مربع</option>
            <option value="sqkilometers">کیلومتر مربع</option>
            <option value="hectares">هکتار</option>
            <option value="sqmiles">مایل مربع</option>
          </select>
        </div>
        <p style="margin-top: 10px; font-size: 13px; color: #666; line-height: 1.4;">نکته: برای شروع، روی دکمه کلیک کنید و نقاط را روی نقشه انتخاب کنید. برای پایان، دابل‌کلیک کنید.</p>
        <div id="toolbar-measure-results" style="margin-top: 10px; font-size: 14px;"></div>
      </div>
    `;

    contentDiv.innerHTML = htmlContent;

    console.log('محتوای اندازه‌گیری لود شد');
    console.log('contentDiv بعد از لود:', contentDiv.innerHTML.substring(0, 200) + '...');

    // بررسی فوری وجود دکمه‌ها
    setTimeout(() => {
      const testBtn = document.getElementById('toolbar-measure-start');
      console.log('تست فوری دکمه:', !!testBtn);
    }, 50);
  }

  // لود کردن محتوای ترسیم
  loadDrawContent(contentDiv) {
    contentDiv.innerHTML = `
      <div class="large-content">

        <div class="draw-tools">
          <button id="toolbar-draw-polyline" title="خط"><i class="fas fa-slash"></i></button>
          <button id="toolbar-draw-polygon" title="چندضلعی"><i class="fas fa-draw-polygon"></i></button>
          <button id="toolbar-draw-circle" title="دایره"><i class="fas fa-circle"></i></button>
          <button id="toolbar-draw-rectangle" title="مستطیل"><i class="fas fa-square"></i></button>
          <button id="toolbar-draw-marker" title="نشانگر"><i class="fas fa-map-marker-alt"></i></button>
          <button id="toolbar-edit-layers" title="ویرایش"><i class="fas fa-edit"></i></button>
          <button id="toolbar-remove-layers" title="حذف"><i class="fas fa-trash"></i></button>
          <button id="toolbar-save-edit" title="ذخیره ویرایش" style="display: none;"><i class="fas fa-save"></i></button>
        </div>
        <div class="draw-controls" style="margin-top: 10px;">
          <button id="toolbar-draw-stop" class="btn btn-danger" style="display:none;" title="غیرفعال کردن ترسیم">
            <i class="fas fa-stop"></i> غیرفعال کردن ترسیم
          </button>
        </div>
      </div>
    `;

    // اضافه کردن رویدادها
    this.setupDrawEvents();
  }

  // لود کردن محتوای خط‌کش
  loadRulerContent(contentDiv) {
    contentDiv.innerHTML = `
      <div class="large-content">

        <div class="ruler-tools">
          <button id="toolbar-ruler-start" title="شروع/پایان خط‌کش" style="font-size: 24px;">
            <i class="fas fa-ruler-horizontal"></i>
          </button>
          <button id="toolbar-ruler-stop" class="btn btn-danger" style="display:none; margin-left: 10px;" title="غیرفعال کردن خط‌کش">
            <i class="fas fa-stop"></i> غیرفعال
          </button>
        </div>
        <p style="margin-top: 10px; font-size: 13px; color: #666; line-height: 1.4;">نکته: برای شروع، روی دکمه کلیک کنید و نقاط را روی نقشه انتخاب کنید. برای پایان، دابل‌کلیک کنید.</p>
      </div>
    `;

    // اضافه کردن رویدادها
    this.setupRulerEvents();
  }

  // لود کردن محتوای خروجی
  loadExportContent(contentDiv) {
    contentDiv.innerHTML = `
      <div class="large-content">

        <div class="print-tools">
          <button id="toolbar-print-landscape" title="لندسکیپ"><i class="fas fa-arrows-alt-h"></i></button>
          <button id="toolbar-print-portrait" title="پورتریت"><i class="fas fa-arrows-alt-v"></i></button>
          <button id="toolbar-print-auto" title="خودکار"><i class="fas fa-sync-alt"></i></button>
          <button id="toolbar-print-a4" title="A4"><i class="fas fa-file"></i></button>
          <button id="toolbar-print-a3" title="A3"><i class="fas fa-file-alt"></i></button>
        </div>
      </div>
    `;

    // اضافه کردن رویدادها
    this.setupExportEvents();
  }



  // لود کردن محتوای پنل کاربر
  // loadUserPanelContent(contentDiv) {
  //   const username = localStorage.getItem("username") || "کاربر";
  //   const isAdmin = username === "admin";

  //   const userGreeting = isAdmin
  //     ? `<p style="color: green; font-size: 16px; font-weight: bold;">سلام ${username}</p>
  //        <p style="color: green; font-size: 14px;">کاربر ادمین</p>`
  //     : `<p style="font-size: 16px;">سلام ${username}</p>
  //        <p style="font-size: 14px;">کاربر عادی</p>`;

  //   contentDiv.innerHTML = `
  //     <div class="user-panel-content">
  //       ${userGreeting}
  //       <hr style="margin: 10px 0;">
  //       <p style="font-size: 14px;">امکانات کاربری:</p>
  //       <ul style="list-style-type: none; padding: 0;">
  //         <li><button id="toolbar-change-password-btn" class="change-password-btn">تغییر رمز عبور</button></li>
  //         <li><button id="toolbar-logout-btn-sidebar" class="logout-btn-sidebar">خروج از سیستم</button></li>
  //       </ul>
  //     </div>
  //   `;

  //   // اضافه کردن رویدادها
  //   this.setupUserPanelEvents();
  // }



  // پر کردن کمبوباکس لایه‌ها
  populateLayerDropdown() {
    const layerDropdown = document.getElementById('toolbar-layer-dropdown');
    if (!layerDropdown) {
      console.warn('toolbar-layer-dropdown element not found');
      return;
    }

    // استفاده از getLayersConfig مستقیم
    if (!config_getLayersConfig) {
      console.warn('getLayersConfig هنوز آماده نیست');
      layerDropdown.innerHTML = '<option value="" disabled selected>در حال بارگیری...</option>';
      return;
    }

    try {
      const layersConfig = config_getLayersConfig();
      if (!layersConfig || layersConfig.length === 0) {
        layerDropdown.innerHTML = '<option value="" disabled selected>هیچ لایه‌ای یافت نشد</option>';
        return;
      }

      layerDropdown.innerHTML = '<option value="" disabled selected>لایه را انتخاب کنید</option>';
      layersConfig.forEach((layer, index) => {
        layerDropdown.innerHTML += `<option value="${index}">${layer.name}</option>`;
      });

      // اضافه کردن رویداد تغییر
      layerDropdown.addEventListener('change', function() {
        const selectedIndex = parseInt(this.value, 10);
        if (!isNaN(selectedIndex) && window.loadLayers && window.toggleLayer && window.switchView) {
          console.log("انتخاب اندیس از کمبو‌باکس تولبار:", selectedIndex);
          const layerName = layersConfig[selectedIndex].name;
          window.toggleLayer(layerName, true); // لایه رو فعال می‌کنه
          window.loadLayers(selectedIndex); // جدول رو لود می‌کنه
          console.log(`لایه ${layerName} انتخاب و روشن شد`);
          window.switchView("split"); // نمایش جدول و نقشه به‌صورت اسپلیت
        } else {
          console.error("اندیس نامعتبر از کمبوباکس:", this.value);
        }
      });
    } catch (error) {
      console.error('خطا در پر کردن کمبوباکس لایه‌ها:', error);
      layerDropdown.innerHTML = '<option value="" disabled selected>خطا در بارگیری</option>';
    }
  }

  // بررسی آمادگی وابستگی‌ها
  checkDependencies() {
    const dependencies = {
      getLayersConfig: !!window.getLayersConfig,
      getMap: !!window.getMap,
      L: !!window.L,
      measureControl: !!window.measureControl,
      rulerControl: !!window.rulerControl,
      printControl: !!window.printControl,
      coordinateMarkers: !!window.coordinateMarkers,
      bookmarkControl: !!window.bookmarkControl,
      isMeasuring: window.isMeasuring !== undefined,
      isRulerActive: window.isRulerActive !== undefined,
      selectedLengthUnit: !!window.selectedLengthUnit,
      selectedAreaUnit: !!window.selectedAreaUnit
    };

    console.log('وضعیت وابستگی‌ها:', dependencies);

    // هشدار برای dependencies مهم
    if (!dependencies.measureControl) {
      console.warn('measureControl در دسترس نیست - ویجت اندازه‌گیری کار نخواهد کرد');
    }
    if (!dependencies.rulerControl) {
      console.warn('rulerControl در دسترس نیست - ویجت خط‌کش کار نخواهد کرد');
    }

    return dependencies;
  }
  // تابع انتظار برای آماده شدن dependencies
  waitForDependencies(callback, maxAttempts = 50) {
    let attempts = 0;
    const checkInterval = setInterval(() => {
      attempts++;
      const deps = this.checkDependencies();

      if (deps.measureControl && deps.rulerControl && deps.getMap && deps.L) {
        clearInterval(checkInterval);
        console.log('همه dependencies آماده شدند');
        if (callback) callback();
      } else if (attempts >= maxAttempts) {
        clearInterval(checkInterval);
        console.error('Timeout: برخی dependencies آماده نشدند');
        if (callback) callback(); // ادامه با محدودیت
      }
    }, 100);
  }

  // فعال کردن ویجت
  activateWidget(widgetId) {
    // این تابع برای فعال کردن ویجت‌های مختلف استفاده می‌شود
    console.log(`ویجت ${widgetId} فعال شد`);

    // بررسی وابستگی‌ها
    this.checkDependencies();
  }

  // غیرفعال کردن ویجت
  deactivateWidget(widgetId) {
    // این تابع برای غیرفعال کردن ویجت‌های مختلف استفاده می‌شود
    console.log(`ویجت ${widgetId} غیرفعال شد`);
  }

  // تنظیم رویدادهای نقشه‌ها
  setupMapsEvents() {
    // رویدادهای مربوط به لایه‌ها - حذف شده چون دکمه کشویی جایگزین شده
  }

  // تنظیم دکمه کشویی همه لایه‌ها
  setupToggleAllButton() {
    const toggleAllBtn = document.getElementById('toolbar-toggle-all-layers');
    if (toggleAllBtn) {
      toggleAllBtn.onclick = function() {
        const currentState = this.getAttribute('data-state');
        const newState = currentState === 'on' ? 'off' : 'on';
        const isOn = newState === 'on';

        // تغییر وضعیت دکمه
        this.setAttribute('data-state', newState);

        // تغییر متن و آیکون
        const icon = this.querySelector('i');
        const span = this.querySelector('span');

        if (isOn) {
          icon.className = 'fas fa-toggle-on';
          span.textContent = 'غیرفعال کردن همه';
        } else {
          icon.className = 'fas fa-toggle-off';
          span.textContent = 'فعال کردن همه';
        }

        // اعمال تغییرات روی لایه‌ها
        if (window.toggleAllLayers) {
          window.toggleAllLayers(isOn);
        }

        console.log(`همه لایه‌ها ${isOn ? 'فعال' : 'غیرفعال'} شدند`);
      };
    }
  }

  // تنظیم رویدادهای تصاویر ماهواره
  setupBasemapsEvents() {
    // هماهنگ‌سازی انتخاب فعلی با ناوبار
    this.syncCurrentBasemapSelection();

    // اضافه کردن event listener برای تغییرات رادیو باتن‌های تولبار
    const toolbarRadios = [
      'toolbar-base-googlesat', 'toolbar-base-googlemap',
      'toolbar-base-osm', 'toolbar-base-nobasemap'
    ];

    toolbarRadios.forEach(radioId => {
      const radio = document.getElementById(radioId);
      if (radio) {
        radio.addEventListener('change', (e) => {
          if (e.target.checked) {
            const basemapType = radioId.replace('toolbar-base-', '');
            console.log(`تغییر تصویر ماهواره از تولبار: ${basemapType}`);

            // فراخوانی تابع switchBaseLayer برای تغییر واقعی نقشه
            if (window.switchBaseLayer) {
              window.switchBaseLayer(basemapType, true); // true = fromToolbar
            }
          }
        });
      }
    });

    console.log('رویدادهای تصاویر ماهواره تولبار تنظیم شد');
  }

  // هماهنگ‌سازی انتخاب فعلی تصویر ماهواره
  syncCurrentBasemapSelection() {
    // نقشه نام‌های لایه به متن فارسی
    const basemapNames = {
      googlesat: 'تصویر گوگل',
      googlemap: 'گوگل مپ',
      osm: 'اوپن استریت',
      nobasemap: 'بدون تصویر'
    };

    // بررسی کدام تصویر ماهواره در حال حاضر فعال است
    const map = window.getMap();
    if (!map) return;

    // پیدا کردن لایه فعال فعلی
    let currentBasemap = 'osm'; // پیش‌فرض اوپن استریت

    // بررسی لایه‌های موجود در نقشه
    let foundBasemap = false;
    map.eachLayer(function(layer) {
      if (layer instanceof L.TileLayer && !foundBasemap) {
        // بررسی URL برای تشخیص نوع تصویر ماهواره
        const url = layer._url || '';
        console.log('بررسی URL لایه:', url);

        if (url.includes('lyrs=s') || url.includes('/sat/')) {
          currentBasemap = 'googlesat';
          foundBasemap = true;
        } else if (url.includes('lyrs=m') || url.includes('/map/')) {
          currentBasemap = 'googlemap';
          foundBasemap = true;
        } else if (url.includes('/osm/') || url.includes('openstreetmap.org')) {
          currentBasemap = 'osm';
          foundBasemap = true;
        }
      }
    });

    // اگر هیچ TileLayer پیدا نشد، بدون تصویر است
    if (!foundBasemap) {
      let hasEmptyLayerGroup = false;
      map.eachLayer(function(layer) {
        if (layer instanceof L.LayerGroup && layer.getLayers().length === 0) {
          hasEmptyLayerGroup = true;
        }
      });
      if (hasEmptyLayerGroup) {
        currentBasemap = 'nobasemap';
      }
    }

    // تنظیم رادیو باتن مناسب در تولبار
    const toolbarRadio = document.getElementById(`toolbar-base-${currentBasemap}`);
    if (toolbarRadio) {
      toolbarRadio.checked = true;
    }

    // هماهنگ‌سازی رادیو باتن‌های ناوبار
    const navbarRadio = document.getElementById(`base-${currentBasemap}`);
    if (navbarRadio) {
      navbarRadio.checked = true;
    }

    // 🆕 به‌روزرسانی عنوان منوی ناوبار
    if (window.updateNavbarBasemapTitle) {
      window.updateNavbarBasemapTitle(currentBasemap, basemapNames[currentBasemap]);
    }

    console.log(`تصویر ماهواره فعلی در widget-toolbar: ${currentBasemap}`);
  }

  // تابع مستقیم برای تنظیم انتخاب تصویر ماهواره (بدون بررسی نقشه)
  setBasemapSelection(layerName) {
    console.log(`تنظیم مستقیم انتخاب تولبار: ${layerName}`);

    // نقشه نام‌های لایه به متن فارسی
    const basemapNames = {
      googlesat: 'تصویر گوگل',
      googlemap: 'گوگل مپ',
      osm: 'اوپن استریت',
      nobasemap: 'بدون تصویر'
    };

    // پاک کردن همه انتخاب‌های تولبار
    const toolbarRadios = [
      'toolbar-base-googlesat', 'toolbar-base-googlemap',
      'toolbar-base-osm', 'toolbar-base-nobasemap'
    ];

    toolbarRadios.forEach(radioId => {
      const radio = document.getElementById(radioId);
      if (radio) {
        radio.checked = false;
      }
    });

    // انتخاب رادیو باتن مناسب در تولبار
    const toolbarRadio = document.getElementById(`toolbar-base-${layerName}`);
    if (toolbarRadio) {
      toolbarRadio.checked = true;
      console.log(`رادیو باتن تولبار ${layerName} انتخاب شد`);
    } else {
      console.warn(`رادیو باتن تولبار ${layerName} پیدا نشد`);
    }
  }

  // تنظیم رویدادهای بوکمارک (کپی مستقیم از ui.js)
  setupBookmarksEvents() {
    const map = window.getMap();
    if (!window.bookmarkControl) {
      console.error("کنترل بوکمارک تعریف نشده است!");
      const bookmarksList = document.getElementById("toolbar-bookmarks-list");
      if (bookmarksList) {
        bookmarksList.innerHTML = "<p>خطا: بوکمارک‌ها لود نشدند.</p>";
      }
      return;
    }

    const bookmarksList = document.getElementById("toolbar-bookmarks-list");
    if (bookmarksList) {
      this.updateBookmarksList(bookmarksList, window.bookmarkControl);
    }

    const addBookmarkBtn = document.getElementById("toolbar-add-bookmark-btn");
    if (addBookmarkBtn) {
      addBookmarkBtn.onclick = function () {
        const latlng = map.getCenter();
        map.fire("bookmark:new", { latlng });
      };
    }

    map.on("contextmenu", function (e) {
      const activeForm = document.querySelector('.widget-form.active');
      if (activeForm && activeForm.id === 'form-bookmarks') {
        map.fire("bookmark:new", { latlng: e.latlng });
      }
    });

    map.on(
      "bookmark:add bookmark:removed bookmark:remove",
      (e) => {
        console.log("رویداد دریافت شد:", e.type, e.data);
        if (bookmarksList) {
          this.updateBookmarksList(bookmarksList, window.bookmarkControl);
        }
      }
    );
  }

  // به‌روزرسانی لیست بوکمارک‌ها
  updateBookmarksList(container, control) {
    if (!control || !control._storage) {
      console.error("کنترل یا _storage تعریف نشده است!");
      container.innerHTML = "<p>خطا: بوکمارک‌ها لود نشدند.</p>";
      return;
    }

    control._storage.getAllItems(function (bookmarks) {
      container.innerHTML = "";
      if (!bookmarks || bookmarks.length === 0) {
        container.innerHTML = "<p>هیچ بوکمارکی وجود ندارد.</p>";
        return;
      }

      const ul = document.createElement("ul");
      bookmarks.forEach(function (bookmark) {
        const li = document.createElement("li");
        li.innerHTML = `
          <span>${bookmark.name || "بدون نام"}</span>
          <span>(${bookmark.latlng[0].toFixed(6)}, ${bookmark.latlng[1].toFixed(6)})</span>
          <button class="zoom-to" data-lat="${bookmark.latlng[0]}" data-lng="${bookmark.latlng[1]}">بزرگ‌نمایی</button>
          <button class="remove" data-id="${bookmark.id}">حذف</button>
        `;
        ul.appendChild(li);
      });
      container.appendChild(ul);

      // رویدادهای دکمه‌ها
      container.querySelectorAll(".zoom-to").forEach(function (btn) {
        btn.onclick = function () {
          const lat = parseFloat(btn.dataset.lat);
          const lng = parseFloat(btn.dataset.lng);
          if (window.getMap) {
            window.getMap().setView([lat, lng], 13);
          }
        };
      });

      container.querySelectorAll(".remove").forEach(function (btn) {
        btn.onclick = function () {
          const id = btn.dataset.id;
          if (id && window.getMap) {
            const bookmarkToRemove = bookmarks.find((b) => b.id === id);
            if (bookmarkToRemove) {
              window.getMap().fire("bookmark:remove", { data: bookmarkToRemove });
            }
          }
        };
      });
    });
  }

  // تنظیم رویدادهای مختصات (کپی مستقیم از ui.js)
  setupCoordinatesEvents() {
    const latInput = document.getElementById("toolbar-lat");
    const lngInput = document.getElementById("toolbar-lng");
    const utmXInput = document.getElementById("toolbar-utm-x");
    const utmYInput = document.getElementById("toolbar-utm-y");
    const utmZoneInput = document.getElementById("toolbar-utm-zone");
    const utmBandInput = document.getElementById("toolbar-utm-band");
    const utmSouthHemiInput = document.getElementById("toolbar-utm-southhemi");
    const utmResult = document.getElementById("toolbar-utm-result");
    const latlngResult = document.getElementById("toolbar-latlng-result");

    let isPickingCoordinates = false;

    // تابع کمکی برای غیرفعال کردن حالت انتخاب مختصات
    const disableCoordinatePicking = () => {
      isPickingCoordinates = false;
      const pickCoordinateBtn = document.getElementById("toolbar-pick-coordinate-btn");
      const coordinatesStopBtn = document.getElementById("toolbar-coordinates-stop");

      if (pickCoordinateBtn) {
        pickCoordinateBtn.classList.remove("active");
      }
      if (coordinatesStopBtn) {
        coordinatesStopBtn.style.display = "none";
      }

      const map = window.getMap();
      if (map) {
        map.getContainer().style.cursor = "";
        map.off("click", widget_toolbar_onMapClick);
      }
      console.log("ابزار انتخاب مختصات غیرفعال شد");
    };

    // کپی مستقیم از ui.js
    const toUtmBtn = document.getElementById("toolbar-to-utm-btn");
    if (toUtmBtn) {
      toUtmBtn.onclick = function () {
        if (utmResult) utmResult.innerHTML = "---";
        const ll = window.L.latLng(latInput.value, lngInput.value);
        const utm = ll.utm();
        if (utmResult) {
          utmResult.innerHTML = `${utm.x.toFixed(1)}, ${utm.y.toFixed(1)}, Zone: ${utm.zone}, Band: ${utm.band}, Hemi: ${utm.southHemi ? "S" : "N"}`;
        }
        if (utmXInput) utmXInput.value = utm.x.toFixed(1);
        if (utmYInput) utmYInput.value = utm.y.toFixed(1);
        if (utmZoneInput) utmZoneInput.value = utm.zone;
        if (utmBandInput) utmBandInput.value = utm.band;
        if (utmSouthHemiInput) utmSouthHemiInput.value = utm.southHemi ? "S" : "N";

        const map = window.getMap();
        const marker = window.L.marker(ll).addTo(window.coordinateMarkers);
        marker
          .bindPopup(`UTM: ${utm.toString()}<br>LatLng: ${ll}`)
          .openPopup();
        map.setView(ll, map.getZoom());
      };
    }

    const toLatlngBtn = document.getElementById("toolbar-to-latlng-btn");
    if (toLatlngBtn) {
      toLatlngBtn.onclick = function () {
        if (latlngResult) latlngResult.innerHTML = "---";
        const sh = utmSouthHemiInput.value.toUpperCase();
        const southHemi = ["S", "TRUE", "Y", "YES", "1"].indexOf(sh) > -1;
        const utm = window.L.utm(
          utmXInput.value,
          utmYInput.value,
          utmZoneInput.value,
          utmBandInput.value,
          southHemi
        );
        const ll = utm.latLng();
        if (ll) {
          if (latlngResult) {
            latlngResult.innerHTML = `Lat: ${ll.lat.toFixed(6)}, Lng: ${ll.lng.toFixed(6)}`;
          }
          if (latInput) latInput.value = ll.lat.toFixed(6);
          if (lngInput) lngInput.value = ll.lng.toFixed(6);

          const map = window.getMap();
          const marker = window.L.marker(ll).addTo(window.coordinateMarkers);
          marker
            .bindPopup(`UTM: ${utm.toString()}<br>LatLng: ${ll}`)
            .openPopup();
          map.setView(ll, map.getZoom());
        }
      };
    }

    const deleteMarkersBtn = document.getElementById("toolbar-delete-markers-btn");
    if (deleteMarkersBtn) {
      deleteMarkersBtn.onclick = function () {
        window.coordinateMarkers.clearLayers();
        console.log("همه نشانگرها حذف شدند");
        // غیرفعال کردن حالت انتخاب مختصات بعد از حذف نشانگرها
        if (isPickingCoordinates) {
          disableCoordinatePicking();
        }
      };
    }

    // استفاده از تابع global onMapClick

    const pickCoordinateBtn = document.getElementById("toolbar-pick-coordinate-btn");
    const coordinatesStopBtn = document.getElementById("toolbar-coordinates-stop");

    if (pickCoordinateBtn) {
      pickCoordinateBtn.onclick = function() {
        const map = window.getMap ? window.getMap() : null;
        if (!map) return;

        if (!isPickingCoordinates) {
          isPickingCoordinates = true;
          pickCoordinateBtn.classList.add("active");
          map.getContainer().style.cursor = "crosshair";

          // نمایش دکمه غیرفعال‌سازی
          if (coordinatesStopBtn) coordinatesStopBtn.style.display = "inline-block";

          map.on("click", widget_toolbar_onMapClick);
          console.log("ابزار انتخاب مختصات فعال شد");
        } else {
          disableCoordinatePickingToolbar();
        }
      };
    }

    // دکمه غیرفعال‌سازی مختصات
    if (coordinatesStopBtn) {
      coordinatesStopBtn.onclick = function () {
        disableCoordinatePickingToolbar();
        console.log("ابزار انتخاب مختصات غیرفعال شد");
      };
    }

    if (deleteMarkersBtn) {
      deleteMarkersBtn.addEventListener('click', function() {
        // حذف نشانگرها
        if (window.coordinateMarkers) {
          window.coordinateMarkers.clearLayers();
          console.log("همه نشانگرها حذف شدند");
        }
        // غیرفعال کردن حالت انتخاب مختصات بعد از حذف نشانگرها
        if (isPickingCoordinates) {
          disableCoordinatePicking();
        }
      });
    }
  }

  // تابع تنظیم event listener ها
  setupEventListeners() {
    console.log('تلاش برای پیدا کردن دکمه‌های اندازه‌گیری...');

    const measureStartBtn = document.getElementById('toolbar-measure-start');
    const measureStopBtn = document.getElementById('toolbar-measure-stop');
    const unitLengthSelect = document.getElementById('toolbar-unit-length');
    const unitAreaSelect = document.getElementById('toolbar-unit-area');

    console.log('وضعیت دکمه‌ها:', {
      measureStartBtn: !!measureStartBtn,
      measureStopBtn: !!measureStopBtn,
      unitLengthSelect: !!unitLengthSelect,
      unitAreaSelect: !!unitAreaSelect
    });

    if (!measureStartBtn) {
      console.error('دکمه شروع اندازه‌گیری پیدا نشد - احتمالاً محتوا هنوز لود نشده');

      // بررسی کنیم که آیا محتوا اصلاً وجود دارد
      const contentDiv = document.querySelector('.widget-content');
      if (contentDiv) {
        console.log('محتوای فعلی contentDiv:', contentDiv.innerHTML.substring(0, 200) + '...');

        // اگر محتوا وجود دارد اما دکمه‌ها نیستند، دوباره لود کنیم
        if (contentDiv.innerHTML.includes('large-content') && !contentDiv.innerHTML.includes('toolbar-measure-start')) {
          console.log('محتوا وجود دارد اما دکمه‌ها نیستند - دوباره لود می‌کنیم');
          this.loadMeasureContent(contentDiv);

          // تلاش مجدد بعد از لود
          setTimeout(() => {
            this.setupEventListeners();
          }, 200);
          return;
        }
      } else {
        console.error('contentDiv پیدا نشد');
      }
      return; // حلقه بی‌نهایت را متوقف می‌کنیم
    }

    if (!measureStopBtn) {
      console.error('دکمه توقف اندازه‌گیری پیدا نشد');
      return;
    }

    // تنظیم event listener برای دکمه شروع
    this.setupStartButton(measureStartBtn, measureStopBtn);

    // تنظیم event listener برای دکمه توقف
    this.setupStopButton(measureStopBtn);

    // تنظیم event listener برای تغییر واحدها
    this.setupUnitSelectors(unitLengthSelect, unitAreaSelect);

    console.log('Event listener های جدید تنظیم شدند');
  }

  // تنظیم دکمه شروع - ایجاد measureControl جدید هر بار
  setupStartButton(measureStartBtn, measureStopBtn) {
    measureStartBtn.style.fontSize = "24px";
    measureStartBtn.onclick = () => {
      const map = window.getMap();
      if (!map) {
        console.error("نقشه هنوز آماده نیست، لطفاً صبر کنید");
        return;
      }

      if (!window.isMeasuring) {
        // همیشه یک measureControl جدید ایجاد کنیم - مثل ویجت ترسیم
        this.createFreshMeasureControl(map, measureStopBtn);
      } else {
        // پایان اندازه‌گیری
        this.finishMeasure();
      }
    };
  }

  // ایجاد measureControl جدید
  createFreshMeasureControl(map, measureStopBtn) {
    try {
      // حذف measureControl قدیمی اگر وجود دارد
      if (window.measureControl) {
        try {
          map.removeControl(window.measureControl);
          console.log('measureControl قدیمی حذف شد');
        } catch (e) {
          console.warn('خطا در حذف measureControl قدیمی:', e);
        }
      }

      // تنظیم متغیرهای سراسری اگر تنظیم نشده‌اند
      if (!window.selectedLengthUnit) {
        window.selectedLengthUnit = 'kilometers'; // پیش‌فرض مطابق با select
      }
      if (!window.selectedAreaUnit) {
        window.selectedAreaUnit = 'sqmeters'; // پیش‌فرض مطابق با select
      }

      // ایجاد measureControl جدید با تنظیمات مناسب
      const measureOptions = {
        primaryLengthUnit: window.selectedLengthUnit,
        primaryAreaUnit: window.selectedAreaUnit,
        activeColor: '#ed3833',
        completedColor: '#63aabc',
        popupOptions: {
          className: 'leaflet-measure-resultpopup',
          autoPanPadding: [10, 10]
        }
      };

      console.log('ایجاد measureControl جدید با تنظیمات:', measureOptions);

      // ایجاد instance جدید
      window.measureControl = new window.L.Control.Measure(measureOptions);
      map.addControl(window.measureControl);

      console.log('measureControl جدید به نقشه اضافه شد');

      // تنظیم event handler های اندازه‌گیری برای instance جدید
      this.setupMeasureEventHandlers(map);

      // شروع اندازه‌گیری
      setTimeout(() => {
        this.startMeasureProcess(measureStopBtn);
      }, 200);

    } catch (error) {
      console.error('خطا در ایجاد measureControl جدید:', error);
    }
  }

  // تنظیم event handler های اندازه‌گیری برای نمایش اندازه‌ها در حین ترسیم
  setupMeasureEventHandlers(map) {
    console.log('تنظیم event handler های اندازه‌گیری...');

    // همیشه measureLabels جدید ایجاد کنیم - مثل measureControl
    if (window.measureLabels) {
      try {
        map.removeLayer(window.measureLabels);
        console.log('measureLabels قدیمی حذف شد');
      } catch (e) {
        console.warn('خطا در حذف measureLabels قدیمی:', e);
      }
    }

    window.measureLabels = window.L.layerGroup().addTo(map);
    console.log('measureLabels جدید ایجاد شد');

    // متغیرهای موقت برای نمایش در حین ترسیم
    let tempShape = null;
    let tempLabel = null;

    // event handler برای نمایش خط موقت در حین حرکت ماوس
    const mouseMoveHandler = (e) => {
      // لاگ فقط برای اولین بار
      if (window.isMeasuring && !this.loggedMouseMove) {
        console.log('mousemove handler فعال - measureControl:', !!window.measureControl, 'locked:', window.measureControl?._locked);
        this.loggedMouseMove = true;
      }

      if (window.isMeasuring && window.measureControl && window.measureControl._locked &&
          window.measureControl._latlngs && window.measureControl._latlngs.length > 0) {

        const lastPoint = window.measureControl._latlngs[window.measureControl._latlngs.length - 1];
        const distance = lastPoint.distanceTo(e.latlng) /
          (window.selectedLengthUnit === "kilometers" ? 1000 :
           window.selectedLengthUnit === "meters" ? 1 : 1609.34);

        const midPoint = window.L.latLng(
          (lastPoint.lat + e.latlng.lat) / 2,
          (lastPoint.lng + e.latlng.lng) / 2
        );

        // حذف شکل‌های قبلی
        if (tempShape && window.measureLabels) {
          window.measureLabels.removeLayer(tempShape);
        }
        if (tempLabel && window.measureLabels) {
          window.measureLabels.removeLayer(tempLabel);
        }

        // ایجاد خط موقت
        if (window.measureLabels) {
          tempShape = window.L.polyline([lastPoint, e.latlng], {
            color: "#ed3833",
            weight: 2,
            dashArray: "5, 10",
          }).addTo(window.measureLabels);

          // ایجاد لیبل موقت
          tempLabel = window.L.marker(midPoint, {
            icon: window.L.divIcon({
              className: "measure-label",
              html: `${distance.toFixed(2)} ${
                window.selectedLengthUnit === "kilometers" ? "کیلومتر" :
                window.selectedLengthUnit === "meters" ? "متر" : "مایل"
              }`,
            }),
          }).addTo(window.measureLabels);

          console.log('خط موقت و لیبل اضافه شد:', distance.toFixed(2));
        }
      }
    };

    // event handler برای اضافه کردن لیبل ثابت بعد از کلیک
    const clickHandler = (e) => {
      if (window.isMeasuring && window.measureControl && window.measureControl._locked &&
          window.measureControl._latlngs && window.measureControl._latlngs.length >= 2) {

        const latlngs = window.measureControl._latlngs;
        const lastIndex = latlngs.length - 1;
        const latLng1 = latlngs[lastIndex - 1];
        const latLng2 = latlngs[lastIndex];

        const distance = latLng1.distanceTo(latLng2) /
          (window.selectedLengthUnit === "kilometers" ? 1000 :
           window.selectedLengthUnit === "meters" ? 1 : 1609.34);

        const midPoint = window.L.latLng(
          (latLng1.lat + latLng2.lat) / 2,
          (latLng1.lng + latLng2.lng) / 2
        );

        // حذف شکل‌های موقت
        if (tempShape && window.measureLabels) {
          window.measureLabels.removeLayer(tempShape);
          tempShape = null;
        }
        if (tempLabel && window.measureLabels) {
          window.measureLabels.removeLayer(tempLabel);
          tempLabel = null;
        }

        // اضافه کردن لیبل ثابت
        if (window.measureLabels) {
          window.L.marker(midPoint, {
            icon: window.L.divIcon({
              className: "measure-label",
              html: `${distance.toFixed(2)} ${
                window.selectedLengthUnit === "kilometers" ? "کیلومتر" :
                window.selectedLengthUnit === "meters" ? "متر" : "مایل"
              }`,
            }),
          }).addTo(window.measureLabels);

          console.log('لیبل ثابت اضافه شد:', distance.toFixed(2));
        }
      }
    };

    // حذف event handler های قدیمی اگر وجود دارند
    if (this.currentMouseMoveHandler) {
      map.off('mousemove', this.currentMouseMoveHandler);
    }
    if (this.currentClickHandler) {
      map.off('click', this.currentClickHandler);
    }

    // اضافه کردن event handler های جدید
    map.on('mousemove', mouseMoveHandler);
    map.on('click', clickHandler);

    // ذخیره reference برای حذف بعدی
    this.currentMouseMoveHandler = mouseMoveHandler;
    this.currentClickHandler = clickHandler;

    console.log('Event handler های اندازه‌گیری تنظیم شدند');
  }

  // تابع شروع فرآیند اندازه‌گیری
  startMeasureProcess(measureStopBtn) {
    try {
      // ریست کردن فلگ لاگ
      this.loggedMouseMove = false;

      // پاک کردن measureLabels قبل از شروع جدید
      if (window.measureLabels) {
        window.measureLabels.clearLayers();
      }

      // ابتدا measureControl را expand کنیم
      window.measureControl._expand();

      // سپس دکمه start را شبیه‌سازی کنیم
      const startButton = window.measureControl._container.querySelector('.js-start');
      if (startButton) {
        startButton.click();
        console.log("کلیک روی دکمه start measureControl انجام شد");
      } else {
        console.error("دکمه start پیدا نشد");
        return;
      }

      window.isMeasuring = true;

      console.log('وضعیت measureControl بعد از شروع:', {
        locked: window.measureControl._locked,
        latlngs: window.measureControl._latlngs?.length
      });

      // نمایش دکمه غیرفعال‌سازی
      if (measureStopBtn) measureStopBtn.style.display = "inline-block";

      console.log("اندازه‌گیری شروع شد از تولبار");
    } catch (error) {
      console.error('خطا در startMeasureProcess:', error);
    }
  }

  // تنظیم دکمه توقف
  setupStopButton(measureStopBtn) {
    measureStopBtn.onclick = () => {
      console.log('دکمه غیرفعال‌سازی کلیک شد - وضعیت:', {
        isMeasuring: window.isMeasuring,
        measureControl: !!window.measureControl,
        locked: window.measureControl ? window.measureControl._locked : 'N/A',
        latlngs: window.measureControl ? window.measureControl._latlngs?.length : 'N/A'
      });

      this.finishMeasure();
      measureStopBtn.style.display = "none";
      console.log("اندازه‌گیری کاملاً غیرفعال شد از toolbar");
    };
  }

  // تنظیم انتخابگرهای واحد
  setupUnitSelectors(unitLengthSelect, unitAreaSelect) {
    if (unitLengthSelect) {
      unitLengthSelect.onchange = function () {
        window.selectedLengthUnit = this.value;
        if (window.measureControl) {
          window.measureControl.options.primaryLengthUnit = this.value;
        }
        console.log("واحد طول تغییر کرد به:", this.value);
      };
    }

    if (unitAreaSelect) {
      unitAreaSelect.onchange = function () {
        window.selectedAreaUnit = this.value;
        if (window.measureControl) {
          window.measureControl.options.primaryAreaUnit = this.value;
        }
        console.log("واحد مساحت تغییر کرد به:", this.value);
      };
    }
  }

  // تابع پایان اندازه‌گیری
  finishMeasure() {
    if (window.measureControl && window.measureControl._locked) {
      try {
        const cancelButton = window.measureControl._container.querySelector('.js-cancel');
        if (cancelButton) {
          cancelButton.click();
          console.log("کلیک روی دکمه cancel measureControl انجام شد");
        } else {
          // fallback به روش مستقیم
          window.measureControl._finishMeasure();
        }
      } catch (error) {
        console.error('خطا در پایان اندازه‌گیری:', error);
      }
    }

    // ریست متغیرهای سراسری
    window.isMeasuring = false;
  }

  // تابع ریست کامل اندازه‌گیری - حذف measureControl قدیمی
  resetMeasureControl() {
    try {
      // حذف measureControl قدیمی از نقشه
      const map = window.getMap();
      if (map && window.measureControl) {
        try {
          map.removeControl(window.measureControl);
          console.log('measureControl قدیمی از نقشه حذف شد');
        } catch (e) {
          console.warn('خطا در حذف measureControl از نقشه:', e);
        }
      }

      // پاک کردن reference
      window.measureControl = null;

      // ریست متغیرهای سراسری
      window.isMeasuring = false;

      // پاک کردن نتایج UI
      const resultsDiv = document.getElementById("toolbar-measure-results");
      const sidebarResultsDiv = document.getElementById("measure-results");
      if (resultsDiv) resultsDiv.innerHTML = "";
      if (sidebarResultsDiv) sidebarResultsDiv.innerHTML = "";

      // مخفی کردن دکمه stop
      const measureStopBtn = document.getElementById('toolbar-measure-stop');
      if (measureStopBtn) measureStopBtn.style.display = "none";

      console.log('اندازه‌گیری کاملاً ریست شد');
    } catch (error) {
      console.error('خطا در ریست اندازه‌گیری:', error);
    }
  }

  // تنظیم رویدادهای اندازه‌گیری - بدون وابستگی به measureControl موجود
  setupMeasureEvents() {
    console.log('شروع تنظیم رویدادهای اندازه‌گیری');

    // تأخیر بیشتر برای اطمینان از لود شدن DOM
    setTimeout(() => {
      console.log('تلاش برای تنظیم event listener ها...');
      this.setupEventListeners();
    }, 300);
  }

  // تنظیم رویدادهای ترسیم (مشابه ui.js)
  setupDrawEvents() {
    // غیرفعال کردن ابزار قبلی
    if (window.activeDrawTool) {
      window.activeDrawTool.disable();
      window.activeDrawTool = null;
    }

    const saveButton = document.getElementById('toolbar-save-edit');

    // تنظیم گزینه‌های ترسیم (کپی مستقیم از ui.js)
    const drawOptions = {
      draw: {
        polyline: {
          shapeOptions: {
            color: "red",
            weight: 4,
            opacity: 1,
          },
        },
        polygon: {
          allowIntersection: false,
          drawError: {
            color: "purple",
            message: "<strong>خطا!</strong> نمی‌تونید اینو بکشید!",
          },
          shapeOptions: {
            color: "orange",
            weight: 4,
            opacity: 1,
            fillColor: "#32CD32",
            fillOpacity: 0.5,
          },
        },
        circle: {
          shapeOptions: {
            color: "steelblue",
            weight: 4,
            opacity: 1,
            fillColor: "#8A2BE2",
            fillOpacity: 0.5,
          },
        },
        rectangle: {
          shapeOptions: {
            color: "green",
            weight: 4,
            opacity: 1,
            fillColor: "#FF4500",
            fillOpacity: 0.5,
          },
        },
        marker: {
          icon: new window.L.Icon.Default(),
        },
      },
    };

    // کپی مستقیم رویدادها از ui.js
    const polylineBtn = document.getElementById('toolbar-draw-polyline');
    if (polylineBtn) {
      polylineBtn.onclick = function () {
        const map = window.getMap();
        if (!map) {
          console.error("نقشه هنوز آماده نیست، لطفاً صبر کنید");
          return;
        }
        if (window.activeDrawTool) window.activeDrawTool.disable();
        window.activeDrawTool = new window.L.Draw.Polyline(map, drawOptions.draw.polyline);
        window.activeDrawTool.enable();
        if (saveButton) saveButton.style.display = "none";
      };
    }

    const polygonBtn = document.getElementById('toolbar-draw-polygon');
    if (polygonBtn) {
      polygonBtn.onclick = function () {
        const map = window.getMap();
        if (!map) {
          console.error("نقشه هنوز آماده نیست، لطفاً صبر کنید");
          return;
        }
        if (window.activeDrawTool) window.activeDrawTool.disable();
        window.activeDrawTool = new window.L.Draw.Polygon(map, drawOptions.draw.polygon);
        window.activeDrawTool.enable();
        if (saveButton) saveButton.style.display = "none";
      };
    }

    const circleBtn = document.getElementById('toolbar-draw-circle');
    if (circleBtn) {
      circleBtn.onclick = function () {
        const map = window.getMap();
        if (!map) {
          console.error("نقشه هنوز آماده نیست، لطفاً صبر کنید");
          return;
        }
        if (window.activeDrawTool) window.activeDrawTool.disable();
        window.activeDrawTool = new window.L.Draw.Circle(map, drawOptions.draw.circle);
        window.activeDrawTool.enable();
        if (saveButton) saveButton.style.display = "none";
      };
    }

    const rectangleBtn = document.getElementById('toolbar-draw-rectangle');
    if (rectangleBtn) {
      rectangleBtn.onclick = function () {
        const map = window.getMap();
        if (!map) {
          console.error("نقشه هنوز آماده نیست، لطفاً صبر کنید");
          return;
        }
        if (window.activeDrawTool) window.activeDrawTool.disable();
        window.activeDrawTool = new window.L.Draw.Rectangle(map, drawOptions.draw.rectangle);
        window.activeDrawTool.enable();
        if (saveButton) saveButton.style.display = "none";
      };
    }

    const markerBtn = document.getElementById('toolbar-draw-marker');
    if (markerBtn) {
      markerBtn.onclick = function () {
        const map = window.getMap();
        if (!map) {
          console.error("نقشه هنوز آماده نیست، لطفاً صبر کنید");
          return;
        }
        if (window.activeDrawTool) window.activeDrawTool.disable();
        window.activeDrawTool = new window.L.Draw.Marker(map, drawOptions.draw.marker);
        window.activeDrawTool.enable();
        if (saveButton) saveButton.style.display = "none";
      };
    }

    // رویدادهای ویرایش و حذف
    const editBtn = document.getElementById('toolbar-edit-layers');
    if (editBtn) {
      editBtn.onclick = function () {
        if (window.L && window.L.EditToolbar && window.editableLayers) {
          window.activeDrawTool = new window.L.EditToolbar.Edit(window.getMap(), {
            featureGroup: window.editableLayers
          });
          window.activeDrawTool.enable();
          if (saveButton) saveButton.style.display = "inline-block";
          console.log('ابزار ویرایش فعال شد');
        }
      };
    }

    const removeBtn = document.getElementById('toolbar-remove-layers');
    if (removeBtn) {
      removeBtn.onclick = function () {
        if (window.L && window.L.EditToolbar && window.editableLayers) {
          window.activeDrawTool = new window.L.EditToolbar.Delete(window.getMap(), {
            featureGroup: window.editableLayers
          });
          window.activeDrawTool.enable();
          if (saveButton) saveButton.style.display = "none";
          console.log('ابزار حذف فعال شد');
        }
      };
    }

    if (saveButton) {
      saveButton.onclick = function () {
        if (window.activeDrawTool) {
          window.activeDrawTool.save();
          window.activeDrawTool.disable();
          window.activeDrawTool = null;
          saveButton.style.display = "none";

          // مخفی کردن دکمه غیرفعال‌سازی
          const stopBtn = document.getElementById('toolbar-draw-stop');
          if (stopBtn) stopBtn.style.display = "none";

          console.log('تغییرات ذخیره شد و ابزار غیرفعال شد');
        }
      };
    }

    // دکمه غیرفعال‌سازی ترسیم
    const drawStopBtn = document.getElementById('toolbar-draw-stop');
    if (drawStopBtn) {
      drawStopBtn.onclick = function () {
        if (window.activeDrawTool) {
          window.activeDrawTool.disable();
          window.activeDrawTool = null;
          drawStopBtn.style.display = "none";

          // مخفی کردن دکمه ذخیره
          if (saveButton) saveButton.style.display = "none";

          console.log('ابزار ترسیم غیرفعال شد');
        }
      };
    }

    // نمایش دکمه غیرفعال‌سازی هنگام فعال‌سازی ابزارهای ترسیم
    const showStopButton = () => {
      if (drawStopBtn) drawStopBtn.style.display = "inline-block";
    };

    // اضافه کردن نمایش دکمه غیرفعال‌سازی به همه ابزارهای ترسیم
    [polylineBtn, polygonBtn, circleBtn, rectangleBtn, markerBtn].forEach(btn => {
      if (btn && btn.onclick) {
        const originalOnclick = btn.onclick;
        btn.onclick = function() {
          originalOnclick.call(this);
          showStopButton();
        };
      }
    });
  }

  // تنظیم رویدادهای خط‌کش (کپی مستقیم از ui.js)
  setupRulerEvents() {
    const rulerStartBtn = document.getElementById('toolbar-ruler-start');
    const rulerStopBtn = document.getElementById('toolbar-ruler-stop');

    if (rulerStartBtn) {
      rulerStartBtn.onclick = function () {
        // بررسی وجود rulerControl
        if (!window.rulerControl) {
          console.error('rulerControl در دسترس نیست');
          return;
        }

        if (!window.isRulerActive) {
          try {
            window.rulerControl._toggleMeasure();
            window.isRulerActive = true;

            // نمایش دکمه غیرفعال‌سازی
            if (rulerStopBtn) rulerStopBtn.style.display = "inline-block";

            console.log("خط‌کش شروع شد از تولبار");

            // غیرفعال کردن سایر ابزارها
            if (window.activeDrawTool) {
              window.activeDrawTool.disable();
              window.activeDrawTool = null;
            }
            if (window.isMeasuring && window.measureControl) {
              window.measureControl._finishMeasure();
              window.isMeasuring = false;
            }
            if (window.disableCoordinatePicking) {
              window.disableCoordinatePicking();
            }
          } catch (error) {
            console.error('خطا در شروع خط‌کش:', error);
          }
        } else {
          try {
            window.rulerControl._toggleMeasure();
            window.isRulerActive = false;

            // مخفی کردن دکمه غیرفعال‌سازی
            if (rulerStopBtn) rulerStopBtn.style.display = "none";

            console.log("خط‌کش متوقف شد از تولبار");
          } catch (error) {
            console.error('خطا در پایان خط‌کش:', error);
          }
        }
      };
    }

    // دکمه غیرفعال‌سازی خط‌کش
    if (rulerStopBtn) {
      rulerStopBtn.onclick = function () {
        if (window.isRulerActive && window.rulerControl) {
          window.rulerControl._toggleMeasure();
          window.isRulerActive = false;
          rulerStopBtn.style.display = "none";

          console.log('خط‌کش غیرفعال شد');
        }
      };
    }
  }

  // تنظیم رویدادهای خروجی
  setupExportEvents() {
    const printButtons = [
      { id: 'toolbar-print-landscape', name: 'لندسکیپ' },
      { id: 'toolbar-print-portrait', name: 'پورتریت' },
      { id: 'toolbar-print-auto', name: 'خودکار' },
      { id: 'toolbar-print-a4', name: 'A4' },
      { id: 'toolbar-print-a3', name: 'A3' }
    ];

    printButtons.forEach(buttonInfo => {
      const button = document.getElementById(buttonInfo.id);
      if (button) {
        button.addEventListener('click', function() {
          if (!window.printControl || !window.printControl.browserPrint) {
            console.error('کنترل پرینت در دسترس نیست');
            return;
          }

          if (!window.L || !window.L.BrowserPrint) {
            console.error('L.BrowserPrint در دسترس نیست');
            return;
          }

          let mode;
          switch (buttonInfo.id) {
            case 'toolbar-print-landscape':
              mode = window.L.BrowserPrint.Mode.Landscape("A4", {
                title: "چاپ افقی",
                header: {
                  enabled: true,
                  text: "عنوان هدر فارسی",
                  size: "10mm",
                  overTheMap: false,
                },
                footer: {
                  enabled: true,
                  text: "عنوان فوتر فارسی",
                  size: "10mm",
                  overTheMap: false,
                },
              });
              break;
            case 'toolbar-print-portrait':
              mode = window.L.BrowserPrint.Mode.Portrait("A4", { title: "چاپ عمودی" });
              break;
            case 'toolbar-print-auto':
              mode = window.L.BrowserPrint.Mode.Auto("A4", { title: "چاپ خودکار" });
              break;
            case 'toolbar-print-a4':
              mode = window.L.BrowserPrint.Mode.Auto("A4", { title: "چاپ A4" });
              break;
            case 'toolbar-print-a3':
              mode = window.L.BrowserPrint.Mode.Auto("A3", { title: "چاپ A3" });
              break;
          }

          if (mode) {
            window.printControl.browserPrint.print(mode);
            console.log(`پرینت ${buttonInfo.name} انتخاب شد`);
          }
        });
      }
    });
  }

  // تنظیم رویدادهای پنل کاربر
  setupUserPanelEvents() {
    // رویدادهای مربوط به پنل کاربر
    const changePasswordBtn = document.getElementById('toolbar-change-password-btn');
    const logoutBtn = document.getElementById('toolbar-logout-btn-sidebar');

    if (changePasswordBtn) {
      changePasswordBtn.addEventListener('click', function() {
        // فعلاً هیچ کاری نمی‌کند
        console.log('دکمه تغییر رمز کلیک شد - فعلاً غیرفعال');
      });
    }

    if (logoutBtn) {
      logoutBtn.addEventListener('click', function() {
        // مستقیم خروج بدون تأیید
        localStorage.removeItem('loggedIn');
        localStorage.removeItem('username');
        window.location.href = '/index.html';
      });
    }
  }
}

// ایجاد نمونه سراسری
const widgetToolbar = new WidgetToolbar();

// تابع کمکی برای بررسی آمادگی سیستم
window.checkSystemReadiness = function() {
  const status = {
    getLayersConfig: !!window.getLayersConfig,
    getMap: !!window.getMap,
    L: !!window.L,
    measureControl: !!window.measureControl,
    rulerControl: !!window.rulerControl,
    printControl: !!window.printControl,
    coordinateMarkers: !!window.coordinateMarkers,
    bookmarkControl: !!window.bookmarkControl,
    toggleLayer: !!window.toggleLayer,
    loadLayers: !!window.loadLayers,
    switchView: !!window.switchView,
    generateLayerCheckboxes: !!window.generateLayerCheckboxes
  };

  console.table(status);

  const readyCount = Object.values(status).filter(Boolean).length;
  const totalCount = Object.keys(status).length;

  console.log(`آمادگی سیستم: ${readyCount}/${totalCount} (${Math.round(readyCount/totalCount*100)}%)`);

  // تست تابع getLayersConfig
  if (window.getLayersConfig) {
    try {
      const layers = window.getLayersConfig();
      console.log('تعداد لایه‌ها:', layers ? layers.length : 'null');
    } catch (error) {
      console.error('خطا در getLayersConfig:', error);
    }
  }

  // تست تابع getMap
  if (window.getMap) {
    try {
      const map = window.getMap();
      console.log('نقشه:', map ? 'موجود' : 'null');
    } catch (error) {
      console.error('خطا در getMap:', error);
    }
  }

  return status;
};

// تابع کمکی برای بارگیری مجدد تولبار
window.reloadToolbarContent = function() {
  console.log('بارگیری مجدد محتوای تولبار...');
  if (window.widgetToolbar) {
    window.widgetToolbar.refreshWidgetContents();
  } else {
    console.error('widgetToolbar در دسترس نیست');
  }
};

// تابع کمکی برای تست ویجت خاص
window.testWidget = function(widgetId) {
  console.log(`تست ویجت: ${widgetId}`);
  if (window.widgetToolbar) {
    const widget = window.widgetToolbar.widgets.find(w => w.id === widgetId);
    if (widget) {
      window.widgetToolbar.openWidget(widget);
    } else {
      console.error(`ویجت ${widgetId} یافت نشد`);
    }
  } else {
    console.error('widgetToolbar در دسترس نیست');
  }
};

// تابع راه‌اندازی
function initializeWidgetToolbar() {
  try {
    // اضافه کردن getMap به window اگر موجود نیست
    if (!window.getMap && typeof getMap === 'function') {
      window.getMap = getMap;
      console.log('getMap به window اضافه شد');
    }

    // اضافه کردن سایر توابع ضروری
    if (!window.getLayersConfig && typeof config_getLayersConfig === 'function') {
      window.getLayersConfig = config_getLayersConfig;
      console.log('getLayersConfig به window اضافه شد');
    }

    widgetToolbar.createToolbar();
    console.log('نوار تولبار ویجت‌ها با موفقیت راه‌اندازی شد');

    // اضافه کردن به window برای دسترسی سراسری
    window.widgetToolbar = widgetToolbar;

    // هماهنگ‌سازی با سایدبار موجود
    setTimeout(() => {
      if (window.syncHeaderWithSidebar) {
        window.syncHeaderWithSidebar();
      }

      // هماهنگ‌سازی اولیه تصاویر ماهواره
      widgetToolbar.syncCurrentBasemapSelection();

      // تنظیم پیش‌فرض اوپن استریت
      setTimeout(() => {
        widgetToolbar.setBasemapSelection('osm');
      }, 200);
    }, 1000);

    // تلاش برای بارگیری مجدد محتوای ویجت‌ها بعد از آماده شدن وابستگی‌ها
    setTimeout(() => {
      widgetToolbar.refreshWidgetContents();
    }, 2000);

  } catch (error) {
    console.error('خطا در راه‌اندازی نوار تولبار ویجت‌ها:', error);
  }
}

// اضافه کردن تابع refresh به کلاس
WidgetToolbar.prototype.refreshWidgetContents = function() {
  console.log('تلاش برای بارگیری مجدد محتوای ویجت‌ها...');

  // بارگیری مجدد چک‌باکس‌های لایه‌ها
  const layerCheckboxContainer = document.getElementById('toolbar-layer-checkboxes');
  if (layerCheckboxContainer && window.getLayersConfig) {
    try {
      const layersHTML = this.getLayerCheckboxesHTML();
      layerCheckboxContainer.innerHTML = layersHTML;
      console.log('چک‌باکس‌های لایه با موفقیت بارگیری شدند');
    } catch (error) {
      console.error('خطا در بارگیری مجدد چک‌باکس‌های لایه:', error);
    }
  }
};

// تابع هماهنگ‌سازی وضعیت لایه‌ها
WidgetToolbar.prototype.refreshLayerStates = function() {
  console.log('هماهنگ‌سازی وضعیت لایه‌های toolbar...');

  if (!window.getLayersConfig || !window.getMap) {
    console.warn('توابع مورد نیاز برای هماهنگ‌سازی موجود نیستند');
    return;
  }

  const layersConfig = window.getLayersConfig();
  const map = window.getMap();

  if (!layersConfig || !map) {
    console.warn('کانفیگ لایه‌ها یا نقشه موجود نیست');
    return;
  }

  layersConfig.forEach((layer, index) => {
    const toolbarCheckbox = document.getElementById(`toolbar-layer-${index}`);
    if (toolbarCheckbox) {
      // بررسی وضعیت واقعی لایه در نقشه
      const layerObj = window.getFeatureLayers ? window.getFeatureLayers()[layer.name] : null;
      const isLayerOnMap = layerObj && map.hasLayer(layerObj);

      // به‌روزرسانی چک‌باکس toolbar
      if (toolbarCheckbox.checked !== isLayerOnMap) {
        toolbarCheckbox.checked = isLayerOnMap;
        console.log(`چک‌باکس toolbar برای ${layer.name} به ${isLayerOnMap} تغییر کرد`);
      }
    }
  });
}

// تابع هماهنگ‌سازی با وضعیت فعلی لایه‌ها (برای زمان باز شدن toolbar)
WidgetToolbar.prototype.syncWithCurrentLayerStates = function() {
  console.log('🔄 شروع هماهنگ‌سازی toolbar با وضعیت فعلی لایه‌ها...');

  // بررسی دقیق‌تر توابع
  console.log('🔍 بررسی توابع موجود:');
  console.log('- window.getLayersConfig:', typeof window.getLayersConfig);
  console.log('- window.getMap:', typeof window.getMap);
  console.log('- window.getFeatureLayers:', typeof window.getFeatureLayers);

  if (!window.getLayersConfig || !window.getMap || !window.getFeatureLayers) {
    console.warn('❌ توابع مورد نیاز برای هماهنگ‌سازی موجود نیستند');

    // تلاش با توابع global مستقیم
    if (typeof config_getLayersConfig === 'function' && typeof getMap === 'function' && typeof getFeatureLayers === 'function') {
      console.log('✅ استفاده از توابع global مستقیم');
      this.syncWithGlobalFunctions();
    }
    return;
  }

  const layersConfig = window.getLayersConfig();
  const map = window.getMap();
  const featureLayers = window.getFeatureLayers();

  if (!layersConfig || !map || !featureLayers) {
    console.warn('❌ کانفیگ لایه‌ها، نقشه یا لایه‌ها موجود نیست');
    return;
  }

  console.log(`📋 تعداد لایه‌ها: ${layersConfig.length}`);

  layersConfig.forEach((layer, index) => {
    const toolbarCheckbox = document.getElementById(`toolbar-layer-${index}`);
    console.log(`🔍 بررسی لایه ${layer.name} (index: ${index})`);
    console.log(`📦 چک‌باکس toolbar پیدا شد:`, !!toolbarCheckbox);

    if (toolbarCheckbox) {
      // بررسی وضعیت واقعی لایه در نقشه
      const layerObj = featureLayers[layer.name];
      const isLayerOnMap = layerObj && map.hasLayer(layerObj);

      console.log(`🗺️ لایه ${layer.name} در نقشه: ${isLayerOnMap}`);
      console.log(`☑️ چک‌باکس فعلی: ${toolbarCheckbox.checked}`);

      // به‌روزرسانی چک‌باکس toolbar بدون trigger کردن event
      if (toolbarCheckbox.checked !== isLayerOnMap) {
        toolbarCheckbox.checked = isLayerOnMap;
        console.log(`✅ چک‌باکس toolbar برای ${layer.name} هماهنگ شد: ${isLayerOnMap}`);
      } else {
        console.log(`✓ چک‌باکس toolbar برای ${layer.name} قبلاً هماهنگ بود`);
      }
    } else {
      console.log(`❌ چک‌باکس toolbar برای ${layer.name} پیدا نشد`);
    }
  });

  // به‌روزرسانی دکمه toggle all
  this.updateToggleAllButtonState();
  console.log('🏁 هماهنگ‌سازی toolbar تمام شد');
}

// تابع هماهنگ‌سازی با استفاده از توابع global مستقیم
WidgetToolbar.prototype.syncWithGlobalFunctions = function() {
  console.log('🔄 هماهنگ‌سازی با توابع global مستقیم...');

  try {
    const layersConfig = config_getLayersConfig();
    const map = getMap();
    const featureLayers = getFeatureLayers();

    if (!layersConfig || !map || !featureLayers) {
      console.warn('❌ کانفیگ لایه‌ها، نقشه یا لایه‌ها موجود نیست');
      return;
    }

    console.log(`📋 تعداد لایه‌ها: ${layersConfig.length}`);

    layersConfig.forEach((layer, index) => {
      const toolbarCheckbox = document.getElementById(`toolbar-layer-${index}`);
      console.log(`🔍 بررسی لایه ${layer.name} (index: ${index})`);
      console.log(`📦 چک‌باکس toolbar پیدا شد:`, !!toolbarCheckbox);

      if (toolbarCheckbox) {
        // بررسی وضعیت واقعی لایه در نقشه
        const layerObj = featureLayers[layer.name];
        const isLayerOnMap = layerObj && map.hasLayer(layerObj);

        console.log(`🗺️ لایه ${layer.name} در نقشه: ${isLayerOnMap}`);
        console.log(`☑️ چک‌باکس فعلی: ${toolbarCheckbox.checked}`);

        // به‌روزرسانی چک‌باکس toolbar بدون trigger کردن event
        if (toolbarCheckbox.checked !== isLayerOnMap) {
          toolbarCheckbox.checked = isLayerOnMap;
          console.log(`✅ چک‌باکس toolbar برای ${layer.name} هماهنگ شد: ${isLayerOnMap}`);
        } else {
          console.log(`✓ چک‌باکس toolbar برای ${layer.name} قبلاً هماهنگ بود`);
        }
      } else {
        console.log(`❌ چک‌باکس toolbar برای ${layer.name} پیدا نشد`);
      }
    });

    // به‌روزرسانی دکمه toggle all
    this.updateToggleAllButtonState();
    console.log('🏁 هماهنگ‌سازی با توابع global تمام شد');

  } catch (error) {
    console.error('❌ خطا در هماهنگ‌سازی با توابع global:', error);
  }
}

// تابع به‌روزرسانی وضعیت دکمه toggle all
WidgetToolbar.prototype.updateToggleAllButtonState = function() {
  const toggleAllBtn = document.getElementById('toolbar-toggle-all-layers');
  if (!toggleAllBtn || !window.getLayersConfig) return;

  const layersConfig = window.getLayersConfig();
  let allChecked = true;
  let anyChecked = false;

  // بررسی وضعیت همه چک‌باکس‌ها
  layersConfig.forEach((layer, index) => {
    const checkbox = document.getElementById(`toolbar-layer-${index}`);
    if (checkbox) {
      if (checkbox.checked) {
        anyChecked = true;
      } else {
        allChecked = false;
      }
    }
  });

  // تنظیم وضعیت دکمه
  const icon = toggleAllBtn.querySelector('i');
  const span = toggleAllBtn.querySelector('span');

  if (allChecked && anyChecked) {
    // همه روشن هستند
    toggleAllBtn.setAttribute('data-state', 'on');
    if (icon) icon.className = 'fas fa-toggle-on';
    if (span) span.textContent = 'غیرفعال کردن همه';
  } else {
    // همه خاموش هستند یا بعضی روشن هستند
    toggleAllBtn.setAttribute('data-state', 'off');
    if (icon) icon.className = 'fas fa-toggle-off';
    if (span) span.textContent = 'فعال کردن همه';
  }
}



;// ./src/js/app.js




// import { buildFilters, applyFilter } from "@js/filters.js";
// import { drawCharts } from "@js/charts.js";




console.log("app.js لود شد و اجرا شد!");

// سرکوب کردن هشدارهای alasql برای بهبود تجربه کاربری
const originalError = console.error;
console.error = function(...args) {
    if (args[0] && typeof args[0] === 'string' && args[0].includes('unreachable code after return statement')) {
        return; // نادیده گرفتن این خطای خاص alasql
    }
    originalError.apply(console, args);
};

// سرکوب کردن هشدارهای touchleave برای بهبود تجربه کاربری
const app_originalWarn = console.warn;
console.warn = function(...args) {
    if (args[0] && typeof args[0] === 'string' && 
        (args[0].includes('wrong event specified: touchleave') || 
         args[0].includes('Deprecated include of L.Mixin.Events'))) {
        return; // نادیده گرفتن این هشدارهای خاص
    }
    app_originalWarn.apply(console, args);
};

// اجرای مستقیم برنامه بدون بررسی احراز هویت
initializeApp();

// تابع اصلی راه‌اندازی برنامه
function initializeApp() {
    console.log("برنامه در حال اجرا...");

    // نمایش لودر
    const loadingMask = document.createElement('div');
    loadingMask.innerHTML = `
        <div id="loading-mask" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(255, 255, 255, 0.9); z-index: 9999; display: flex; justify-content: center; align-items: center; flex-direction: column;">
            <img src="./images/LoftLoader-Pro.gif" alt="Loading..." style="width: 100px; height: 100px; margin-bottom: 20px;">
            <div style="font-family:Nahid,Tahoma, sans-serif,Arial ; font-size: 16px; color: #333;">لطفا صبر کنید، در حال بارگذاری برنامه...</div>
        </div>
    `;
    document.body.appendChild(loadingMask);

    // لود jQuery و Leaflet و سایر وابستگی‌ها
    loadJQuery()
        .then(() => Promise.all([loadStyles(), loadScripts()]))
        .then(() => loadLeaflet())
        .then(() => loadInitialTemplates())
        .then(() => $.getJSON("./config/config.json"))
        .then((data) => setLayersConfig(data.layers))
        .then(() => setupMap())
        .then(() => setupUI())
        .then(() => {
            fitToBounds();
            switchView("map");

            // راه‌اندازی نوار تولبار ویجت‌ها
            initializeWidgetToolbar();

            // نمایش نام کاربر
            const username = localStorage.getItem("username") || "مهمان";
            $("#username").text(`کاربر: ${username}`);

            // رویداد خروج
            $("#logout-btn").on("click", () => {
                localStorage.removeItem("username");
                window.location.href = "/index.html";
            });

            // مخفی کردن لودینگ
            setTimeout(() => {
                $("#loading-mask").fadeOut(500, () => $("#loading-mask").remove());
            }, 2000);
        })
        .catch((error) => {
            console.error("خطا در فرآیند راه‌اندازی:", error);
            $("#loading-mask").remove();
        });
}

// لود jQuery
function loadJQuery() {
    return new Promise((resolve, reject) => {
        if (window.$) return resolve();
        const script = document.createElement("script");
        script.src = "./assets/jquery@2.2.4/jquery/dist/jquery.min.js";
        script.onload = resolve;
        script.onerror = () => reject(new Error("خطا در لود jQuery"));
        document.head.appendChild(script);
    });
}

// لود Leaflet
function loadLeaflet() {
    return new Promise((resolve, reject) => {
        if (window.L) return resolve();
        const script = document.createElement("script");
        script.src = "./assets/leaflet@1.9.4/dist/leaflet.js";
        script.onload = resolve;
        script.onerror = () => reject(new Error("خطا در لود Leaflet"));
        document.head.appendChild(script);
    });
}

// لود تمپلیت‌های اولیه
async function loadInitialTemplates() {
    return Promise.all([
        loadTemplate("navbar", null).then((data) => $("body").prepend(data)),
        loadTemplate("sidebar", null).then((data) => $("body").append(data)),
        loadTemplate("searchContainer", null).then((data) => $("body").append(data)),
        loadTemplate("tableContainer", null).then((data) => $("body").append(data)),
        loadTemplate("images", null).then((data) => $("body").append(data)),
        loadTemplate("aboutModal", null).then((data) => $("body").append(data)),
        loadTemplate("aboutModal1", null).then((data) => $("body").append(data)),
        loadTemplate("chartModal", null).then((data) => $("body").append(data)),
        loadTemplate("filterModal", null).then((data) => $("body").append(data)),
        loadTemplate("featureModal", null).then((data) => $("body").append(data)),
        loadTemplate("imageGalleryModal", null).then((data) => $("body").append(data)),
    ]);
}

/******/ })()
;