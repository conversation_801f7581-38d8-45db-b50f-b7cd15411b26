{"name": "leaflet-minimap", "version": "3.6.1", "author": "Norkart AS", "description": "A plugin for Leaflet that provides a minimap in the corner of the map view.", "keywords": ["maps", "leaflet", "client", "minimap"], "license": "BSD-2-<PERSON><PERSON>", "readmeFilename": "README.md", "repository": {"type": "git", "url": "https://github.com/Norkart/Leaflet-MiniMap.git"}, "main": "dist/Control.MiniMap.min.js", "homepage": "https://github.com/Norkart/Leaflet-MiniMap", "bugs": {"url": "https://github.com/Norkart/Leaflet-MiniMap/issues"}, "devDependencies": {"clean-css": "3.0.7", "happiness": "^1.0.7", "leaflet": ">=1.0.3", "ncp": "1.0.1", "svg2png": "1.1.0", "svgo": "0.5.0", "uglify-js": "2.6.0"}, "scripts": {"build:js": "uglifyjs  --output dist/Control.MiniMap.min.js src/Control.MiniMap.js", "build:css": "cleancss  --skip-rebase --output dist/Control.MiniMap.min.css src/Control.MiniMap.css", "build:img": "svgo src/images/toggle.svg dist/images/toggle.svg && node buildscripts/svgtopng.js", "build": "npm run build:css && npm run build:js && npm run build:img", "test": "happiness src/**/*.js", "preversion": "npm test && npm run build"}, "happiness": {"globals": ["define", "L"]}}