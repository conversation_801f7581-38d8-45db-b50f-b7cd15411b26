<!DOCTYPE html>
<html>
<head>
    <title>Leaflet.draw vector editing handlers</title>

    <script src="libs/leaflet-src.js"></script>
    <link rel="stylesheet" href="libs/leaflet.css"/>

    <script src="../../src/Leaflet.draw.js"></script>
    <script src="../../src/Leaflet.Draw.Event.js"></script>
    <link rel="stylesheet" href="../../src/leaflet.draw.css"/>

    <script src="../../src/Toolbar.js"></script>
    <script src="../../src/Tooltip.js"></script>

    <script src="../../src/ext/GeometryUtil.js"></script>
    <script src="../../src/ext/LatLngUtil.js"></script>
    <script src="../../src/ext/LineUtil.Intersect.js"></script>
    <script src="../../src/ext/Polygon.Intersect.js"></script>
    <script src="../../src/ext/Polyline.Intersect.js"></script>
    <script src="../../src/ext/TouchEvents.js"></script>

    <script src="../../src/draw/DrawToolbar.js"></script>
    <script src="../../src/draw/handler/Draw.Feature.js"></script>
    <script src="../../src/draw/handler/Draw.SimpleShape.js"></script>
    <script src="../../src/draw/handler/Draw.Polyline.js"></script>
    <script src="../../src/draw/handler/Draw.Marker.js"></script>
    <script src="../../src/draw/handler/Draw.Circle.js"></script>
    <script src="../../src/draw/handler/Draw.CircleMarker.js"></script>
    <script src="../../src/draw/handler/Draw.Polygon.js"></script>
    <script src="../../src/draw/handler/Draw.Rectangle.js"></script>


    <script src="../../src/edit/EditToolbar.js"></script>
    <script src="../../src/edit/handler/EditToolbar.Edit.js"></script>
    <script src="../../src/edit/handler/EditToolbar.Delete.js"></script>

    <script src="../../src/Control.Draw.js"></script>

    <script src="../../src/edit/handler/Edit.Poly.js"></script>
    <script src="../../src/edit/handler/Edit.SimpleShape.js"></script>
    <script src="../../src/edit/handler/Edit.Rectangle.js"></script>
    <script src="../../src/edit/handler/Edit.Marker.js"></script>
    <script src="../../src/edit/handler/Edit.CircleMarker.js"></script>
    <script src="../../src/edit/handler/Edit.Circle.js"></script>
</head>
<body>
<div id="map" style="width: 800px; height: 600px; border: 1px solid #ccc"></div>

<script>
    var osmUrl = 'http://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
            osmAttrib = '&copy; <a href="http://openstreetmap.org/copyright">OpenStreetMap</a> contributors',
            osm = L.tileLayer(osmUrl, { maxZoom: 18, attribution: osmAttrib }),
            map = new L.Map('map', { center: new L.LatLng(51.505, -0.04), zoom: 13 }),
            drawnItems = L.featureGroup().addTo(map);
    L.control.layers({
        'osm': osm.addTo(map),
        "google": L.tileLayer('http://www.google.cn/maps/vt?lyrs=s@189&gl=cn&x={x}&y={y}&z={z}', {
            attribution: 'google'
        })
    }, { 'drawlayer': drawnItems }, { position: 'topleft', collapsed: false }).addTo(map);
    map.addControl(new L.Control.Draw({
        edit: {
            featureGroup: drawnItems,
            poly: {
                allowIntersection: false
            }
        },
        draw: {
            polygon: {
                allowIntersection: false,
                showArea: true
            }
        }
    }));

    map.on(L.Draw.Event.CREATED, function (event) {
        var layer = event.layer;

        drawnItems.addLayer(layer);
    });

</script>
</body>
</html>
