<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<title>Leaflet.Control.Search</title>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
	<link rel="shortcut icon" type="image/png" href="images/favicon.png">
	<link rel="stylesheet" href="examples/style.css" />
</head>

<body id="home">

<h2>Leaflet.Control.Search</h2>

<div id="desc">
	A Leaflet Control for search markers/features location by attribute<br />
	and much more.
	<div style="position:absolute;top:0;right:-120px">
		<iframe src="http://ghbtns.com/github-btn.html?user=stefan<PERSON>udini&amp;repo=leaflet-search&amp;type=watch&amp;count=true" allowtransparency="true" frameborder="0" scrolling="0" width="104px" height="20px"></iframe>
	</div>
	<br />
	Other useful stuff for <a href="https://opengeo.tech/maps/">Web Mapping...</a>
</div>

<div style="clear:both">
If any of these open source solutions help your work and saved you time consider sending a donation
<form style="display: inline-block; vertical-align: middle;" action="https://www.paypal.com/donate" method="post" target="_top">
	<input type="hidden" name="business" value="RK9Z26RBUTG4U">
	<input type="hidden" name="no_recurring" value="0">
	<input type="hidden" name="item_name" value="Your donation helps me keep alive the open source projects that I share for you">
	<input type="hidden" name="currency_code" value="EUR">
	<input type="image" src="https://www.paypalobjects.com/en_US/i/btn/btn_donate_SM.gif" border="0" name="submit" title="PayPal - The safer, easier way to pay online!" alt="Donate with PayPal button">
	<img alt="" border="0" src="https://www.paypal.com/en_IT/i/scr/pixel.gif" width="1" height="1" q9ndu8q2m="">
</form>
</div>


<div class="contents">
	<h4>Features</h4>
	<ul id="ff">
		<li>Autocomplete</li>
		<li>No require external Ajax libs</li>		
		<li>Retrieve data locations by Ajax/Jsonp</li>
		<li>Pre-filtering data from Ajax/Jsonp</li>
		<li>Complete fields remapping for remote Jsonp service</li>
		<li>Data source callback support</li>
		<li>Localization placeholder and text alert</li>
		<li>Autozoom on location founded</li>
		<li>Autoresize textbox</li>
		<li>Customize tooltip menu</li>		
		<li>Many options to customize the behavior</li>
		<li>Support search in features collection</li>
		<li>Render Search Box Outside the Leaflet Map</li>
		<li>AMD and CommonJS compatible</li>
	</ul>
</div>
<div class="contents">
	<h4>Examples</h4>
	<ul id="examples">
		<li><a href="examples/simple.html">Simple</a></li>
		<li><a href="examples/multiple-layers.html">Multiple Layers</a></li>
		<li><a href="examples/outside.html">Outside the Map</a></li>
		<li><a href="examples/geojson-layer.html">GeoJSON features</a></li>
		<li><a href="examples/ajax.html">Ajax with Custom Icon</a></li>
		<li><a href="examples/jsonp.html">Jsonp</a></li>
		<li><a href="examples/ajax-jquery.html">Ajax by jQuery</a></li>
		<li><a href="examples/custom-source-data.html">Custom static data using sourceData option</a></li>
		<li><a href="examples/jsonp-filtered.html">Jsonp Filtered</a></li>
		<li><a href="examples/custom-tip.html">Custom Tip Item</a></li>		
		<li><a href="examples/geocoding-google.html">GeoCoding - Google</a></li>
		<li><a href="examples/geocoding-nominatim.html">GeoCoding - OSM Nominatim</a></li>
		<li><a href="examples/mobile.html">Mobile styled</a></li>
		<li><a href="examples/methods.html">Search text by method</a></li>
		<li><a href="examples/fuzzy.html">Custom filter, Fuzzy Search</a></li>
		<li><a href="examples/requirejs.html">RequireJS module loading</a></li>
		<li><a href="examples/location-url.html">Location to website</a></li>
		<li><a href="examples/popup.html">Search in popup content</a></li>
	</ul>
</div>
<div class="contents">
	<h4>Code repositories</h4>
	<a target="_blank" href="https://github.com/stefanocudini/leaflet-search">Github.com</a>
	<br />	
	<a target="_blank" href="https://npmjs.org/package/leaflet-search">Node Packaged Module</a>
	<br />	
	<h4>Website</h4>
	<a href="https://opengeo.tech/maps/leaflet-search/">opengeo.tech/maps/leaflet-search</a>
	<br />

	<h4>Download</h4>
	<ul>
		<li><a href="https://github.com/stefanocudini/leaflet-search/archive/master.zip">Dev Pack (.zip)</a></li>
		<li><a href="dist/leaflet-search.src.js">Source Code (.js)</a></li>
		<li><a href="dist/leaflet-search.min.js">Compressed (.min.js)</a></li>		
	</ul>
</div>

<div id="copy"><a href="https://opengeo.tech/">Website</a> &bull; <a rel="author" href="https://opengeo.tech/stefano-cudini/">Stefano Cudini</a></div>
	
<a href="https://github.com/stefanocudini/leaflet-search"><img id="ribbon" src="https://s3.amazonaws.com/github/ribbons/forkme_right_darkblue_121621.png" alt="Fork me on GitHub"></a>

<div style="clear:both;font-size:.85em;margin-bottom:1em">
	<b>Publish your application:</b>
	<a href="https://github.com/stefanocudini/leaflet-search/wiki/Websites-that-use-Leaflet-Control-Search">Websites that use Leaflet Control Search</a>
	<br />
	<br />
	<b>For questions and bugs:</b> I recommend you to <a href="https://github.com/stefanocudini/leaflet-search/issues">create New Issue</a> on Github repository.</strong><br />
	Or to obtain a fast response consult <a href="https://groups.google.com/forum/?hl=it&fromgroups=#!forum/leaflet-js">Official Leaflet community forum</a>.<br />
</div>

<div id="comments">
	<div id="disqus_thread"></div>
</div>



</body>
</html>
