"use strict";
(self["webpackChunkhozeh_gis"] = self["webpackChunkhozeh_gis"] || []).push([[392],{

/***/ 392:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });

Promise.resolve(/* import() */).then(__webpack_require__.bind(__webpack_require__, 392));

// تعریف کنترل MousePosition
L.Control.MousePosition = L.Control.extend({
  options: {
      position: "bottomright",
      separator: "-",
      emptyString: "",
      lngFirst: false,
      numDigits: 4, // تعداد ارقام اعشار برای مختصات جغرافیایی
      utmDigits: 4, // تعداد ارقام اعشار برای مختصات UTM
      lngFormatter: undefined,
      latFormatter: undefined,
      prefix: "مختصات جغرافیایی : ",
  },

  onAdd: function (map) {
      this._container = L.DomUtil.create("div", "leaflet-control-mouseposition");
      L.DomEvent.disableClickPropagation(this._container);
      map.on("mousemove", this._onMouseMove, this);
      
      // اضافه کردن event listener برای تغییر سایز صفحه
      this._resizeHandler = this._onResize.bind(this);
      window.addEventListener('resize', this._resizeHandler);
      
      this._container.innerHTML = this.options.emptyString;
      return this._container;
  },

  onRemove: function (map) {
      map.off("mousemove", this._onMouseMove);
      // حذف event listener برای تغییر سایز صفحه
      if (this._resizeHandler) {
          window.removeEventListener('resize', this._resizeHandler);
      }
  },

  // تابع برای مدیریت تغییر سایز صفحه
  _onResize: function() {
      // اگر مختصات فعلی وجود داره، دوباره محاسبه کن
      if (this._lastLatLng) {
          this._onMouseMove({ latlng: this._lastLatLng });
      }
  },

  
  _onMouseMove: function (e) {
    // ذخیره مختصات فعلی برای استفاده در resize
    this._lastLatLng = e.latlng;
    
    // فرمت‌دهی مختصات جغرافیایی با تعداد ارقام تنظیم شده
    var lng = this.options.lngFormatter
        ? this.options.lngFormatter(e.latlng.lng)
        : e.latlng.lng.toFixed(this.options.numDigits); // استفاده از numDigits
    var lat = this.options.latFormatter
        ? this.options.latFormatter(e.latlng.lat)
        : e.latlng.lat.toFixed(this.options.numDigits); // استفاده از numDigits

    // بررسی سایز صفحه
    var isMobile = window.innerWidth <= 768;
    var isTablet = window.innerWidth <= 1024 && window.innerWidth > 768;

    // تبدیل مختصات جغرافیایی به UTM (فقط در دسکتاپ)
    var utmStr = "";
    if (!isMobile) {
      var utm = e.latlng.utm();
      // بررسی نیمکره شمالی یا جنوبی
      var hemisphere = e.latlng.lat >= 0 ? "N" : "S";
      
      // فرمت‌دهی مختصات UTM با تعداد ارقام تنظیم شده
      utmStr = ` مختصات یو تی ام : (${utm.x.toFixed(this.options.utmDigits)}  , ${utm.y.toFixed(this.options.utmDigits)})   زون  ${utm.zone}${hemisphere}    `;

      // جایگزینی نقطه اعشاری با / در مختصات UTM
      utmStr = utmStr.replace(/\./g, "/");
    }

    // جایگزینی نقطه اعشاری با / در مختصات جغرافیایی
    lng = lng.toString().replace(".", "/");
    lat = lat.toString().replace(".", "/");

    var latLngStr = this.options.lngFirst
        ? `(${lng}, ${lat})`
        : `(${lat}, ${lng})`;

    // نمایش مختصات بر اساس سایز صفحه
    if (isMobile) {
      // فقط مختصات جغرافیایی در موبایل
      this._container.innerHTML = `${this.options.prefix} ${latLngStr}`;
    } else if (isTablet) {
      // مختصات جغرافیایی + UTM کوتاه در تبلت
      var shortUtm = utmStr.replace("مختصات یو تی ام : ", "").split("زون")[0];
      this._container.innerHTML = `${this.options.prefix} ${latLngStr} - ${shortUtm}`;
    } else {
      // همه مختصات در دسکتاپ
      this._container.innerHTML = `${this.options.prefix} ${latLngStr} ${this.options.separator} ${utmStr}`;
    }
  },
});

L.Map.mergeOptions({
  positionControl: false,
});

L.Map.addInitHook(function () {
  if (this.options.positionControl) {
      this.positionControl = new L.Control.MousePosition();
      this.addControl(this.positionControl);
  }
});

const mousePosition = function (options) {
  return new L.Control.MousePosition(options);
};

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (mousePosition);

/***/ })

}]);