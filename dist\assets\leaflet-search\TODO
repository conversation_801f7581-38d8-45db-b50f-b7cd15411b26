Tasks found in: src/leaflet-search-geocoder.js
    [Line: 61] [low]               //TODO refact resp data
Tasks found in: src/leaflet-search.js
    [Line: 16] [low] //TODO implement can do research on multiple sources layers and remote		
    [Line: 17] [low] //TODO history: false,		//show latest searches in tooltip		
    [Line: 21] [low] //TODO here insert function  search inputText FIRST in _recordsCache keys and if not find results.. 
    [Line: 24] [low] //TODO change structure of _recordsCache
    [Line: 27] [low] //TODO important optimization!!! always append data in this._recordsCache
    [Line: 31] [low] //TODO here insert function  search inputText FIRST in _recordsCache keys and if not find results.. 
    [Line: 34] [low] //TODO change structure of _recordsCache
    [Line: 62] [low] 		//TODO implements uniq option 'sourceData' to recognizes source type: url,array,callback or layer				
    [Line: 501] [low] 		//TODO throw new Error("propertyName '"+propName+"' not found in JSON data");
    [Line: 510] [low] 			//TODO add rnd param or randomize callback name! in recordsFromJsonp
    [Line: 531] [low] 		//TODO add rnd param or randomize callback name! in recordsFromAjax			
    [Line: 635] [low] 		//TODO implements autype without selection(useful for mobile device)
    [Line: 766] [low] 			//TODO _recordsFromLayer must return array of objects, formatted from _formatData
    [Line: 787] [low] 				//TODO refact!
    [Line: 979] [low] 	//TODO refact animate() more smooth! like this: http://goo.gl/DDlRs
    [Line: 1003] [low] 						//TODO use create event 'animateEnd' in L.Control.Search.Marker 
    [Line: 18] [med] //FIXME option condition problem {autoCollapse: true, markerLocation: true} not show location
    [Line: 19] [med] //FIXME option condition problem {autoCollapse: false }
    [Line: 910] [med] 		//FIXME autoCollapse option hide self._markerSearch before visualized!!
