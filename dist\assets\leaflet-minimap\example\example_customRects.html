<!DOCTYPE html>
<html>
<head>
	<title>MiniMap Demo</title>
	<meta charset="utf-8" />

	<link rel="stylesheet" href="./fullscreen.css" />

	<!-- Leaflet -->
	<link rel="stylesheet" href="https://unpkg.com/leaflet@1.0.1/dist/leaflet.css" />
	<script src="https://unpkg.com/leaflet@1.0.1/dist/leaflet.js" type="text/javascript"></script>

	<!-- Leaflet Plugins -->
	<link rel="stylesheet" href="../src/Control.MiniMap.css" />
	<script src="../src/Control.MiniMap.js" type="text/javascript"></script>

</head>
<body>
		<div id="map" ></div>

	<script type="text/javascript">
	
		var map = new L.Map('map');
		var osmUrl='http://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png';
		var osmAttrib='Map data &copy; OpenStreetMap contributors';
		var osm = new L.TileLayer(osmUrl, {minZoom: 5, maxZoom: 18, attribution: osmAttrib});

		map.addLayer(osm);
		map.setView(new L.LatLng(59.92448055859924, 10.758276373601069),10);
		
		//Plugin magic goes here! Note that you cannot use the same layer object again, as that will confuse the two map controls
		var osm2 = new L.TileLayer(osmUrl, {minZoom: 0, maxZoom: 13, attribution: osmAttrib });

		var rect1 = {color: "#ff1100", weight: 3};
		var rect2 = {color: "#0000AA", weight: 1, opacity:0, fillOpacity:0};
		var miniMap = new L.Control.MiniMap(osm2, { toggleDisplay: true, aimingRectOptions : rect1, shadowRectOptions: rect2}).addTo(map);
	</script>
</body>
</html>
