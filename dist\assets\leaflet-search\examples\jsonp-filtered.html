<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"> 
<html xmlns="http://www.w3.org/1999/xhtml"> 
<head> 
<title></title> 
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" /> 
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.3.0/dist/leaflet.css" />
<link rel="stylesheet" href="../src/leaflet-search.css" />
<link rel="stylesheet" href="style.css" />
<style>
#jsonresp {
	width:10em;
	min-height:10.5em;
	margin-left:1em;
	padding:.25em;
	overflow-y:scroll;
	float:left;
	background:#eee;
	border:1px solid #aaa;
	box-shadow: 2px 2px 6px #999;
	color:#666;
}
</style>
</head>

<body>
<h3><a href="../"><big>◄</big> Leaflet.Control.Search</a></h3>

<h4>JSONP Example: <em>search locations by third party jsonp service, with filter data</em></h4>
<div id="map"></div>

<div id="post-it">
<b>Search values:</b><br />
OpenStreetMap Data offer by MapQuest Open Platform<br />
<small><a href="http://open.mapquestapi.com/nominatim/">open.mapquestapi.com</a></small>
</div>

<div id="jsonresp">native JSON</div>

<script src="https://unpkg.com/leaflet@1.3.0/dist/leaflet.js"></script>
<script src="../src/leaflet-search.js"></script>
<script>

	var map = new L.Map('map', {zoom: 9, center: new L.latLng([41.575730,13.002411]) });
	map.addLayer(new L.TileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png'));	//base layer
	
	function formatJSON(rawjson) {	//callback that remap fields name
		var json = {},
			key, loc, disp = [];

		for(var i in rawjson)
		{
			disp = rawjson[i].display_name.split(',');	

			key = disp[0] +', '+ disp[1];
			
			loc = L.latLng( rawjson[i].lat, rawjson[i].lon );
			
			json[ key ]= loc;	//key,value format
		}
		
		return json;
	}
	
	var searchOpts = {
			url: 'http://nominatim.openstreetmap.org/search?format=json&q={s}',
			jsonpParam: 'json_callback',
			formatData: formatJSON,
			zoom: 10,
			minLength: 2,
			autoType: false,
			marker: {
				icon: false,
				animate: false
			}
		};
		
	map.addControl( new L.Control.Search(searchOpts) );

</script>

<div id="copy"><a href="https://opengeo.tech/">Website</a> &bull; <a rel="author" href="https://opengeo.tech/stefano-cudini/">Stefano Cudini</a></div>



</body>
</html>
