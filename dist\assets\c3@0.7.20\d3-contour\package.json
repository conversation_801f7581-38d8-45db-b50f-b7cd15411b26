{"name": "d3-contour", "version": "1.3.2", "description": "Compute contour polygons using marching squares.", "keywords": ["d3", "d3-module", "contour", "isoline"], "homepage": "https://d3js.org/d3-contour/", "license": "BSD-3-<PERSON><PERSON>", "author": {"name": "<PERSON>", "url": "http://bost.ocks.org/mike"}, "main": "dist/d3-contour.js", "unpkg": "dist/d3-contour.min.js", "jsdelivr": "dist/d3-contour.min.js", "module": "src/index.js", "repository": {"type": "git", "url": "https://github.com/d3/d3-contour.git"}, "scripts": {"pretest": "rollup -c", "test": "tape 'test/**/*-test.js' && eslint src", "prepublishOnly": "rm -rf dist && yarn test", "postpublish": "git push && git push --tags && cd ../d3.github.com && git pull && cp ../${npm_package_name}/dist/${npm_package_name}.js ${npm_package_name}.v${npm_package_version%%.*}.js && cp ../${npm_package_name}/dist/${npm_package_name}.min.js ${npm_package_name}.v${npm_package_version%%.*}.min.js && git add ${npm_package_name}.v${npm_package_version%%.*}.js ${npm_package_name}.v${npm_package_version%%.*}.min.js && git commit -m \"${npm_package_name} ${npm_package_version}\" && git push && cd - && zip -j dist/${npm_package_name}.zip -- LICENSE README.md dist/${npm_package_name}.js dist/${npm_package_name}.min.js"}, "devDependencies": {"eslint": "5", "rollup": "0.64", "rollup-plugin-terser": "1", "tape": "4"}, "dependencies": {"d3-array": "^1.1.1"}}