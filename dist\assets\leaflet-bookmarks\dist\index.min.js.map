{"version": 3, "file": "index.min.js", "sources": ["../src/string.js", "../src/storage/global.js", "../src/storage/localstorage.js", "../src/storage.js", "../src/formpopup.js", "../src/leaflet.delegate.js", "../src/bookmarks.js", "../index.js"], "sourcesContent": ["/**\n * Substitutes {{ obj.field }} in strings\n *\n * @param  {String}  str\n * @param  {Object}  object\n * @param  {RegExp=} regexp\n * @return {String}\n */\nexport function substitute(str, object, regexp) {\n  return str.replace(regexp || /{{([\\s\\S]+?)}}/g, function (match, name) {\n    name = trim(name);\n\n    if (name.indexOf(\".\") === -1) {\n      if (match.charAt(0) == \"\\\\\") return match.slice(1);\n      return object[name] != null ? object[name] : \"\";\n    } else {\n      // nested\n      let result = object;\n      name = name.split(\".\");\n      for (var i = 0, len = name.length; i < len; i++) {\n        if (name[i] in result) result = result[name[i]];\n        else return \"\";\n      }\n      return result;\n    }\n  });\n}\n\nconst alpha = \"abcdefghijklmnopqrstuvwxyz\";\n/**\n * Unique string from date. Puts character at the beginning,\n * for the sake of good manners\n *\n * @return {String}\n */\nexport function unique(prefix) {\n  return (\n    (prefix || alpha[Math.floor(Math.random() * alpha.length)]) +\n    new Date().getTime().toString(16)\n  );\n}\n\n/**\n * Trim whitespace\n * @param  {String} str\n * @return {String}\n */\nexport function trim(str) {\n  return str.replace(/^\\s+|\\s+$/g, \"\");\n}\n\n/**\n * Clean and trim\n * @param  {String} str\n * @return {String}\n */\nexport function clean(str) {\n  return trim(str.replace(/\\s+/g, \" \"));\n}\n", "/**\n * @type {Object}\n */\nconst data = {};\n\n/**\n * Object based storage\n * @class Storage.Engine.Global\n * @constructor\n */\nexport default class GlobalStorage {\n  constructor(prefix) {\n    /**\n     * @type {String}\n     */\n    this._prefix = prefix;\n  }\n\n  /**\n   * @param  {String}   key\n   * @param  {Function} callback\n   */\n  getItem(key, callback) {\n    callback(data[this._prefix + key]);\n  }\n\n  /**\n   * @param {String}   key\n   * @param {*}        item\n   * @param {Function} callback\n   */\n  setItem(key, item, callback) {\n    data[this._prefix + key] = item;\n    callback(item);\n  }\n\n  /**\n   * @param  {Function} callback\n   */\n  getAllItems(callback) {\n    const items = [];\n    for (const key in data) {\n      if (data.hasOwnProperty(key) && key.indexOf(this_prefix) === 0) {\n        items.push(data[key]);\n      }\n    }\n    callback(items);\n  }\n\n  /**\n   * @param  {String}   key\n   * @param  {Function} callback\n   */\n  removeItem(key, callback) {\n    this.getItem(key, (item) => {\n      if (item) {\n        delete data[this._prefix + key];\n      } else {\n        item = null;\n      }\n      if (callback) callback(item);\n    });\n  }\n}\n", "/**\n * @const\n * @type {RegExp}\n */\nconst JSON_RE = /^[\\{\\[](.)*[\\]\\}]$/;\n\n/**\n * LocalStoarge based storage\n */\nexport default class LocalStorage {\n  constructor(prefix) {\n    /**\n     * @type {String}\n     */\n    this._prefix = prefix;\n\n    /**\n     * @type {LocalStorage}\n     */\n    this._storage = window.localStorage;\n  }\n\n  /**\n   * @param  {String}   key\n   * @param  {Function} callback\n   */\n  getItem(key, callback) {\n    let item = this._storage.getItem(this._prefix + key);\n    if (item && JSON_RE.test(item)) {\n      item = JSON.parse(item);\n    }\n    callback(item);\n  }\n\n  /**\n   * @param  {Function} callback\n   */\n  getAllItems(callback) {\n    const items = [];\n    const prefixLength = this._prefix.length;\n    for (const key in this._storage) {\n      if (\n        this._storage.getItem(key) !== null &&\n        key.indexOf(this._prefix) === 0\n      ) {\n        this.getItem(key.substring(prefixLength), (item) => items.push(item));\n      }\n    }\n    callback(items);\n  }\n\n  /**\n   * @param  {String}   key\n   * @param  {Function} callback\n   */\n  removeItem(key, callback) {\n    const self = this;\n    this.getItem(key, (item) => {\n      this._storage.removeItem(self._prefix + key);\n      if (callback) callback(item);\n    });\n  }\n\n  /**\n   * @param  {String}   key\n   * @param  {*}        item\n   * @param  {Function} callback\n   */\n  setItem(key, item, callback) {\n    let itemStr = item.toString();\n    if (itemStr === \"[object Object]\") {\n      itemStr = JSON.stringify(item);\n    }\n    this._storage.setItem(this._prefix + key, itemStr);\n    callback(item);\n  }\n}\n", "import { unique } from \"./string\";\n\nimport XHR from \"./storage/xhr\";\nimport GlobalStorage from \"./storage/global\";\nimport LocalStorage from \"./storage/localstorage\";\n\n/**\n * @const\n * @enum {Number}\n */\nexport const EngineType = {\n  // XHR: 1, // we don't have it included, it's a stub\n  GLOBALSTORAGE: 2,\n  LOCALSTORAGE: 3,\n};\n\n/**\n * Persistent storage, depends on engine choice: localStorage/ajax\n * @param {String} name\n */\nexport default class Storage {\n  constructor(name, engineType) {\n    if (typeof name !== \"string\") {\n      engineType = name;\n      name = unique();\n    }\n\n    /**\n     * @type {String}\n     */\n    this._name = name;\n\n    /**\n     * @type {Storage.Engine}\n     */\n    this._engine = Storage.createEngine(\n      engineType,\n      this._name,\n      Array.prototype.slice.call(arguments, 2)\n    );\n  }\n\n  /**\n   * Engine factory\n   * @param  {Number} type\n   * @param  {String} prefix\n   * @return {Storage.Engine}\n   */\n  static createEngine(type, prefix, args) {\n    if (type === EngineType.GLOBALSTORAGE) {\n      return new GlobalStorage(prefix);\n    }\n    if (type === EngineType.LOCALSTORAGE) {\n      return new LocalStorage(prefix);\n    }\n  }\n\n  /**\n   * @param {String}   key\n   * @param {*}        item\n   * @param {Function} callback\n   */\n  setItem(key, item, callback) {\n    this._engine.setItem(key, item, callback);\n    return this;\n  }\n\n  /**\n   * @param  {String}   key\n   * @param  {Function} callback\n   */\n  getItem(key, callback) {\n    this._engine.getItem(key, callback);\n    return this;\n  }\n\n  /**\n   * @param  {Function} callback\n   */\n  getAllItems(callback) {\n    this._engine.getAllItems(callback);\n  }\n\n  /**\n   * @param  {String}   key\n   * @param  {Function} callback\n   */\n  removeItem(key, callback) {\n    this._engine.removeItem(key, callback);\n  }\n}\n", "import L from \"leaflet\";\nimport { unique, substitute } from \"./string\";\n\nconst modes = {\n  CREATE: 1,\n  UPDATE: 2,\n  SHOW: 3,\n  OPTIONS: 4,\n};\n\n/**\n * New bookmark form popup\n *\n * @class  FormPopup\n * @extends {L.Popup}\n */\nexport default L.Popup.extend(\n  /** @lends FormPopup.prototype */ {\n    statics: { modes },\n\n    /**\n     * @type {Object}\n     */\n    options: {\n      mode: modes.CREATE,\n      className: \"leaflet-bookmarks-form-popup\",\n      templateOptions: {\n        formClass: \"leaflet-bookmarks-form\",\n        inputClass: \"leaflet-bookmarks-form-input\",\n        inputErrorClass: \"has-error\",\n        idInputClass: \"leaflet-bookmarks-form-id\",\n        coordsClass: \"leaflet-bookmarks-form-coords\",\n        submitClass: \"leaflet-bookmarks-form-submit\",\n        inputPlaceholder: \"Bookmark name\",\n        removeClass: \"leaflet-bookmarks-form-remove\",\n        editClass: \"leaflet-bookmarks-form-edit\",\n        cancelClass: \"leaflet-bookmarks-form-cancel\",\n        editableClass: \"editable\",\n        removableClass: \"removable\",\n        menuItemClass: \"nav-item\",\n        editMenuText: \"Edit\",\n        removeMenuText: \"Remove\",\n        cancelMenuText: \"Cancel\",\n        submitTextCreate: \"+\",\n        submitTextEdit: '<span class=\"icon-checkmark\"></span>',\n      },\n      generateNames: false,\n      minWidth: 160,\n      generateNamesPrefix: \"Bookmark \",\n      template:\n        '<form class=\"{{ formClass }}\">' +\n        '<div class=\"input-group\"><input type=\"text\" name=\"bookmark-name\" ' +\n        'placeholder=\"{{ inputPlaceholder }}\" class=\"form-control {{ inputClass }}\" value=\"{{ name }}\">' +\n        '<input type=\"hidden\" class={{ idInputClass }} value=\"{{ id }}\">' +\n        '<button type=\"submit\" class=\"input-group-addon {{ submitClass }}\">' +\n        \"{{ submitText }}</button></div>\" +\n        '<div class=\"{{ coordsClass }}\">{{ coords }}</div>' +\n        \"</form>\",\n      menuTemplate:\n        '<ul class=\"nav {{ mode }}\" role=\"menu\">' +\n        '<li class=\"{{ editClass }}\"><a href=\"#\" class=\"{{ menuItemClass }}\">{{ editMenuText }}</a></li>' +\n        '<li class=\"{{ removeClass }}\"><a href=\"#\" class=\"{{ menuItemClass }}\">{{ removeMenuText }}</a></li>' +\n        '<li><a href=\"#\" class=\"{{ menuItemClass }} {{ cancelClass }}\">{{ cancelMenuText }}</a></li>' +\n        \"</ul>\",\n    },\n\n    /**\n     * @param  {Object}  options\n     * @param  {L.Layer} source\n     * @param  {Object=} bookmark\n     *\n     * @constructor\n     */\n    initialize: function (options, source, control, bookmark) {\n      /**\n       * @type {Object}\n       */\n      this._bookmark = bookmark;\n\n      /**\n       * @type {L.Control.Bookmarks}\n       */\n      this._control = control;\n\n      /**\n       * @type {L.LatLng}\n       */\n      this._latlng = source.getLatLng();\n\n      /**\n       * For dragging purposes we're not maintaining the usual\n       * link between the marker and Popup, otherwise it will simply be destroyed\n       */\n      source._popup_ = this;\n\n      L.Popup.prototype.initialize.call(this, options, source);\n    },\n\n    /**\n     * Add menu button\n     */\n    _initLayout: function () {\n      L.Popup.prototype._initLayout.call(this);\n\n      if (\n        this.options.mode === modes.SHOW &&\n        (this._bookmark.editable || this._bookmark.removable)\n      ) {\n        const menuButton = (this._menuButton = L.DomUtil.create(\n          \"a\",\n          \"leaflet-popup-menu-button\"\n        ));\n        this._container.insertBefore(menuButton, this._closeButton);\n        menuButton.href = \"#menu\";\n        menuButton.innerHTML = '<span class=\"menu-icon\"></span>';\n        L.DomEvent.disableClickPropagation(menuButton);\n        L.DomEvent.on(menuButton, \"click\", this._onMenuButtonClick, this);\n      }\n    },\n\n    /**\n     * Show options menu\n     */\n    _showMenu: function () {\n      this._map.fire(\"bookmark:options\", { data: this._bookmark });\n    },\n\n    /**\n     * @param  {MouseEvent} evt\n     */\n    _onMenuButtonClick: function (evt) {\n      L.DomEvent.preventDefault(evt);\n      this._showMenu();\n      this.close();\n    },\n\n    /**\n     * Renders template only\n     * @override\n     */\n    _updateContent: function () {\n      let content;\n      if (this.options.mode === modes.SHOW) {\n        content = this._control._getPopupContent(this._bookmark);\n      } else {\n        let template = this.options.template;\n        let submitText = this.options.templateOptions.submitTextCreate;\n        if (this.options.mode === modes.OPTIONS) {\n          template = this.options.menuTemplate;\n        }\n        if (this.options.mode === modes.UPDATE) {\n          submitText = this.options.templateOptions.submitTextEdit;\n        }\n        const modeClass = [];\n        if (this._bookmark.editable) {\n          modeClass.push(this.options.templateOptions.editableClass);\n        }\n        if (this._bookmark.removable) {\n          modeClass.push(this.options.templateOptions.removableClass);\n        }\n        content = substitute(\n          template,\n          L.Util.extend(\n            {},\n            this._bookmark || {},\n            this.options.templateOptions,\n            {\n              submitText: submitText,\n              coords: this.formatCoords(\n                this._source.getLatLng(),\n                this._map.getZoom()\n              ),\n              mode: modeClass.join(\" \"),\n            }\n          )\n        );\n      }\n      this._content = content;\n      L.Popup.prototype._updateContent.call(this);\n      this._onRendered();\n    },\n\n    /**\n     * Form rendered, set up create or edit\n     */\n    _onRendered: function () {\n      if (\n        this.options.mode === modes.CREATE ||\n        this.options.mode === modes.UPDATE\n      ) {\n        const form = this._contentNode.querySelector(\n          \".\" + this.options.templateOptions.formClass\n        );\n        const input = form.querySelector(\n          \".\" + this.options.templateOptions.inputClass\n        );\n\n        L.DomEvent.on(form, \"submit\", this._onSubmit, this);\n        setTimeout(this._setFocus.bind(this), 250);\n      } else if (this.options.mode === modes.OPTIONS) {\n        L.DomEvent.delegate(\n          this._container,\n          \".\" + this.options.templateOptions.editClass,\n          \"click\",\n          this._onEditClick,\n          this\n        );\n        L.DomEvent.delegate(\n          this._container,\n          \".\" + this.options.templateOptions.removeClass,\n          \"click\",\n          this._onRemoveClick,\n          this\n        );\n        L.DomEvent.delegate(\n          this._container,\n          \".\" + this.options.templateOptions.cancelClass,\n          \"click\",\n          this._onCancelClick,\n          this\n        );\n      }\n    },\n\n    /**\n     * Set focus at the end of input\n     */\n    _setFocus: function () {\n      const input = this._contentNode.querySelector(\n        \".\" + this.options.templateOptions.inputClass\n      );\n      // Multiply by 2 to ensure the cursor always ends up at the end;\n      // Opera sometimes sees a carriage return as 2 characters.\n      const strLength = input.value.length * 2;\n      input.focus();\n      input.setSelectionRange(strLength, strLength);\n    },\n\n    /**\n     * Edit button clicked\n     * @param  {Event} evt\n     */\n    _onEditClick: function (evt) {\n      L.DomEvent.preventDefault(evt);\n      this._map.fire(\"bookmark:edit\", { data: this._bookmark });\n      this.close();\n    },\n\n    /**\n     * Remove button clicked\n     * @param  {Event} evt\n     */\n    _onRemoveClick: function (evt) {\n      L.DomEvent.preventDefault(evt);\n      this._map.fire(\"bookmark:remove\", { data: this._bookmark });\n      this.close();\n    },\n\n    /**\n     * Back from options view\n     * @param  {Event} evt\n     */\n    _onCancelClick: function (evt) {\n      L.DomEvent.preventDefault(evt);\n      this._map.fire(\"bookmark:show\", { data: this._bookmark });\n      this.close();\n    },\n\n    /**\n     * Creates bookmark object from form data\n     * @return {Object}\n     */\n    _getBookmarkData: function () {\n      const options = this.options;\n      if (options.getBookmarkData) {\n        return options.getBookmarkData.call(this);\n      }\n      const input = this._contentNode.querySelector(\n        \".\" + options.templateOptions.inputClass\n      );\n      const idInput = this._contentNode.querySelector(\n        \".\" + options.templateOptions.idInputClass\n      );\n      return {\n        latlng: this._source.getLatLng(),\n        zoom: this._map.getZoom(),\n        name: input.value,\n        id: idInput.value || unique(),\n      };\n    },\n\n    /**\n     * Form submit, dispatch eventm close popup\n     * @param {Event} evt\n     */\n    _onSubmit: function (evt) {\n      L.DomEvent.stop(evt);\n\n      const input = this._contentNode.querySelector(\n        \".\" + this.options.templateOptions.inputClass\n      );\n      input.classList.remove(this.options.templateOptions.inputErrorClass);\n\n      if (input.value === \"\" && this.options.generateNames) {\n        input.value = unique(this.options.generateNamesPrefix);\n      }\n\n      const validate = this.options.validateInput || (() => true);\n\n      if (input.value !== \"\" && validate.call(this, input.value)) {\n        const bookmark = L.Util.extend(\n          {},\n          this._bookmark,\n          this._getBookmarkData()\n        );\n        const map = this._map;\n\n        this.close();\n        if (this.options.mode === modes.CREATE) {\n          map.fire(\"bookmark:add\", { data: bookmark });\n        } else {\n          map.fire(\"bookmark:edited\", { data: bookmark });\n        }\n      } else {\n        input.classList.add(this.options.templateOptions.inputErrorClass);\n      }\n    },\n\n    /**\n     * @param  {L.LatLng} coords\n     * @param  {Number=}  zoom\n     * @return {String}\n     */\n    formatCoords: function (coords, zoom) {\n      if (this.options.formatCoords) {\n        return this.options.formatCoords.call(this, coords, zoom);\n      }\n      return [coords.lat.toFixed(4), coords.lng.toFixed(4), zoom].join(\n        \",&nbsp;\"\n      );\n    },\n\n    /**\n     * Hook to source movements\n     * @param  {L.Map} map\n     * @return {Element}\n     */\n    onAdd: function (map) {\n      this._source.on(\"dragend\", this._onSourceMoved, this);\n      this._source.on(\"dragstart\", this._onSourceMoveStart, this);\n      return L.Popup.prototype.onAdd.call(this, map);\n    },\n\n    /**\n     * @param  {L.Map} map\n     */\n    onRemove: function (map) {\n      this._source.off(\"dragend\", this._onSourceMoved, this);\n      L.Popup.prototype.onRemove.call(this, map);\n    },\n\n    /**\n     * Marker drag\n     */\n    _onSourceMoveStart: function () {\n      // store\n      this._bookmark = L.Util.extend(\n        this._bookmark || {},\n        this._getBookmarkData()\n      );\n      this._container.style.display = \"none\";\n    },\n\n    /**\n     * Marker moved\n     * @param  {Event} e\n     */\n    _onSourceMoved: function (e) {\n      this._latlng = this._source.getLatLng();\n      this._container.style.display = \"\";\n      this._source.openPopup();\n      this.update();\n    },\n  }\n);\n", "import L from \"leaflet\";\n\n/**\n * Courtesy of https://github.com/component/matches-selector\n */\nconst matchesSelector = ((ElementPrototype) => {\n  const matches =\n    ElementPrototype.matches ||\n    ElementPrototype.webkitMatchesSelector ||\n    ElementPrototype.mozMatchesSelector ||\n    ElementPrototype.msMatchesSelector ||\n    ElementPrototype.oMatchesSelector ||\n    // hello IE\n    function (selector) {\n      var node = this,\n        parent = node.parentNode || node.document,\n        nodes = parent.querySelectorAll(selector);\n\n      for (var i = 0, len = nodes.length; i < len; ++i) {\n        if (nodes[i] == node) return true;\n      }\n      return false;\n    };\n\n  /**\n   * @param  {Element} element\n   * @param  {String} selector\n   * @return {Boolean}\n   */\n  return function (element, selector) {\n    return matches.call(element, selector);\n  };\n})(Element.prototype);\n\n/**\n * Courtesy of https://github.com/component/closest\n *\n * @param  {Element} element\n * @param  {String}  selector\n * @param  {Boolean} checkSelf\n * @param  {Element} root\n *\n * @return {Element|Null}\n */\nfunction closest(element, selector, checkSelf, root) {\n  element = checkSelf\n    ? {\n        parentNode: element,\n      }\n    : element;\n\n  root = root || document;\n\n  // Make sure `element !== document` and `element != null`\n  // otherwise we get an illegal invocation\n  while ((element = element.parentNode) && element !== document) {\n    if (matchesSelector(element, selector)) return element;\n    // After `matches` on the edge case that\n    // the selector matches the root\n    // (when the root is not the document)\n    if (element === root) return null;\n  }\n}\n\n/**\n * Based on https://github.com/component/delegate\n *\n * @param  {Element}  el\n * @param  {String}   selector\n * @param  {String}   type\n * @param  {Function} fn\n *\n * @return {Function}\n */\nL.DomEvent.delegate = function (el, selector, type, fn, bind) {\n  return L.DomEvent.on(el, type, (evt) => {\n    const target = evt.target || evt.srcElement;\n    evt.delegateTarget = closest(target, selector, true, el);\n    if (evt.delegateTarget && !evt.propagationStopped) {\n      fn.call(bind || el, evt);\n    }\n  });\n};\n", "import L from \"leaflet\";\nimport Storage, { EngineType } from \"./storage\";\nimport FormPopup from \"./formpopup\";\nimport { substitute } from \"./string\";\nimport \"./leaflet.delegate\";\n\n// expose\nL.Util._template = L.Util._template || substitute;\n\n/**\n * Bookmarks control\n * @class  L.Control.Bookmarks\n * @extends {L.Control}\n */\nexport default L.Control.extend(\n  /**  @lends Bookmarks.prototype */ {\n    statics: {\n      Storage,\n      FormPopup,\n    },\n\n    /**\n     * @type {Object}\n     */\n    options: {\n      localStorage: true,\n\n      /* you can provide access to your own storage,\n       * xhr for example, but make sure it has all\n       * required endpoints:\n       *\n       * .getItem(id, callback)\n       * .setItem(id, callback)\n       * .getAllItems(callback)\n       * .removeItem(id, callback)\n       */\n      storage: null,\n      name: \"leaflet-bookmarks\",\n      position: \"topright\", // chose your own if you want\n\n      containerClass: \"leaflet-bar leaflet-bookmarks-control\",\n      expandedClass: \"expanded\",\n      headerClass: \"bookmarks-header\",\n      listClass: \"bookmarks-list\",\n      iconClass: \"bookmarks-icon\",\n      iconWrapperClass: \"bookmarks-icon-wrapper\",\n      listWrapperClass: \"bookmarks-list-wrapper\",\n      listWrapperClassAdd: \"list-with-button\",\n      wrapperClass: \"bookmarks-container\",\n      addBookmarkButtonCss: \"add-bookmark-button\",\n\n      animateClass: \"bookmark-added-anim\",\n      animateDuration: 150,\n\n      formPopup: {\n        popupClass: \"bookmarks-popup\",\n      },\n\n      bookmarkTemplate:\n        '<li class=\"{{ itemClass }}\" data-id=\"{{ data.id }}\">' +\n        '<span class=\"{{ removeClass }}\">&times;</span>' +\n        '<span class=\"{{ nameClass }}\">{{ data.name }}</span>' +\n        '<span class=\"{{ coordsClass }}\">{{ data.coords }}</span>' +\n        \"</li>\",\n\n      emptyTemplate:\n        '<li class=\"{{ itemClass }} {{ emptyClass }}\">' +\n        \"{{ data.emptyMessage }}</li>\",\n\n      dividerTemplate: '<li class=\"divider\"></li>',\n\n      bookmarkTemplateOptions: {\n        itemClass: \"bookmark-item\",\n        nameClass: \"bookmark-name\",\n        coordsClass: \"bookmark-coords\",\n        removeClass: \"bookmark-remove\",\n        emptyClass: \"bookmarks-empty\",\n      },\n\n      defaultBookmarkOptions: {\n        editable: true,\n        removable: true,\n      },\n\n      title: \"Bookmarks\",\n      emptyMessage: \"No bookmarks yet\",\n      addBookmarkMessage: \"Add new bookmark\",\n      collapseOnClick: true,\n      scrollOnAdd: true,\n      scrollDuration: 1000,\n      popupOnShow: true,\n      addNewOption: true,\n\n      /**\n       * This you can change easily to output\n       * whatever you have stored in bookmark\n       *\n       * @type {String}\n       */\n      popupTemplate:\n        \"<div><h3>{{ name }}</h3><p>{{ latlng }}, {{ zoom }}</p></div>\",\n\n      /**\n       * Prepare your bookmark data for template.\n       * If you don't change it, the context of this\n       * function will be bookmarks control, so you can\n       * access the map or other things from here\n       *\n       * @param  {Object} bookmark\n       * @return {Object}\n       */\n      getPopupContent: function (bookmark) {\n        return substitute(this.options.popupTemplate, {\n          latlng: this.formatCoords(bookmark.latlng),\n          name: bookmark.name,\n          zoom: this._map.getZoom(),\n        });\n      },\n    },\n\n    /**\n     * @param  {Object} options\n     * @constructor\n     */\n    initialize: function (options) {\n      options = options || {};\n\n      /**\n       * Bookmarks array\n       * @type {Array}\n       */\n      this._data = [];\n\n      /**\n       * @type {Element}\n       */\n      this._list = null;\n\n      /**\n       * @type {L.Marker}\n       */\n      this._marker = null;\n\n      /**\n       * @type {HTMLElement}\n       */\n      this._addButton = null;\n\n      /**\n       * @type {Element}\n       */\n      this._icon = null;\n\n      /**\n       * @type {Boolean}\n       */\n      this._isCollapsed = true;\n\n      L.Util.setOptions(this, options);\n\n      /**\n       * @type {Storage}\n       */\n      this._storage =\n        options.storage ||\n        (this.options.localStorage\n          ? new Storage(this.options.name, EngineType.LOCALSTORAGE)\n          : new Storage(this.options.name, EngineType.GLOBALSTORAGE));\n\n      L.Control.prototype.initialize.call(this, this.options);\n    },\n\n    /**\n     * @param {L.Map} map\n     */\n    onAdd: function (map) {\n      const container = (this._container = L.DomUtil.create(\n        \"div\",\n        this.options.containerClass\n      ));\n\n      L.DomEvent.disableClickPropagation(container).disableScrollPropagation(\n        container\n      );\n      container.innerHTML =\n        '<div class=\"' +\n        this.options.headerClass +\n        '\"><span class=\"' +\n        this.options.iconWrapperClass +\n        '\">' +\n        '<span class=\"' +\n        this.options.iconClass +\n        '\"></span></span>';\n\n      this._icon = container.querySelector(\".\" + this.options.iconClass);\n      this._icon.title = this.options.title;\n\n      this._createList(this.options.bookmarks);\n\n      const wrapper = L.DomUtil.create(\n        \"div\",\n        this.options.wrapperClass,\n        this._container\n      );\n      wrapper.appendChild(this._listwrapper);\n\n      this._initLayout();\n\n      L.DomEvent.on(container, \"click\", this._onClick, this).on(\n        container,\n        \"contextmenu\",\n        L.DomEvent.stopPropagation\n      );\n\n      map\n        .on(\"bookmark:new\", this._onBookmarkAddStart, this)\n        .on(\"bookmark:add\", this._onBookmarkAdd, this)\n        .on(\"bookmark:edited\", this._onBookmarkEdited, this)\n        .on(\"bookmark:show\", this._onBookmarkShow, this)\n        .on(\"bookmark:edit\", this._onBookmarkEdit, this)\n        .on(\"bookmark:options\", this._onBookmarkOptions, this)\n        .on(\"bookmark:remove\", this._onBookmarkRemove, this)\n        .on(\"resize\", this._initLayout, this);\n\n      return container;\n    },\n\n    /**\n     * @param  {L.Map} map\n     */\n    onRemove: function (map) {\n      map\n        .off(\"bookmark:new\", this._onBookmarkAddStart, this)\n        .off(\"bookmark:add\", this._onBookmarkAdd, this)\n        .off(\"bookmark:edited\", this._onBookmarkEdited, this)\n        .off(\"bookmark:show\", this._onBookmarkShow, this)\n        .off(\"bookmark:edit\", this._onBookmarkEdit, this)\n        .off(\"bookmark:options\", this._onBookmarkOptions, this)\n        .off(\"bookmark:remove\", this._onBookmarkRemove, this)\n        .off(\"resize\", this._initLayout, this);\n\n      if (this._marker) this._marker._popup_.close();\n\n      if (this.options.addNewOption) {\n        L.DomEvent.off(\n          this._container.querySelector(\n            \".\" + this.options.addBookmarkButtonCss\n          ),\n          \"click\",\n          this._onAddButtonPressed,\n          this\n        );\n      }\n\n      this._marker = null;\n      this._popup = null;\n      this._container = null;\n    },\n\n    /**\n     * @return {Array.<Object>}\n     */\n    getData: function () {\n      return this._filterBookmarksOutput(this._data);\n    },\n\n    /**\n     * @param  {Array.<Number>|Function|null} bookmarks\n     */\n    _createList: function (bookmarks) {\n      this._listwrapper = L.DomUtil.create(\n        \"div\",\n        this.options.listWrapperClass,\n        this._container\n      );\n      this._list = L.DomUtil.create(\n        \"ul\",\n        this.options.listClass,\n        this._listwrapper\n      );\n\n      // select bookmark\n      L.DomEvent.delegate(\n        this._list,\n        \".\" + this.options.bookmarkTemplateOptions.itemClass,\n        \"click\",\n        this._onBookmarkClick,\n        this\n      );\n\n      this._setEmptyListContent();\n\n      if (L.Util.isArray(bookmarks)) {\n        this._appendItems(bookmarks);\n      } else if (typeof bookmarks === \"function\") {\n        this._appendItems(bookmarks());\n      } else {\n        this._storage.getAllItems((bookmarks) => this._appendItems(bookmarks));\n      }\n    },\n\n    /**\n     * Empty list\n     */\n    _setEmptyListContent: function () {\n      this._list.innerHTML = substitute(\n        this.options.emptyTemplate,\n        L.Util.extend(this.options.bookmarkTemplateOptions, {\n          data: {\n            emptyMessage: this.options.emptyMessage,\n          },\n        })\n      );\n    },\n\n    /**\n     * Sees that the list size is not too big\n     */\n    _initLayout: function () {\n      const size = this._map.getSize();\n      this._listwrapper.style.maxHeight =\n        Math.min(size.y * 0.6, size.y - 100) + \"px\";\n\n      if (this.options.position === \"topleft\") {\n        L.DomUtil.addClass(this._container, \"leaflet-bookmarks-to-right\");\n      }\n      if (this.options.addNewOption) {\n        const addButton = L.DomUtil.create(\n          \"div\",\n          this.options.addBookmarkButtonCss\n        );\n        if (this._addButton === null) {\n          this._listwrapper.parentNode.appendChild(addButton);\n          this._addButton = addButton;\n          this._listwrapper.parentNode.classList.add(\n            this.options.listWrapperClassAdd\n          );\n          addButton.innerHTML =\n            '<span class=\"plus\">+</span>' +\n            '<span class=\"content\">' +\n            this.options.addBookmarkMessage +\n            \"</span>\";\n          L.DomEvent.on(addButton, \"click\", this._onAddButtonPressed, this);\n        }\n      }\n    },\n\n    /**\n     * @param  {MouseEvent} evt\n     */\n    _onAddButtonPressed: function (evt) {\n      L.DomEvent.stop(evt);\n      this.collapse();\n      this._map.fire(\"bookmark:new\", {\n        latlng: this._map.getCenter(),\n      });\n    },\n\n    /**\n     * I don't care if they're unique or not,\n     * if you do - handle this\n     *\n     * @param {Array.<Object>} bookmarks\n     * @return {Array.<Object>}\n     */\n    _filterBookmarks: function (bookmarks) {\n      if (this.options.filterBookmarks) {\n        return this.options.filterBookmarks.call(this, bookmarks);\n      }\n      return bookmarks;\n    },\n\n    /**\n     * Filter bookmarks for output. This one allows you to save dividers as well\n     *\n     * @param {Array.<Object>} bookmarks\n     * @return {Array.<Object>}\n     */\n    _filterBookmarksOutput: function (bookmarks) {\n      if (this.options.filterBookmarksOutput) {\n        return this.options.filterBookmarksOutput.call(this, bookmarks);\n      }\n      return bookmarks;\n    },\n\n    /**\n     * Append list items(render)\n     * @param  {Array.<Object>} bookmarks\n     */\n    _appendItems: function (bookmarks) {\n      let html = \"\";\n      let wasEmpty = this._data.length === 0;\n      let bookmark;\n\n      // maybe you have something in mind?\n      bookmarks = this._filterBookmarks(bookmarks);\n\n      // store\n      this._data = this._data.concat(bookmarks);\n\n      for (let i = 0, len = bookmarks.length; i < len; i++) {\n        html += this._renderBookmarkItem(bookmarks[i]);\n      }\n\n      if (html !== \"\") {\n        // replace `empty` message if needed\n        if (wasEmpty) {\n          this._list.innerHTML = html;\n        } else {\n          this._list.innerHTML += html;\n        }\n      }\n\n      if (this._isCollapsed) {\n        const container = this._container;\n        const className = this.options.animateClass;\n        container.classList.add(className);\n        window.setTimeout(function () {\n          container.classList.remove(className);\n        }, this.options.animateDuration);\n      } else {\n        this._scrollToLast();\n      }\n    },\n\n    /**\n     * Scrolls to last element of the list\n     */\n    _scrollToLast: function () {\n      const listwrapper = this._listwrapper;\n      let pos = this._listwrapper.scrollTop;\n      const targetVal = this._list.lastChild.offsetTop;\n      let start = 0;\n\n      const step =\n        (targetVal - pos) / (this.options.scrollDuration / (1000 / 16));\n\n      function scroll(timestamp) {\n        if (!start) start = timestamp;\n        //var progress = timestamp - start;\n\n        pos = Math.min(pos + step, targetVal);\n        listwrapper.scrollTop = pos;\n        if (pos !== targetVal) {\n          L.Util.requestAnimFrame(scroll);\n        }\n      }\n      L.Util.requestAnimFrame(scroll);\n    },\n\n    /**\n     * Render single bookmark item\n     * @param  {Object} bookmark\n     * @return {String}\n     */\n    _renderBookmarkItem: function (bookmark) {\n      if (bookmark.divider) {\n        return substitute(this.options.dividerTemplate, bookmark);\n      }\n\n      this.options.bookmarkTemplateOptions.data =\n        this._getBookmarkDataForTemplate(bookmark);\n\n      return substitute(\n        this.options.bookmarkTemplate,\n        this.options.bookmarkTemplateOptions\n      );\n    },\n\n    /**\n     * Extracts data and style expressions for item template\n     * @param  {Object} bookmark\n     * @return {Object}\n     */\n    _getBookmarkDataForTemplate: function (bookmark) {\n      if (this.options.getBookmarkDataForTemplate) {\n        return this.options.getBookmarkDataForTemplate.call(this, bookmark);\n      }\n      return {\n        coords: this.formatCoords(bookmark.latlng),\n        name: this.formatName(bookmark.name),\n        zoom: bookmark.zoom,\n        id: bookmark.id,\n      };\n    },\n\n    /**\n     * @param  {L.LatLng} latlng\n     * @return {String}\n     */\n    formatCoords: function (latlng) {\n      if (this.options.formatCoords) {\n        return this.options.formatCoords.call(this, latlng);\n      }\n      return latlng[0].toFixed(4) + \",&nbsp;\" + latlng[1].toFixed(4);\n    },\n\n    /**\n     * @param  {String} name\n     * @return {String}\n     */\n    formatName: function (name) {\n      if (this.options.formatName) {\n        return this.options.formatName.call(this, name);\n      }\n      return name;\n    },\n\n    /**\n     * Shows bookmarks list\n     */\n    expand: function () {\n      L.DomUtil.addClass(this._container, this.options.expandedClass);\n      this._isCollapsed = false;\n    },\n\n    /**\n     * Hides bookmarks list and the form\n     */\n    collapse: function () {\n      L.DomUtil.removeClass(this._container, this.options.expandedClass);\n      this._isCollapsed = true;\n    },\n\n    /**\n     * @param  {Event} evt\n     */\n    _onClick: function (evt) {\n      const expanded = L.DomUtil.hasClass(\n        this._container,\n        this.options.expandedClass\n      );\n      let target = evt.target || evt.srcElement;\n\n      if (expanded) {\n        if (target === this._container) {\n          return this.collapse();\n        }\n        // check if it's inside the header\n        while (target !== this._container) {\n          if (\n            L.DomUtil.hasClass(target, this.options.headerClass) ||\n            L.DomUtil.hasClass(target, this.options.listWrapperClass)\n          ) {\n            this.collapse();\n            break;\n          }\n          target = target.parentNode;\n        }\n      } else this.expand();\n    },\n\n    /**\n     * @param  {Object} evt\n     */\n    _onBookmarkAddStart: function (evt) {\n      if (this._marker) this._popup.close();\n\n      this._marker = new L.Marker(evt.latlng, {\n        icon: this.options.icon || new L.Icon.Default(),\n        draggable: true,\n        riseOnHover: true,\n      }).addTo(this._map);\n      this._marker.on(\"popupclose\", this._onPopupClosed, this);\n\n      // open form\n      this._popup = new L.Control.Bookmarks.FormPopup(\n        L.Util.extend(this.options.formPopup, {\n          mode: L.Control.Bookmarks.FormPopup.modes.CREATE,\n        }),\n        this._marker,\n        this,\n        L.Util.extend({}, evt.data, this.options.defaultBookmarkOptions)\n      ).addTo(this._map);\n    },\n\n    /**\n     * Bookmark added\n     * @param  {Object} bookmark\n     */\n    _onBookmarkAdd: function (bookmark) {\n      const map = this._map;\n      bookmark = this._cleanBookmark(bookmark.data);\n      this._storage.setItem(bookmark.id, bookmark, (item) => {\n        map.fire(\"bookmark:saved\", {\n          data: item,\n        });\n        this._appendItems([item]);\n      });\n      this._showBookmark(bookmark);\n    },\n\n    /**\n     * Update done\n     * @param  {Event} evt\n     */\n    _onBookmarkEdited: function (evt) {\n      const map = this._map;\n      const bookmark = this._cleanBookmark(evt.data);\n      this._storage.setItem(bookmark.id, bookmark, (item) => {\n        map.fire(\"bookmark:saved\", { data: item });\n        const data = this._data;\n        this._data = [];\n        for (var i = 0, len = data.length; i < len; i++) {\n          if (data[i].id === bookmark.id) {\n            data.splice(i, 1, bookmark);\n          }\n        }\n        this._appendItems(data);\n      });\n      this._showBookmark(bookmark);\n    },\n\n    /**\n     * Cleans circular reference for JSON\n     * @param  {Object} bookmark\n     * @return {Object}\n     */\n    _cleanBookmark: function (bookmark) {\n      if (!L.Util.isArray(bookmark.latlng)) {\n        bookmark.latlng = [bookmark.latlng.lat, bookmark.latlng.lng];\n      }\n      return bookmark;\n    },\n\n    /**\n     * Form closed\n     * @param  {Object} evt\n     */\n    _onPopupClosed: function (evt) {\n      this._map.removeLayer(this._marker);\n      this._marker = null;\n      this._popup = null;\n    },\n\n    /**\n     * @param  {String} id\n     * @return {Object|Null}\n     */\n    _getBookmark: function (id) {\n      for (let i = 0, len = this._data.length; i < len; i++) {\n        if (this._data[i].id === id) return this._data[i];\n      }\n      return null;\n    },\n\n    /**\n     * @param  {Object} evt\n     */\n    _onBookmarkShow: function (evt) {\n      this._gotoBookmark(evt.data);\n    },\n\n    /**\n     * Event handler for edit\n     * @param  {Object} evt\n     */\n    _onBookmarkEdit: function (evt) {\n      this._editBookmark(evt.data);\n    },\n\n    /**\n     * Remove bookmark triggered\n     * @param  {Event} evt\n     */\n    _onBookmarkRemove: function (evt) {\n      this._removeBookmark(evt.data);\n    },\n\n    /**\n     * Bookmark options called\n     * @param  {Event} evt\n     */\n    _onBookmarkOptions: function (evt) {\n      this._bookmarkOptions(evt.data);\n    },\n\n    /**\n     * Show menu popup\n     * @param  {Object} bookmark\n     */\n    _bookmarkOptions: function (bookmark) {\n      const coords = L.latLng(bookmark.latlng);\n      const marker = (this._marker = this._createMarker(coords, bookmark));\n      // open form\n      this._popup = new L.Control.Bookmarks.FormPopup(\n        L.Util.extend(this.options.formPopup, {\n          mode: L.Control.Bookmarks.FormPopup.modes.OPTIONS,\n        }),\n        marker,\n        this,\n        bookmark\n      ).addTo(this._map);\n    },\n\n    /**\n     * Call edit popup\n     * @param  {Object} bookmark\n     */\n    _editBookmark: function (bookmark) {\n      const coords = L.latLng(bookmark.latlng);\n      const marker = (this._marker = this._createMarker(coords, bookmark));\n      marker.dragging.enable();\n      // open form\n      this._popup = new L.Control.Bookmarks.FormPopup(\n        L.Util.extend(this.options.formPopup, {\n          mode: L.Control.Bookmarks.FormPopup.modes.UPDATE,\n        }),\n        marker,\n        this,\n        bookmark\n      ).addTo(this._map);\n    },\n\n    /**\n     * Returns a handler that will remove the bookmark from map\n     * in case it got removed from the list\n     * @param  {Object}   bookmark\n     * @param  {L.Marker} marker\n     * @return {Function}\n     */\n    _getOnRemoveHandler: function (bookmark, marker) {\n      return function (evt) {\n        if (evt.data.id === bookmark.id) {\n          marker.clearAllEventListeners();\n          if (marker._popup_) marker._popup_.close();\n          this.removeLayer(marker);\n        }\n      };\n    },\n\n    /**\n     * Creates bookmark marker\n     * @param  {L.LatLng} coords\n     * @param  {Object}   bookmark\n     * @return {L.Marker}\n     */\n    _createMarker: function (coords, bookmark) {\n      const marker = new L.Marker(coords, {\n        icon: this.options.icon || new L.Icon.Default(),\n        riseOnHover: true,\n      }).addTo(this._map);\n      const removeIfRemoved = this._getOnRemoveHandler(bookmark, marker);\n      this._map.on(\"bookmark:removed\", removeIfRemoved, this._map);\n      marker\n        .on(\"popupclose\", () => this._map.removeLayer(this))\n        .on(\"remove\", () => this._map.off(\"bookmark:removed\", removeIfRemoved));\n      return marker;\n    },\n\n    /**\n     * Shows bookmark, nothing else\n     * @param  {Object} bookmark\n     */\n    _showBookmark: function (bookmark) {\n      if (this._marker) this._marker._popup_.close();\n      const coords = L.latLng(bookmark.latlng);\n      const marker = this._createMarker(coords, bookmark);\n      const popup = new L.Control.Bookmarks.FormPopup(\n        L.Util.extend(this.options.formPopup, {\n          mode: L.Control.Bookmarks.FormPopup.modes.SHOW,\n        }),\n        marker,\n        this,\n        bookmark\n      );\n      if (this.options.popupOnShow) popup.addTo(this._map);\n      this._popup = popup;\n      this._marker = marker;\n    },\n\n    /**\n     * @param  {Object} bookmark\n     */\n    _gotoBookmark: function (bookmark) {\n      this._map.setView(bookmark.latlng, bookmark.zoom);\n      this._showBookmark(bookmark);\n    },\n\n    /**\n     * @param  {Object} bookmark\n     */\n    _removeBookmark: function (bookmark) {\n      const remove = (proceed) => {\n        if (!proceed) return this._showBookmark(bookmark);\n\n        this._data.splice(this._data.indexOf(bookmark), 1);\n        this._storage.removeItem(bookmark.id, (bookmark) => {\n          this._onBookmarkRemoved(bookmark);\n        });\n      };\n\n      if (typeof this.options.onRemove === \"function\") {\n        this.options.onRemove(bookmark, remove);\n      } else {\n        remove(true);\n      }\n    },\n\n    /**\n     * @param  {Object} bookmark\n     */\n    _onBookmarkRemoved: function (bookmark) {\n      const li = this._list.querySelector(\n        \".\" +\n          this.options.bookmarkTemplateOptions.itemClass +\n          \"[data-id='\" +\n          bookmark.id +\n          \"']\"\n      );\n\n      this._map.fire(\"bookmark:removed\", { data: bookmark });\n\n      if (li) {\n        L.DomUtil.setOpacity(li, 0);\n        setTimeout(() => {\n          if (li.parentNode) li.parentNode.removeChild(li);\n          if (this._data.length === 0) this._setEmptyListContent();\n        }, 250);\n      }\n    },\n\n    /**\n     * Gets popup content\n     * @param  {Object} bookmark\n     * @return {String}\n     */\n    _getPopupContent: function (bookmark) {\n      if (this.options.getPopupContent) {\n        return this.options.getPopupContent.call(this, bookmark);\n      }\n      return JSON.stringify(bookmark);\n    },\n\n    /**\n     * @param  {Event} e\n     */\n    _onBookmarkClick: function (evt) {\n      const bookmark = this._getBookmarkFromListItem(evt.delegateTarget);\n      if (!bookmark) return;\n      L.DomEvent.stopPropagation(evt);\n\n      // remove button hit\n      if (\n        L.DomUtil.hasClass(\n          evt.target || evt.srcElement,\n          this.options.bookmarkTemplateOptions.removeClass\n        )\n      ) {\n        this._removeBookmark(bookmark);\n      } else {\n        this._map.fire(\"bookmark:show\", { data: bookmark });\n        if (this.options.collapseOnClick) this.collapse();\n      }\n    },\n\n    /**\n     * In case you've decided to play with ids - we've got you covered\n     * @param  {Element} li\n     * @return {Object|Null}\n     */\n    _getBookmarkFromListItem: function (li) {\n      if (this.options.getBookmarkFromListItem) {\n        return this.options.getBookmarkFromListItem.call(this, li);\n      }\n      return this._getBookmark(li.getAttribute(\"data-id\"));\n    },\n\n    /**\n     * GeoJSON feature out of a bookmark\n     * @param  {Object} bookmark\n     * @return {Object}\n     */\n    bookmarkToFeature: function (bookmark) {\n      const coords = this._getBookmarkCoords(bookmark);\n      bookmark = JSON.parse(JSON.stringify(bookmark)); // quick copy\n      delete bookmark.latlng;\n\n      return L.GeoJSON.getFeature(\n        {\n          feature: {\n            type: \"Feature\",\n            id: bookmark.id,\n            properties: bookmark,\n          },\n        },\n        {\n          type: \"Point\",\n          coordinates: coords,\n        }\n      );\n    },\n\n    /**\n     * @param  {Object} bookmark\n     * @return {Array.<Number>}\n     */\n    _getBookmarkCoords: function (bookmark) {\n      if (bookmark.latlng instanceof L.LatLng) {\n        return [bookmark.latlng.lat, bookmark.latlng.lng];\n      }\n      return bookmark.latlng.reverse();\n    },\n\n    /**\n     * Read bookmarks from GeoJSON FeatureCollectio\n     * @param  {Object} geojson\n     * @return {Object}\n     */\n    fromGeoJSON: function (geojson) {\n      const bookmarks = [];\n      for (let i = 0, len = geojson.features.length; i < len; i++) {\n        const bookmark = geojson.features[i];\n        if (!bookmark.properties.divider) {\n          bookmark.properties.latlng = bookmark.geometry.coordinates\n            .concat()\n            .reverse();\n        }\n        bookmarks.push(bookmark.properties);\n      }\n      return bookmarks;\n    },\n\n    /**\n     * @return {Object}\n     */\n    toGeoJSON: function () {\n      return {\n        type: \"FeatureCollection\",\n        features: ((data) => {\n          const result = [];\n          for (let i = 0, len = data.length; i < len; i++) {\n            if (!data[i].divider) {\n              result.push(this.bookmarkToFeature(data[i]));\n            }\n          }\n          return result;\n        })(this._data),\n      };\n    },\n  }\n);\n", "import L from \"leaflet\";\nimport Bookmarks from \"./src/bookmarks\";\n\nL.Control.Bookmarks = Bookmarks;\n\nexport default Bookmarks;\n"], "names": ["substitute", "str", "object", "regexp", "replace", "match", "name", "trim", "indexOf", "char<PERSON>t", "slice", "let", "result", "i", "len", "split", "length", "const", "alpha", "unique", "prefix", "Math", "floor", "random", "Date", "getTime", "toString", "data", "GlobalStorage", "this", "_prefix", "prototype", "getItem", "key", "callback", "setItem", "item", "getAllItems", "items", "hasOwnProperty", "this_prefix", "push", "removeItem", "JSON_RE", "LocalStorage", "_storage", "window", "localStorage", "test", "JSON", "parse", "prefixLength", "substring", "self", "itemStr", "stringify", "EngineType", "Storage", "engineType", "_name", "_engine", "createEngine", "Array", "call", "arguments", "type", "args", "ElementPrototype", "matches", "modes", "CREATE", "UPDATE", "SHOW", "OPTIONS", "FormPopup", "L", "Popup", "extend", "statics", "options", "mode", "className", "templateOptions", "formClass", "inputClass", "inputErrorClass", "idInputClass", "coordsClass", "submitClass", "inputPlaceholder", "removeClass", "editClass", "cancelClass", "editableClass", "removableClass", "menuItemClass", "editMenuText", "removeMenuText", "cancelMenuText", "submitTextCreate", "submitTextEdit", "generateNames", "min<PERSON><PERSON><PERSON>", "generateNamesPrefix", "template", "menuTemplate", "initialize", "source", "control", "bookmark", "_bookmark", "_control", "_latlng", "getLatLng", "_popup_", "_initLayout", "editable", "removable", "menuButton", "_menuButton", "<PERSON><PERSON><PERSON>", "create", "_container", "insertBefore", "_close<PERSON><PERSON>on", "href", "innerHTML", "DomEvent", "disableClickPropagation", "on", "_onMenuButtonClick", "_showMenu", "_map", "fire", "evt", "preventDefault", "close", "_updateContent", "content", "_get<PERSON><PERSON><PERSON><PERSON><PERSON>nt", "submitText", "modeClass", "<PERSON><PERSON>", "coords", "formatCoords", "_source", "getZoom", "join", "_content", "_onRendered", "form", "_contentNode", "querySelector", "_onSubmit", "setTimeout", "_setFocus", "bind", "delegate", "_onEditClick", "_onRemoveClick", "_onCancelClick", "input", "str<PERSON><PERSON><PERSON>", "value", "focus", "setSelectionRange", "_getBookmarkData", "getBookmarkData", "idInput", "latlng", "zoom", "id", "stop", "classList", "remove", "validate", "validateInput", "map", "add", "lat", "toFixed", "lng", "onAdd", "_onSourceMoved", "_onSourceMoveStart", "onRemove", "off", "style", "display", "e", "openPopup", "update", "matchesSelector", "Element", "webkitMatchesSelector", "mozMatchesSelector", "msMatchesSelector", "oMatchesSelector", "selector", "nodes", "parentNode", "document", "querySelectorAll", "element", "el", "fn", "target", "srcElement", "<PERSON><PERSON><PERSON><PERSON>", "checkSelf", "root", "closest", "propagationStopped", "_template", "Bookmarks", "Control", "storage", "position", "containerClass", "expandedClass", "headerClass", "listClass", "iconClass", "iconWrapperClass", "listWrapperClass", "listWrapperClassAdd", "wrapperClass", "addBookmarkButtonCss", "animateClass", "animateDuration", "formPopup", "popupClass", "bookmarkTemplate", "emptyTemplate", "dividerTemplate", "bookmarkTemplateOptions", "itemClass", "nameClass", "emptyClass", "defaultBookmarkOptions", "title", "emptyMessage", "addBookmarkMessage", "collapseOnClick", "scrollOnAdd", "scrollDuration", "popupOnShow", "addNewOption", "popupTemplate", "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_data", "_list", "_marker", "_addButton", "_icon", "_isCollapsed", "setOptions", "container", "disableScrollPropagation", "_createList", "bookmarks", "append<PERSON><PERSON><PERSON>", "_listwrapper", "_onClick", "stopPropagation", "_onBookmarkAddStart", "_onBookmarkAdd", "_onBookmarkEdited", "_onBookmarkShow", "_onBookmarkEdit", "_onBookmarkOptions", "_onBookmarkRemove", "_onAddButtonPressed", "_popup", "getData", "_filterBookmarksOutput", "_onBookmarkClick", "_setEmptyList<PERSON>ontent", "isArray", "_appendItems", "size", "getSize", "maxHeight", "min", "y", "addClass", "addButton", "collapse", "getCenter", "_filterBookmarks", "filterBookmarks", "filterBookmarksOutput", "html", "wasEmpty", "concat", "_renderBookmarkItem", "_scrollToLast", "listwrapper", "pos", "scrollTop", "targetVal", "<PERSON><PERSON><PERSON><PERSON>", "offsetTop", "step", "requestAnimFrame", "scroll", "timestamp", "divider", "_getBookmarkDataForTemplate", "getBookmarkDataForTemplate", "formatName", "expand", "expanded", "hasClass", "<PERSON><PERSON>", "icon", "Icon", "<PERSON><PERSON><PERSON>", "draggable", "riseOnHover", "addTo", "_onPopupClosed", "_cleanBookmark", "_showBookmark", "splice", "<PERSON><PERSON><PERSON>er", "_getBookmark", "_gotoBookmark", "_editBookmark", "_removeBookmark", "_bookmarkOptions", "latLng", "marker", "_createMarker", "dragging", "enable", "_getOnRemoveHandler", "clearAllEventListeners", "removeIfRemoved", "popup", "<PERSON><PERSON><PERSON><PERSON>", "proceed", "_onBookmarkRemoved", "li", "setOpacity", "<PERSON><PERSON><PERSON><PERSON>", "_getBookmarkFromListItem", "getBookmarkFromListItem", "getAttribute", "bookmarkToFeature", "_getBookmarkCoords", "GeoJSON", "getFeature", "feature", "properties", "coordinates", "LatLng", "reverse", "fromGeoJSON", "g<PERSON><PERSON><PERSON>", "features", "geometry", "toGeoJSON"], "mappings": ";;;;;;;;mZAQO,SAASA,EAAWC,EAAKC,EAAQC,GACtC,OAAOF,EAAIG,QAAQD,GAAU,mBAAmB,SAAUE,EAAOC,GAG/D,IAA2B,KAF3BA,EAqCG,SAAcL,GACnB,OAAOA,EAAIG,QAAQ,aAAc,IAtCxBG,CAAKD,IAEHE,QAAQ,KACf,MAAuB,MAAnBH,EAAMI,OAAO,GAAmBJ,EAAMK,MAAM,GACzB,MAAhBR,EAAOI,GAAgBJ,EAAOI,GAAQ,GAK7C,IAFAK,IAAIC,EAASV,EAEJW,EAAI,EAAGC,GADhBR,EAAOA,EAAKS,MAAM,MACSC,OAAQH,EAAIC,EAAKD,IAAK,CAC/C,KAAIP,EAAKO,KAAMD,GACV,MAAO,GADWA,EAASA,EAAON,EAAKO,IAG9C,OAAOD,KAKbK,IAAMC,EAAQ,6BAOP,SAASC,EAAOC,GACrB,OACGA,GAAUF,EAAMG,KAAKC,MAAMD,KAAKE,SAAWL,EAAMF,WAClD,IAAIQ,MAAOC,UAAUC,SAAS,ICnClCT,IAAMU,EAAO,GAOQC,EACnB,SAAYR,GAIVS,KAAKC,QAAUV,GAOjBQ,EAAAG,UAAAC,QAAA,SAAQC,EAAKC,GACXA,EAASP,EAAKE,KAAKC,QAAUG,KAQ/BL,EAAAG,UAAAI,QAAA,SAAQF,EAAKG,EAAMF,GACjBP,EAAKE,KAAKC,QAAUG,GAAOG,EAC3BF,EAASE,gBAMXC,YAAW,SAACH,GACVjB,IAAMqB,EAAQ,GACd,IAAKrB,IAAMgB,KAAON,EACZA,EAAKY,eAAeN,IAAqC,IAA7BA,EAAIzB,QAAQgC,cAC1CF,EAAMG,KAAKd,EAAKM,IAGpBC,EAASI,IAOXV,EAAAG,UAAAW,WAAA,SAAWT,EAAKC,cACdL,KAAKG,QAAQC,GAAK,SAACG,GACbA,SACKT,EAAKE,EAAKC,QAAUG,GAE3BG,EAAO,KAELF,GAAUA,EAASE,OCxD7BnB,IAAM0B,EAAU,qBAKKC,EACnB,SAAYxB,GAIVS,KAAKC,QAAUV,EAKfS,KAAKgB,SAAWC,OAAOC,cAOzBH,EAAAb,UAAAC,QAAA,SAAQC,EAAKC,GACXvB,IAAIyB,EAAOP,KAAKgB,SAASb,QAAQH,KAAKC,QAAUG,GAC5CG,GAAQO,EAAQK,KAAKZ,KACvBA,EAAOa,KAAKC,MAAMd,IAEpBF,EAASE,gBAMXC,YAAW,SAACH,GACVjB,IAAMqB,EAAQ,GACRa,EAAetB,KAAKC,QAAQd,OAClC,IAAKC,IAAMgB,KAAOJ,KAAKgB,SAEY,OAA/BhB,KAAKgB,SAASb,QAAQC,IACQ,IAA9BA,EAAIzB,QAAQqB,KAAKC,UAEjBD,KAAKG,QAAQC,EAAImB,UAAUD,aAAgBf,UAASE,EAAMG,KAAKL,MAGnEF,EAASI,IAOXM,EAAAb,UAAAW,WAAA,SAAWT,EAAKC,cACRmB,EAAOxB,KACbA,KAAKG,QAAQC,GAAK,SAACG,GACjBP,EAAKgB,SAASH,WAAWW,EAAKvB,QAAUG,GACpCC,GAAUA,EAASE,OAS3BQ,EAAAb,UAAAI,QAAA,SAAQF,EAAKG,EAAMF,GACjBvB,IAAI2C,EAAUlB,EAAKV,WACH,oBAAZ4B,IACFA,EAAUL,KAAKM,UAAUnB,IAE3BP,KAAKgB,SAASV,QAAQN,KAAKC,QAAUG,EAAKqB,GAC1CpB,EAASE,IChENnB,IAAMuC,EAEI,EAFJA,EAGG,EAOKC,EACnB,SAAAA,EAAYnD,EAAMoD,GACI,iBAATpD,IACToD,EAAapD,EACbA,EAAOa,KAMTU,KAAK8B,MAAQrD,EAKbuB,KAAK+B,QAAUH,EAAQI,aACrBH,EACA7B,KAAK8B,MACLG,MAAM/B,UAAUrB,MAAMqD,KAAKC,UAAW,KAU1CP,EAAOI,sBAAaI,EAAM7C,EAAQ8C,GAChC,OAAID,IAAST,EACJ,IAAI5B,EAAcR,GAEvB6C,IAAST,EACJ,IAAIZ,EAAaxB,QAD1B,GAUFqC,EAAA1B,UAAAI,QAAA,SAAQF,EAAKG,EAAMF,GAEjB,OADAL,KAAK+B,QAAQzB,QAAQF,EAAKG,EAAMF,GACzBL,MAOT4B,EAAA1B,UAAAC,QAAA,SAAQC,EAAKC,GAEX,OADAL,KAAK+B,QAAQ5B,QAAQC,EAAKC,GACnBL,kBAMTQ,YAAW,SAACH,GACVL,KAAK+B,QAAQvB,YAAYH,IAO3BuB,EAAA1B,UAAAW,WAAA,SAAWT,EAAKC,GACdL,KAAK+B,QAAQlB,WAAWT,EAAKC,ICrFjCjB,ICE0BkD,EAClBC,EDHFC,EAAQ,CACZC,OAAQ,EACRC,OAAQ,EACRC,KAAM,EACNC,QAAS,GASXC,EAAeC,EAAC,QAACC,MAAMC,OACa,CAChCC,QAAS,CAAET,MAAAA,GAKXU,QAAS,CACPC,KAAMX,EAAMC,OACZW,UAAW,+BACXC,gBAAiB,CACfC,UAAW,yBACXC,WAAY,+BACZC,gBAAiB,YACjBC,aAAc,4BACdC,YAAa,gCACbC,YAAa,gCACbC,iBAAkB,gBAClBC,YAAa,gCACbC,UAAW,8BACXC,YAAa,gCACbC,cAAe,WACfC,eAAgB,YAChBC,cAAe,WACfC,aAAc,OACdC,eAAgB,SAChBC,eAAgB,SAChBC,iBAAkB,IAClBC,eAAgB,wCAElBC,eAAe,EACfC,SAAU,IACVC,oBAAqB,YACrBC,SACE,wZAQFC,aACE,6UAcJC,WAAY,SAAU3B,EAAS4B,EAAQC,EAASC,GAI9ChF,KAAKiF,UAAYD,EAKjBhF,KAAKkF,SAAWH,EAKhB/E,KAAKmF,QAAUL,EAAOM,YAMtBN,EAAOO,QAAUrF,KAEjB8C,UAAEC,MAAM7C,UAAU2E,WAAW3C,KAAKlC,KAAMkD,EAAS4B,IAMnDQ,YAAa,WAGX,GAFAxC,EAAC,QAACC,MAAM7C,UAAUoF,YAAYpD,KAAKlC,MAGjCA,KAAKkD,QAAQC,OAASX,EAAMG,OAC3B3C,KAAKiF,UAAUM,UAAYvF,KAAKiF,UAAUO,WAC3C,CACApG,IAAMqG,EAAczF,KAAK0F,YAAc5C,EAAC,QAAC6C,QAAQC,OAC/C,IACA,6BAEF5F,KAAK6F,WAAWC,aAAaL,EAAYzF,KAAK+F,cAC9CN,EAAWO,KAAO,QAClBP,EAAWQ,UAAY,kCACvBnD,EAAAA,QAAEoD,SAASC,wBAAwBV,GACnC3C,UAAEoD,SAASE,GAAGX,EAAY,QAASzF,KAAKqG,mBAAoBrG,QAOhEsG,UAAW,WACTtG,KAAKuG,KAAKC,KAAK,mBAAoB,CAAE1G,KAAME,KAAKiF,aAMlDoB,mBAAoB,SAAUI,GAC5B3D,EAAAA,QAAEoD,SAASQ,eAAeD,GAC1BzG,KAAKsG,YACLtG,KAAK2G,SAOPC,eAAgB,WACd9H,IAAI+H,EACJ,GAAI7G,KAAKkD,QAAQC,OAASX,EAAMG,KAC9BkE,EAAU7G,KAAKkF,SAAS4B,iBAAiB9G,KAAKiF,eACzC,CACLnG,IAAI6F,EAAW3E,KAAKkD,QAAQyB,SACxBoC,EAAa/G,KAAKkD,QAAQG,gBAAgBiB,iBAC1CtE,KAAKkD,QAAQC,OAASX,EAAMI,UAC9B+B,EAAW3E,KAAKkD,QAAQ0B,cAEtB5E,KAAKkD,QAAQC,OAASX,EAAME,SAC9BqE,EAAa/G,KAAKkD,QAAQG,gBAAgBkB,gBAE5CnF,IAAM4H,EAAY,GACdhH,KAAKiF,UAAUM,UACjByB,EAAUpG,KAAKZ,KAAKkD,QAAQG,gBAAgBW,eAE1ChE,KAAKiF,UAAUO,WACjBwB,EAAUpG,KAAKZ,KAAKkD,QAAQG,gBAAgBY,gBAE9C4C,EAAU1I,EACRwG,EACA7B,EAAC,QAACmE,KAAKjE,OACL,GACAhD,KAAKiF,WAAa,GAClBjF,KAAKkD,QAAQG,gBACb,CACE0D,WAAYA,EACZG,OAAQlH,KAAKmH,aACXnH,KAAKoH,QAAQhC,YACbpF,KAAKuG,KAAKc,WAEZlE,KAAM6D,EAAUM,KAAK,QAK7BtH,KAAKuH,SAAWV,EAChB/D,EAAC,QAACC,MAAM7C,UAAU0G,eAAe1E,KAAKlC,MACtCA,KAAKwH,eAMPA,YAAa,WACX,GACExH,KAAKkD,QAAQC,OAASX,EAAMC,QAC5BzC,KAAKkD,QAAQC,OAASX,EAAME,OAC5B,CACAtD,IAAMqI,EAAOzH,KAAK0H,aAAaC,cAC7B,IAAM3H,KAAKkD,QAAQG,gBAAgBC,WAEvBmE,EAAKE,cACjB,IAAM3H,KAAKkD,QAAQG,gBAAgBE,YAGrCT,UAAEoD,SAASE,GAAGqB,EAAM,SAAUzH,KAAK4H,UAAW5H,MAC9C6H,WAAW7H,KAAK8H,UAAUC,KAAK/H,MAAO,UAC7BA,KAAKkD,QAAQC,OAASX,EAAMI,UACrCE,EAAC,QAACoD,SAAS8B,SACThI,KAAK6F,WACL,IAAM7F,KAAKkD,QAAQG,gBAAgBS,UACnC,QACA9D,KAAKiI,aACLjI,MAEF8C,EAAC,QAACoD,SAAS8B,SACThI,KAAK6F,WACL,IAAM7F,KAAKkD,QAAQG,gBAAgBQ,YACnC,QACA7D,KAAKkI,eACLlI,MAEF8C,EAAC,QAACoD,SAAS8B,SACThI,KAAK6F,WACL,IAAM7F,KAAKkD,QAAQG,gBAAgBU,YACnC,QACA/D,KAAKmI,eACLnI,QAQN8H,UAAW,WACT1I,IAAMgJ,EAAQpI,KAAK0H,aAAaC,cAC9B,IAAM3H,KAAKkD,QAAQG,gBAAgBE,YAI/B8E,EAAiC,EAArBD,EAAME,MAAMnJ,OAC9BiJ,EAAMG,QACNH,EAAMI,kBAAkBH,EAAWA,IAOrCJ,aAAc,SAAUxB,GACtB3D,EAAAA,QAAEoD,SAASQ,eAAeD,GAC1BzG,KAAKuG,KAAKC,KAAK,gBAAiB,CAAE1G,KAAME,KAAKiF,YAC7CjF,KAAK2G,SAOPuB,eAAgB,SAAUzB,GACxB3D,EAAAA,QAAEoD,SAASQ,eAAeD,GAC1BzG,KAAKuG,KAAKC,KAAK,kBAAmB,CAAE1G,KAAME,KAAKiF,YAC/CjF,KAAK2G,SAOPwB,eAAgB,SAAU1B,GACxB3D,EAAAA,QAAEoD,SAASQ,eAAeD,GAC1BzG,KAAKuG,KAAKC,KAAK,gBAAiB,CAAE1G,KAAME,KAAKiF,YAC7CjF,KAAK2G,SAOP8B,iBAAkB,WAChBrJ,IAAM8D,EAAUlD,KAAKkD,QACrB,GAAIA,EAAQwF,gBACV,OAAOxF,EAAQwF,gBAAgBxG,KAAKlC,MAEtCZ,IAAMgJ,EAAQpI,KAAK0H,aAAaC,cAC9B,IAAMzE,EAAQG,gBAAgBE,YAE1BoF,EAAU3I,KAAK0H,aAAaC,cAChC,IAAMzE,EAAQG,gBAAgBI,cAEhC,MAAO,CACLmF,OAAQ5I,KAAKoH,QAAQhC,YACrByD,KAAM7I,KAAKuG,KAAKc,UAChB5I,KAAM2J,EAAME,MACZQ,GAAIH,EAAQL,OAAShJ,MAQzBsI,UAAW,SAAUnB,GACnB3D,EAAAA,QAAEoD,SAAS6C,KAAKtC,GAEhBrH,IAAMgJ,EAAQpI,KAAK0H,aAAaC,cAC9B,IAAM3H,KAAKkD,QAAQG,gBAAgBE,YAErC6E,EAAMY,UAAUC,OAAOjJ,KAAKkD,QAAQG,gBAAgBG,iBAEhC,KAAhB4E,EAAME,OAAgBtI,KAAKkD,QAAQsB,gBACrC4D,EAAME,MAAQhJ,EAAOU,KAAKkD,QAAQwB,sBAGpCtF,IAAM8J,EAAWlJ,KAAKkD,QAAQiG,eAAiB,WAAO,OAAA,GAEtD,GAAoB,KAAhBf,EAAME,OAAgBY,EAAShH,KAAKlC,KAAMoI,EAAME,OAAQ,CAC1DlJ,IAAM4F,EAAWlC,UAAEmE,KAAKjE,OACtB,GACAhD,KAAKiF,UACLjF,KAAKyI,oBAEDW,EAAMpJ,KAAKuG,KAEjBvG,KAAK2G,QACD3G,KAAKkD,QAAQC,OAASX,EAAMC,OAC9B2G,EAAI5C,KAAK,eAAgB,CAAE1G,KAAMkF,IAEjCoE,EAAI5C,KAAK,kBAAmB,CAAE1G,KAAMkF,SAGtCoD,EAAMY,UAAUK,IAAIrJ,KAAKkD,QAAQG,gBAAgBG,kBASrD2D,aAAc,SAAUD,EAAQ2B,GAC9B,OAAI7I,KAAKkD,QAAQiE,aACRnH,KAAKkD,QAAQiE,aAAajF,KAAKlC,KAAMkH,EAAQ2B,GAE/C,CAAC3B,EAAOoC,IAAIC,QAAQ,GAAIrC,EAAOsC,IAAID,QAAQ,GAAIV,GAAMvB,KAC1D,YASJmC,MAAO,SAAUL,GAGf,OAFApJ,KAAKoH,QAAQhB,GAAG,UAAWpG,KAAK0J,eAAgB1J,MAChDA,KAAKoH,QAAQhB,GAAG,YAAapG,KAAK2J,mBAAoB3J,MAC/C8C,EAAAA,QAAEC,MAAM7C,UAAUuJ,MAAMvH,KAAKlC,KAAMoJ,IAM5CQ,SAAU,SAAUR,GAClBpJ,KAAKoH,QAAQyC,IAAI,UAAW7J,KAAK0J,eAAgB1J,MACjD8C,EAAC,QAACC,MAAM7C,UAAU0J,SAAS1H,KAAKlC,KAAMoJ,IAMxCO,mBAAoB,WAElB3J,KAAKiF,UAAYnC,UAAEmE,KAAKjE,OACtBhD,KAAKiF,WAAa,GAClBjF,KAAKyI,oBAEPzI,KAAK6F,WAAWiE,MAAMC,QAAU,QAOlCL,eAAgB,SAAUM,GACxBhK,KAAKmF,QAAUnF,KAAKoH,QAAQhC,YAC5BpF,KAAK6F,WAAWiE,MAAMC,QAAU,GAChC/J,KAAKoH,QAAQ6C,YACbjK,KAAKkK,YCxXLC,GAAoB7H,EA2BvB8H,QAAQlK,UA1BHqC,EACJD,EAAiBC,SACjBD,EAAiB+H,uBACjB/H,EAAiBgI,oBACjBhI,EAAiBiI,mBACjBjI,EAAiBkI,kBAEjB,SAAUC,GAKR,IAJA,IAEEC,GAFS1K,KACK2K,YADL3K,KACwB4K,UAClBC,iBAAiBJ,GAEzBzL,EAAI,EAAGC,EAAMyL,EAAMvL,OAAQH,EAAIC,IAAOD,EAC7C,GAAI0L,EAAM1L,IALDgB,KAKa,OAAO,EAE/B,OAAO,GAQJ,SAAU8K,EAASL,GACxB,OAAOlI,EAAQL,KAAK4I,EAASL,KA4CjC3H,EAAAA,QAAEoD,SAAS8B,SAAW,SAAU+C,EAAIN,EAAUrI,EAAM4I,EAAIjD,GACtD,OAAOjF,EAAAA,QAAEoD,SAASE,GAAG2E,EAAI3I,GAAM,SAACqE,GAC9BrH,IAAM6L,EAASxE,EAAIwE,QAAUxE,EAAIyE,WACjCzE,EAAI0E,eAjCR,SAAiBL,EAASL,EAAUW,EAAWC,GAW7C,IAVAP,EAAUM,EACN,CACET,WAAYG,GAEdA,EAEJO,EAAOA,GAAQT,UAIPE,EAAUA,EAAQH,aAAeG,IAAYF,UAAU,CAC7D,GAAIT,EAAgBW,EAASL,GAAW,OAAOK,EAI/C,GAAIA,IAAYO,EAAM,OAAO,MAiBRC,CAAQL,EAAQR,GAAU,EAAMM,GACjDtE,EAAI0E,iBAAmB1E,EAAI8E,oBAC7BP,EAAG9I,KAAK6F,GAAQgD,EAAItE,OCxE1B3D,EAAAA,QAAEmE,KAAKuE,UAAY1I,EAAAA,QAAEmE,KAAKuE,WAAarN,EAOvC,IAAAsN,EAAe3I,EAAC,QAAC4I,QAAQ1I,OACY,CACjCC,QAAS,CACbrB,QAAMA,EACNiB,UAAMA,GAMFK,QAAS,CACPhC,cAAc,EAWdyK,QAAS,KACTlN,KAAM,oBACNmN,SAAU,WAEVC,eAAgB,wCAChBC,cAAe,WACfC,YAAa,mBACbC,UAAW,iBACXC,UAAW,iBACXC,iBAAkB,yBAClBC,iBAAkB,yBAClBC,oBAAqB,mBACrBC,aAAc,sBACdC,qBAAsB,sBAEtBC,aAAc,sBACdC,gBAAiB,IAEjBC,UAAW,CACTC,WAAY,mBAGdC,iBACE,sNAMFC,cACE,4EAGFC,gBAAiB,4BAEjBC,wBAAyB,CACvBC,UAAW,gBACXC,UAAW,gBACXtJ,YAAa,kBACbG,YAAa,kBACboJ,WAAY,mBAGdC,uBAAwB,CACtB3H,UAAU,EACVC,WAAW,GAGb2H,MAAO,YACPC,aAAc,mBACdC,mBAAoB,mBACpBC,iBAAiB,EACjBC,aAAa,EACbC,eAAgB,IAChBC,aAAa,EACbC,cAAc,EAQdC,cACE,gEAWFC,gBAAiB,SAAU5I,GACzB,OAAO7G,EAAW6B,KAAKkD,QAAQyK,cAAe,CAC5C/E,OAAQ5I,KAAKmH,aAAanC,EAAS4D,QACnCnK,KAAMuG,EAASvG,KACfoK,KAAM7I,KAAKuG,KAAKc,cAStBxC,WAAY,SAAU3B,GACpBA,EAAUA,GAAW,GAMrBlD,KAAK6N,MAAQ,GAKb7N,KAAK8N,MAAQ,KAKb9N,KAAK+N,QAAU,KAKf/N,KAAKgO,WAAa,KAKlBhO,KAAKiO,MAAQ,KAKbjO,KAAKkO,cAAe,EAEpBpL,EAAAA,QAAEmE,KAAKkH,WAAWnO,KAAMkD,GAKxBlD,KAAKgB,SACHkC,EAAQyI,UACP3L,KAAKkD,QAAQhC,aACV,IAAIU,EAAQ5B,KAAKkD,QAAQzE,KAAMkD,GAC/B,IAAIC,EAAQ5B,KAAKkD,QAAQzE,KAAMkD,IAErCmB,UAAE4I,QAAQxL,UAAU2E,WAAW3C,KAAKlC,KAAMA,KAAKkD,UAMjDuG,MAAO,SAAUL,GACfhK,IAAMgP,EAAapO,KAAK6F,WAAa/C,EAAC,QAAC6C,QAAQC,OAC7C,MACA5F,KAAKkD,QAAQ2I,gBA8Cf,OA3CA/I,EAAAA,QAAEoD,SAASC,wBAAwBiI,GAAWC,yBAC5CD,GAEFA,EAAUnI,UACR,eACAjG,KAAKkD,QAAQ6I,YACb,kBACA/L,KAAKkD,QAAQgJ,iBAHb,kBAMAlM,KAAKkD,QAAQ+I,UACb,mBAEFjM,KAAKiO,MAAQG,EAAUzG,cAAc,IAAM3H,KAAKkD,QAAQ+I,WACxDjM,KAAKiO,MAAMd,MAAQnN,KAAKkD,QAAQiK,MAEhCnN,KAAKsO,YAAYtO,KAAKkD,QAAQqL,WAEdzL,UAAE6C,QAAQC,OACxB,MACA5F,KAAKkD,QAAQmJ,aACbrM,KAAK6F,YAEC2I,YAAYxO,KAAKyO,cAEzBzO,KAAKsF,cAELxC,UAAEoD,SAASE,GAAGgI,EAAW,QAASpO,KAAK0O,SAAU1O,MAAMoG,GACrDgI,EACA,cACAtL,EAAC,QAACoD,SAASyI,iBAGbvF,EACGhD,GAAG,eAAgBpG,KAAK4O,oBAAqB5O,MAC7CoG,GAAG,eAAgBpG,KAAK6O,eAAgB7O,MACxCoG,GAAG,kBAAmBpG,KAAK8O,kBAAmB9O,MAC9CoG,GAAG,gBAAiBpG,KAAK+O,gBAAiB/O,MAC1CoG,GAAG,gBAAiBpG,KAAKgP,gBAAiBhP,MAC1CoG,GAAG,mBAAoBpG,KAAKiP,mBAAoBjP,MAChDoG,GAAG,kBAAmBpG,KAAKkP,kBAAmBlP,MAC9CoG,GAAG,SAAUpG,KAAKsF,YAAatF,MAE3BoO,GAMTxE,SAAU,SAAUR,GAClBA,EACGS,IAAI,eAAgB7J,KAAK4O,oBAAqB5O,MAC9C6J,IAAI,eAAgB7J,KAAK6O,eAAgB7O,MACzC6J,IAAI,kBAAmB7J,KAAK8O,kBAAmB9O,MAC/C6J,IAAI,gBAAiB7J,KAAK+O,gBAAiB/O,MAC3C6J,IAAI,gBAAiB7J,KAAKgP,gBAAiBhP,MAC3C6J,IAAI,mBAAoB7J,KAAKiP,mBAAoBjP,MACjD6J,IAAI,kBAAmB7J,KAAKkP,kBAAmBlP,MAC/C6J,IAAI,SAAU7J,KAAKsF,YAAatF,MAE/BA,KAAK+N,SAAS/N,KAAK+N,QAAQ1I,QAAQsB,QAEnC3G,KAAKkD,QAAQwK,cACf5K,EAAC,QAACoD,SAAS2D,IACT7J,KAAK6F,WAAW8B,cACd,IAAM3H,KAAKkD,QAAQoJ,sBAErB,QACAtM,KAAKmP,oBACLnP,MAIJA,KAAK+N,QAAU,KACf/N,KAAKoP,OAAS,KACdpP,KAAK6F,WAAa,MAMpBwJ,QAAS,WACP,OAAOrP,KAAKsP,uBAAuBtP,KAAK6N,QAM1CS,YAAa,SAAUC,cACrBvO,KAAKyO,aAAe3L,UAAE6C,QAAQC,OAC5B,MACA5F,KAAKkD,QAAQiJ,iBACbnM,KAAK6F,YAEP7F,KAAK8N,MAAQhL,UAAE6C,QAAQC,OACrB,KACA5F,KAAKkD,QAAQ8I,UACbhM,KAAKyO,cAIP3L,EAAC,QAACoD,SAAS8B,SACThI,KAAK8N,MACL,IAAM9N,KAAKkD,QAAQ4J,wBAAwBC,UAC3C,QACA/M,KAAKuP,iBACLvP,MAGFA,KAAKwP,uBAED1M,UAAEmE,KAAKwI,QAAQlB,GACjBvO,KAAK0P,aAAanB,GACY,mBAAdA,EAChBvO,KAAK0P,aAAanB,KAElBvO,KAAKgB,SAASR,sBAAa+N,GAAcvO,OAAAA,EAAK0P,aAAanB,OAO/DiB,qBAAsB,WACpBxP,KAAK8N,MAAM7H,UAAY9H,EACrB6B,KAAKkD,QAAQ0J,cACb9J,EAAAA,QAAEmE,KAAKjE,OAAOhD,KAAKkD,QAAQ4J,wBAAyB,CAClDhN,KAAM,CACJsN,aAAcpN,KAAKkD,QAAQkK,kBASnC9H,YAAa,WACXlG,IAAMuQ,EAAO3P,KAAKuG,KAAKqJ,UAOvB,GANA5P,KAAKyO,aAAa3E,MAAM+F,UACtBrQ,KAAKsQ,IAAa,GAATH,EAAKI,EAASJ,EAAKI,EAAI,KAAO,KAEX,YAA1B/P,KAAKkD,QAAQ0I,UACf9I,EAAC,QAAC6C,QAAQqK,SAAShQ,KAAK6F,WAAY,8BAElC7F,KAAKkD,QAAQwK,aAAc,CAC7BtO,IAAM6Q,EAAYnN,UAAE6C,QAAQC,OAC1B,MACA5F,KAAKkD,QAAQoJ,sBAES,OAApBtM,KAAKgO,aACPhO,KAAKyO,aAAa9D,WAAW6D,YAAYyB,GACzCjQ,KAAKgO,WAAaiC,EAClBjQ,KAAKyO,aAAa9D,WAAW3B,UAAUK,IACrCrJ,KAAKkD,QAAQkJ,qBAEf6D,EAAUhK,UACR,oDAEAjG,KAAKkD,QAAQmK,mBACb,UACFvK,UAAEoD,SAASE,GAAG6J,EAAW,QAASjQ,KAAKmP,oBAAqBnP,SAQlEmP,oBAAqB,SAAU1I,GAC7B3D,EAAAA,QAAEoD,SAAS6C,KAAKtC,GAChBzG,KAAKkQ,WACLlQ,KAAKuG,KAAKC,KAAK,eAAgB,CAC7BoC,OAAQ5I,KAAKuG,KAAK4J,eAWtBC,iBAAkB,SAAU7B,GAC1B,OAAIvO,KAAKkD,QAAQmN,gBACRrQ,KAAKkD,QAAQmN,gBAAgBnO,KAAKlC,KAAMuO,GAE1CA,GASTe,uBAAwB,SAAUf,GAChC,OAAIvO,KAAKkD,QAAQoN,sBACRtQ,KAAKkD,QAAQoN,sBAAsBpO,KAAKlC,KAAMuO,GAEhDA,GAOTmB,aAAc,SAAUnB,GACtBzP,IAAIyR,EAAO,GACPC,EAAiC,IAAtBxQ,KAAK6N,MAAM1O,OAI1BoP,EAAYvO,KAAKoQ,iBAAiB7B,GAGlCvO,KAAK6N,MAAQ7N,KAAK6N,MAAM4C,OAAOlC,GAE/B,IAAKzP,IAAIE,EAAI,EAAGC,EAAMsP,EAAUpP,OAAQH,EAAIC,EAAKD,IAC/CuR,GAAQvQ,KAAK0Q,oBAAoBnC,EAAUvP,IAY7C,GATa,KAATuR,IAEEC,EACFxQ,KAAK8N,MAAM7H,UAAYsK,EAEvBvQ,KAAK8N,MAAM7H,WAAasK,GAIxBvQ,KAAKkO,aAAc,CACrB9O,IAAMgP,EAAYpO,KAAK6F,WACjBzC,EAAYpD,KAAKkD,QAAQqJ,aAC/B6B,EAAUpF,UAAUK,IAAIjG,GACxBnC,OAAO4G,YAAW,WAChBuG,EAAUpF,UAAUC,OAAO7F,KAC1BpD,KAAKkD,QAAQsJ,sBAEhBxM,KAAK2Q,iBAOTA,cAAe,WACbvR,IAAMwR,EAAc5Q,KAAKyO,aACrBoC,EAAM7Q,KAAKyO,aAAaqC,UACtBC,EAAY/Q,KAAK8N,MAAMkD,UAAUC,UAGjCC,GACHH,EAAYF,IAAQ7Q,KAAKkD,QAAQsK,qBAYpC1K,EAAAA,QAAEmE,KAAKkK,kBAVP,SAASC,EAAOC,GAIdR,EAAMrR,KAAKsQ,IAAIe,EAAMK,EAAMH,GAC3BH,EAAYE,UAAYD,EACpBA,IAAQE,GACVjO,EAAAA,QAAEmE,KAAKkK,iBAAiBC,OAW9BV,oBAAqB,SAAU1L,GAC7B,OAAIA,EAASsM,QACJnT,EAAW6B,KAAKkD,QAAQ2J,gBAAiB7H,IAGlDhF,KAAKkD,QAAQ4J,wBAAwBhN,KACnCE,KAAKuR,4BAA4BvM,GAE5B7G,EACL6B,KAAKkD,QAAQyJ,iBACb3M,KAAKkD,QAAQ4J,2BASjByE,4BAA6B,SAAUvM,GACrC,OAAIhF,KAAKkD,QAAQsO,2BACRxR,KAAKkD,QAAQsO,2BAA2BtP,KAAKlC,KAAMgF,GAErD,CACLkC,OAAQlH,KAAKmH,aAAanC,EAAS4D,QACnCnK,KAAMuB,KAAKyR,WAAWzM,EAASvG,MAC/BoK,KAAM7D,EAAS6D,KACfC,GAAI9D,EAAS8D,KAQjB3B,aAAc,SAAUyB,GACtB,OAAI5I,KAAKkD,QAAQiE,aACRnH,KAAKkD,QAAQiE,aAAajF,KAAKlC,KAAM4I,GAEvCA,EAAO,GAAGW,QAAQ,GAAK,UAAYX,EAAO,GAAGW,QAAQ,IAO9DkI,WAAY,SAAUhT,GACpB,OAAIuB,KAAKkD,QAAQuO,WACRzR,KAAKkD,QAAQuO,WAAWvP,KAAKlC,KAAMvB,GAErCA,GAMTiT,OAAQ,WACN5O,UAAE6C,QAAQqK,SAAShQ,KAAK6F,WAAY7F,KAAKkD,QAAQ4I,eACjD9L,KAAKkO,cAAe,GAMtBgC,SAAU,WACRpN,UAAE6C,QAAQ9B,YAAY7D,KAAK6F,WAAY7F,KAAKkD,QAAQ4I,eACpD9L,KAAKkO,cAAe,GAMtBQ,SAAU,SAAUjI,GAClBrH,IAAMuS,EAAW7O,UAAE6C,QAAQiM,SACzB5R,KAAK6F,WACL7F,KAAKkD,QAAQ4I,eAEXb,EAASxE,EAAIwE,QAAUxE,EAAIyE,WAE/B,GAAIyG,EAAU,CACZ,GAAI1G,IAAWjL,KAAK6F,WAClB,OAAO7F,KAAKkQ,WAGd,KAAOjF,IAAWjL,KAAK6F,YAAY,CACjC,GACE/C,EAAC,QAAC6C,QAAQiM,SAAS3G,EAAQjL,KAAKkD,QAAQ6I,cACxCjJ,EAAC,QAAC6C,QAAQiM,SAAS3G,EAAQjL,KAAKkD,QAAQiJ,kBACxC,CACAnM,KAAKkQ,WACL,MAEFjF,EAASA,EAAON,iBAEb3K,KAAK0R,UAMd9C,oBAAqB,SAAUnI,GACzBzG,KAAK+N,SAAS/N,KAAKoP,OAAOzI,QAE9B3G,KAAK+N,QAAU,IAAIjL,EAAAA,QAAE+O,OAAOpL,EAAImC,OAAQ,CACtCkJ,KAAM9R,KAAKkD,QAAQ4O,MAAQ,IAAIhP,EAAC,QAACiP,KAAKC,QACtCC,WAAW,EACXC,aAAa,IACZC,MAAMnS,KAAKuG,MACdvG,KAAK+N,QAAQ3H,GAAG,aAAcpG,KAAKoS,eAAgBpS,MAGnDA,KAAKoP,OAAS,IAAItM,EAAC,QAAC4I,QAAQD,UAAU5I,UACpCC,EAAAA,QAAEmE,KAAKjE,OAAOhD,KAAKkD,QAAQuJ,UAAW,CACpCtJ,KAAML,EAAC,QAAC4I,QAAQD,UAAU5I,UAAUL,MAAMC,SAE5CzC,KAAK+N,QACL/N,KACA8C,UAAEmE,KAAKjE,OAAO,GAAIyD,EAAI3G,KAAME,KAAKkD,QAAQgK,yBACzCiF,MAAMnS,KAAKuG,OAOfsI,eAAgB,SAAU7J,cAClBoE,EAAMpJ,KAAKuG,KACjBvB,EAAWhF,KAAKqS,eAAerN,EAASlF,MACxCE,KAAKgB,SAASV,QAAQ0E,EAAS8D,GAAI9D,GAAU,SAACzE,GAC5C6I,EAAI5C,KAAK,iBAAkB,CACzB1G,KAAMS,IAERP,EAAK0P,aAAa,CAACnP,OAErBP,KAAKsS,cAActN,IAOrB8J,kBAAmB,SAAUrI,cACrB2C,EAAMpJ,KAAKuG,KACXvB,EAAWhF,KAAKqS,eAAe5L,EAAI3G,MACzCE,KAAKgB,SAASV,QAAQ0E,EAAS8D,GAAI9D,GAAU,SAACzE,GAC5C6I,EAAI5C,KAAK,iBAAkB,CAAE1G,KAAMS,IACnCnB,IAAMU,EAAOE,EAAK6N,MAClB7N,EAAK6N,MAAQ,GACb,IAAK,IAAI7O,EAAI,EAAGC,EAAMa,EAAKX,OAAQH,EAAIC,EAAKD,IACtCc,EAAKd,GAAG8J,KAAO9D,EAAS8D,IAC1BhJ,EAAKyS,OAAOvT,EAAG,EAAGgG,GAGtBhF,EAAK0P,aAAa5P,MAEpBE,KAAKsS,cAActN,IAQrBqN,eAAgB,SAAUrN,GAIxB,OAHKlC,EAAC,QAACmE,KAAKwI,QAAQzK,EAAS4D,UAC3B5D,EAAS4D,OAAS,CAAC5D,EAAS4D,OAAOU,IAAKtE,EAAS4D,OAAOY,MAEnDxE,GAOToN,eAAgB,SAAU3L,GACxBzG,KAAKuG,KAAKiM,YAAYxS,KAAK+N,SAC3B/N,KAAK+N,QAAU,KACf/N,KAAKoP,OAAS,MAOhBqD,aAAc,SAAU3J,GACtB,IAAKhK,IAAIE,EAAI,EAAGC,EAAMe,KAAK6N,MAAM1O,OAAQH,EAAIC,EAAKD,IAChD,GAAIgB,KAAK6N,MAAM7O,GAAG8J,KAAOA,EAAI,OAAO9I,KAAK6N,MAAM7O,GAEjD,OAAO,MAMT+P,gBAAiB,SAAUtI,GACzBzG,KAAK0S,cAAcjM,EAAI3G,OAOzBkP,gBAAiB,SAAUvI,GACzBzG,KAAK2S,cAAclM,EAAI3G,OAOzBoP,kBAAmB,SAAUzI,GAC3BzG,KAAK4S,gBAAgBnM,EAAI3G,OAO3BmP,mBAAoB,SAAUxI,GAC5BzG,KAAK6S,iBAAiBpM,EAAI3G,OAO5B+S,iBAAkB,SAAU7N,GAC1B5F,IAAM8H,EAASpE,EAAC,QAACgQ,OAAO9N,EAAS4D,QAC3BmK,EAAU/S,KAAK+N,QAAU/N,KAAKgT,cAAc9L,EAAQlC,GAE1DhF,KAAKoP,OAAS,IAAItM,EAAC,QAAC4I,QAAQD,UAAU5I,UACpCC,EAAAA,QAAEmE,KAAKjE,OAAOhD,KAAKkD,QAAQuJ,UAAW,CACpCtJ,KAAML,EAAC,QAAC4I,QAAQD,UAAU5I,UAAUL,MAAMI,UAE5CmQ,EACA/S,KACAgF,GACAmN,MAAMnS,KAAKuG,OAOfoM,cAAe,SAAU3N,GACvB5F,IAAM8H,EAASpE,EAAC,QAACgQ,OAAO9N,EAAS4D,QAC3BmK,EAAU/S,KAAK+N,QAAU/N,KAAKgT,cAAc9L,EAAQlC,GAC1D+N,EAAOE,SAASC,SAEhBlT,KAAKoP,OAAS,IAAItM,EAAC,QAAC4I,QAAQD,UAAU5I,UACpCC,EAAAA,QAAEmE,KAAKjE,OAAOhD,KAAKkD,QAAQuJ,UAAW,CACpCtJ,KAAML,EAAC,QAAC4I,QAAQD,UAAU5I,UAAUL,MAAME,SAE5CqQ,EACA/S,KACAgF,GACAmN,MAAMnS,KAAKuG,OAUf4M,oBAAqB,SAAUnO,EAAU+N,GACvC,OAAO,SAAUtM,GACXA,EAAI3G,KAAKgJ,KAAO9D,EAAS8D,KAC3BiK,EAAOK,yBACHL,EAAO1N,SAAS0N,EAAO1N,QAAQsB,QACnC3G,KAAKwS,YAAYO,MAWvBC,cAAe,SAAU9L,EAAQlC,cACzB+N,EAAS,IAAIjQ,UAAE+O,OAAO3K,EAAQ,CAClC4K,KAAM9R,KAAKkD,QAAQ4O,MAAQ,IAAIhP,EAAC,QAACiP,KAAKC,QACtCE,aAAa,IACZC,MAAMnS,KAAKuG,MACR8M,EAAkBrT,KAAKmT,oBAAoBnO,EAAU+N,GAK3D,OAJA/S,KAAKuG,KAAKH,GAAG,mBAAoBiN,EAAiBrT,KAAKuG,MACvDwM,EACG3M,GAAG,cAAY,WAAQpG,OAAAA,EAAKuG,KAAKiM,YAAYxS,MAC7CoG,GAAG,UAAU,WAAA,OAAMpG,EAAKuG,KAAKsD,IAAI,mBAAoBwJ,MACjDN,GAOTT,cAAe,SAAUtN,GACnBhF,KAAK+N,SAAS/N,KAAK+N,QAAQ1I,QAAQsB,QACvCvH,IAAM8H,EAASpE,EAAC,QAACgQ,OAAO9N,EAAS4D,QAC3BmK,EAAS/S,KAAKgT,cAAc9L,EAAQlC,GACpCsO,EAAQ,IAAIxQ,EAAAA,QAAE4I,QAAQD,UAAU5I,UACpCC,EAAAA,QAAEmE,KAAKjE,OAAOhD,KAAKkD,QAAQuJ,UAAW,CACpCtJ,KAAML,EAAC,QAAC4I,QAAQD,UAAU5I,UAAUL,MAAMG,OAE5CoQ,EACA/S,KACAgF,GAEEhF,KAAKkD,QAAQuK,aAAa6F,EAAMnB,MAAMnS,KAAKuG,MAC/CvG,KAAKoP,OAASkE,EACdtT,KAAK+N,QAAUgF,GAMjBL,cAAe,SAAU1N,GACvBhF,KAAKuG,KAAKgN,QAAQvO,EAAS4D,OAAQ5D,EAAS6D,MAC5C7I,KAAKsS,cAActN,IAMrB4N,gBAAiB,SAAU5N,cACnBiE,EAAS,SAACuK,GACd,IAAKA,EAAS,OAAOxT,EAAKsS,cAActN,GAExChF,EAAK6N,MAAM0E,OAAOvS,EAAK6N,MAAMlP,QAAQqG,GAAW,GAChDhF,EAAKgB,SAASH,WAAWmE,EAAS8D,IAAE,SAAG9D,GACrChF,EAAKyT,mBAAmBzO,OAIS,mBAA1BhF,KAAKkD,QAAQ0G,SACtB5J,KAAKkD,QAAQ0G,SAAS5E,EAAUiE,GAEhCA,GAAO,IAOXwK,mBAAoB,SAAUzO,cACtB0O,EAAK1T,KAAK8N,MAAMnG,cACpB,IACE3H,KAAKkD,QAAQ4J,wBAAwBC,UACrC,aACA/H,EAAS8D,GACT,MAGJ9I,KAAKuG,KAAKC,KAAK,mBAAoB,CAAE1G,KAAMkF,IAEvC0O,IACF5Q,EAAAA,QAAE6C,QAAQgO,WAAWD,EAAI,GACzB7L,uBACM6L,EAAG/I,YAAY+I,EAAG/I,WAAWiJ,YAAYF,GACnB,IAAtB1T,EAAK6N,MAAM1O,QAAca,EAAKwP,yBACjC,OASP1I,iBAAkB,SAAU9B,GAC1B,OAAIhF,KAAKkD,QAAQ0K,gBACR5N,KAAKkD,QAAQ0K,gBAAgB1L,KAAKlC,KAAMgF,GAE1C5D,KAAKM,UAAUsD,IAMxBuK,iBAAkB,SAAU9I,GAC1BrH,IAAM4F,EAAWhF,KAAK6T,yBAAyBpN,EAAI0E,gBAC9CnG,IACLlC,EAAAA,QAAEoD,SAASyI,gBAAgBlI,GAIzB3D,EAAC,QAAC6C,QAAQiM,SACRnL,EAAIwE,QAAUxE,EAAIyE,WAClBlL,KAAKkD,QAAQ4J,wBAAwBjJ,aAGvC7D,KAAK4S,gBAAgB5N,IAErBhF,KAAKuG,KAAKC,KAAK,gBAAiB,CAAE1G,KAAMkF,IACpChF,KAAKkD,QAAQoK,iBAAiBtN,KAAKkQ,cAS3C2D,yBAA0B,SAAUH,GAClC,OAAI1T,KAAKkD,QAAQ4Q,wBACR9T,KAAKkD,QAAQ4Q,wBAAwB5R,KAAKlC,KAAM0T,GAElD1T,KAAKyS,aAAaiB,EAAGK,aAAa,aAQ3CC,kBAAmB,SAAUhP,GAC3B5F,IAAM8H,EAASlH,KAAKiU,mBAAmBjP,GAIvC,cAHAA,EAAW5D,KAAKC,MAAMD,KAAKM,UAAUsD,KACrB4D,OAET9F,EAAAA,QAAEoR,QAAQC,WACf,CACEC,QAAS,CACPhS,KAAM,UACN0G,GAAI9D,EAAS8D,GACbuL,WAAYrP,IAGhB,CACE5C,KAAM,QACNkS,YAAapN,KASnB+M,mBAAoB,SAAUjP,GAC5B,OAAIA,EAAS4D,kBAAkB9F,EAAC,QAACyR,OACxB,CAACvP,EAAS4D,OAAOU,IAAKtE,EAAS4D,OAAOY,KAExCxE,EAAS4D,OAAO4L,WAQzBC,YAAa,SAAUC,GAErB,IADAtV,IAAMmP,EAAY,GACTvP,EAAI,EAAGC,EAAMyV,EAAQC,SAASxV,OAAQH,EAAIC,EAAKD,IAAK,CAC3DI,IAAM4F,EAAW0P,EAAQC,SAAS3V,GAC7BgG,EAASqP,WAAW/C,UACvBtM,EAASqP,WAAWzL,OAAS5D,EAAS4P,SAASN,YAC5C7D,SACA+D,WAELjG,EAAU3N,KAAKoE,EAASqP,YAE1B,OAAO9F,GAMTsG,UAAW,sBACT,MAAO,CACLzS,KAAM,oBACNuS,SAAU,SAAE7U,GAEV,IADAV,IAAML,EAAS,GACNC,EAAI,EAAGC,EAAMa,EAAKX,OAAQH,EAAIC,EAAKD,IACrCc,EAAKd,GAAGsS,SACXvS,EAAO6B,KAAKZ,EAAKgU,kBAAkBlU,EAAKd,KAG5C,OAAOD,EAPC,CAQPiB,KAAK6N,kBCt6BhB/K,EAAAA,QAAE4I,QAAQD,UAAYA"}