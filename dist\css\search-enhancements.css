/* بهبودهای اضافی برای عناصر جستجو */

/* بهبود ظاهر tooltip جستجو */
.leaflet-control-search .search-tooltip {
  max-height: 200px !important;
  max-width: 280px !important;
  min-width: 200px !important;
  overflow-y: auto !important;
  background: #fff !important;
  border: 1px solid #ccc !important;
  border-radius: 4px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  font-size: 12px !important;
  font-family: "Nahid", Tahoma, Geneva, Verdana, sans-serif !important;
  z-index: 10000 !important;
}

/* بهبود ظاهر آیتم‌های tooltip */
.leaflet-control-search .search-tooltip .search-tip {
  padding: 6px 10px !important;
  border-bottom: 1px solid #eee !important;
  cursor: pointer !important;
  font-size: 12px !important;
  line-height: 1.3 !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  transition: background-color 0.2s ease !important;
  text-align: center !important;
  color: #333 !important;
  font-weight: normal !important;
}

.leaflet-control-search .search-tooltip .search-tip:hover {
  background: #f5f5f5 !important;
  color: #333 !important;
}

.leaflet-control-search .search-tooltip .search-tip:last-child {
  border-bottom: none !important;
}

/* بهبود ظاهر scrollbar برای tooltip */
.leaflet-control-search .search-tooltip::-webkit-scrollbar {
  width: 6px !important;
}

.leaflet-control-search .search-tooltip::-webkit-scrollbar-track {
  background: #f1f1f1 !important;
  border-radius: 3px !important;
}

.leaflet-control-search .search-tooltip::-webkit-scrollbar-thumb {
  background: #ccc !important;
  border-radius: 3px !important;
}

.leaflet-control-search .search-tooltip::-webkit-scrollbar-thumb:hover {
  background: #999 !important;
}
