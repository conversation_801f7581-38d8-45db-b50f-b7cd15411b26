<!DOCTYPE html>
<html lang="fa">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Box Zoom استاندارد</title>

    <!-- استایل‌ها -->
    <link rel="stylesheet" href="../../leaflet/dist/leaflet.css" />
    <link rel="stylesheet" href="../dist/leaflet.toolbar.css"/>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" />

    <!-- اسکریپت‌ها -->
    <script src="../../leaflet/dist/leaflet-src.js"></script>
    <script src="../dist/leaflet.toolbar-src.js"></script>

    <style>
        html, body, #map { margin: 0; height: 100%; width: 100%; }

        /* کرسرهای صحیح زوم */
        .zoom-in-cursor { cursor: zoom-in !important; }  /* ذره‌بین زوم این */
        .zoom-out-cursor { cursor: zoom-out !important; } /* ذره‌بین زوم اوت */

        /* استایل پنجره زوم */
        .leaflet-zoom-box {
            position: absolute;
            border: 2px dashed blue;
            background: rgba(0, 0, 255, 0.2);
            pointer-events: none;
        }

        /* استایل دکمه‌های تولبار */
        .leaflet-toolbar-icon {
            font-size: 12px !important;
            color: black !important;
            background: white !important;
            border-radius: 4px !important;
            padding: 4px !important;
            width: 24px !important;
            height: 24px !important;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid #ccc !important;
        }
        .leaflet-toolbar-icon:hover {
            background: #f4f4f4 !important;
        }
    </style>
</head>
<body>
    <div id="map"></div>

    <script>
        var map = L.map('map').setView([41.7896, -87.5996], 15);
        L.tileLayer("http://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png", {
            attribution: '<a href="http://openstreetmap.org/copyright">OpenStreetMap Contributors</a>'
        }).addTo(map);

        var zoomMode = null;
        var startPoint = null;
        var boxDiv = null;

        function activateBoxZoom(mode) {
            zoomMode = mode;
            map.dragging.disable();
            map.getContainer().classList.add(mode === "in" ? "zoom-in-cursor" : "zoom-out-cursor");
        }

        function deactivateBoxZoom() {
            zoomMode = null;
            map.dragging.enable();
            map.getContainer().classList.remove("zoom-in-cursor", "zoom-out-cursor");
        }

        map.on("mousedown", function (e) {
            if (!zoomMode) return;

            startPoint = e.containerPoint;
            boxDiv = L.DomUtil.create("div", "leaflet-zoom-box", map.getContainer());
            boxDiv.style.left = startPoint.x + "px";
            boxDiv.style.top = startPoint.y + "px";
            boxDiv.style.width = "0px";
            boxDiv.style.height = "0px";

            map.on("mousemove", onMouseMove);
            map.on("mouseup", onMouseUp);
        });

        function onMouseMove(e) {
            if (!startPoint || !boxDiv) return;
            var endPoint = e.containerPoint;

            var left = Math.min(startPoint.x, endPoint.x);
            var top = Math.min(startPoint.y, endPoint.y);
            var width = Math.abs(startPoint.x - endPoint.x);
            var height = Math.abs(startPoint.y - endPoint.y);

            boxDiv.style.left = left + "px";
            boxDiv.style.top = top + "px";
            boxDiv.style.width = width + "px";
            boxDiv.style.height = height + "px";
        }

        function onMouseUp(e) {
            if (!startPoint || !boxDiv) return;
            var bounds = L.latLngBounds(
                map.containerPointToLatLng(startPoint),
                map.containerPointToLatLng(e.containerPoint)
            );

            if (zoomMode === "in") {
                map.fitBounds(bounds);
            } else if (zoomMode === "out") {
                var currentZoom = map.getZoom();
                var newZoom = Math.max(currentZoom - 2, 1);
                map.setView(bounds.getCenter(), newZoom);
            }

            L.DomUtil.remove(boxDiv);
            boxDiv = null;
            startPoint = null;

            map.off("mousemove", onMouseMove);
            map.off("mouseup", onMouseUp);

            deactivateBoxZoom();
        }

        var BoxZoomInAction = L.Toolbar2.Action.extend({
            options: {
                toolbarIcon: {
                    html: '<i class="fas fa-search-plus"></i>',
                    tooltip: 'Box Zoom In'
                }
            },
            addHooks: function () {
                activateBoxZoom("in");
            }
        });

        var BoxZoomOutAction = L.Toolbar2.Action.extend({
            options: {
                toolbarIcon: {
                    html: '<i class="fas fa-search-minus"></i>',
                    tooltip: 'Box Zoom Out'
                }
            },
            addHooks: function () {
                activateBoxZoom("out");
            }
        });

        new L.Toolbar2.Control({
            position: "topleft", // تعیین موقعیت در بالا سمت چپ
            actions: [BoxZoomInAction, BoxZoomOutAction]
        }).addTo(map);

    </script>
</body>
</html>
