{"name": "@turf/length", "version": "5.1.5", "description": "turf length module", "main": "main.js", "module": "main.es.js", "types": "index.d.ts", "files": ["index.js", "index.d.ts", "main.js", "main.es.js"], "scripts": {"pretest": "rollup -c ../../rollup.config.js", "test": "node -r @std/esm test.js", "posttest": "node -r @std/esm ../../scripts/validate-es5-dependencies.js", "bench": "node -r @std/esm bench.js", "docs": "node ../../scripts/generate-readmes"}, "repository": {"type": "git", "url": "git://github.com/Turfjs/turf.git"}, "keywords": ["turf", "linestring", "length", "distance", "units", "gis"], "author": "Turf Authors", "contributors": ["<PERSON> <@DenisCarriere>", "<PERSON> <@tmcw>"], "license": "MIT", "bugs": {"url": "https://github.com/Turfjs/turf/issues"}, "homepage": "https://github.com/Turfjs/turf", "devDependencies": {"@std/esm": "*", "benchmark": "*", "load-json-file": "*", "rollup": "*", "tape": "*", "write-json-file": "*"}, "dependencies": {"@turf/distance": "^5.1.5", "@turf/helpers": "^5.1.5", "@turf/meta": "^5.1.5"}, "@std/esm": {"esm": "js", "cjs": true}}