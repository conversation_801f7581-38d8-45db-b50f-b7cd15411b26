<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"> 
<html xmlns="http://www.w3.org/1999/xhtml"> 
<head> 
<title></title> 
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" /> 
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.3.0/dist/leaflet.css" />
<link rel="stylesheet" href="../src/leaflet-search.css" />
<link rel="stylesheet" href="style.css" />
<style>
.search-input {
	font-family:Courier
}
.search-input,
.leaflet-control-search {
	max-width:400px;
}
</style>
</head>

<body>
<h3><a href="../"><big>◄</big> Leaflet.Control.Search</a></h3>

<h4>Google GeoCoding API: <em>search locations name by Google Maps API</em></h4>
<div id="map"></div>

<script src="https://unpkg.com/leaflet@1.3.0/dist/leaflet.js"></script>
<script src="../src/leaflet-search.js"></script>
<script src="../src/leaflet-search-geocoder.js"></script>
<script>

	var map = L.map('map', {
		zoom: 9,
		center: [41.5757,13.0024],
		layers: L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png')
	});
	
	L.control.search({
		geocoder: 'google'
	}).addTo(map);

</script>

<div id="copy"><a href="https://opengeo.tech/">Website</a> &bull; <a rel="author" href="https://opengeo.tech/stefano-cudini/">Stefano Cudini</a></div>



</body>
</html>
