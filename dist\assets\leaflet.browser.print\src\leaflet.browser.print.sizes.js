/**
	MIT License http://www.opensource.org/licenses/mit-license.php
	Author <PERSON> <<EMAIL>> (https://github.com/<PERSON><PERSON>/leaflet.browser.print)
**/
//leaflet.browser.print.sizes.js
/* Portrait mode sizes in mm for 0 lvl*/
L.BrowserPrint.Size =  {
	A: {
		Width: 840,
		Height: 1188
	},
	B: {
		Width: 1000,
		Height: 1414
	},
	C: {
		Width: 916,
		Height: 1296
	},
	D: {
		Width: 770,
		Height: 1090
	},
	LETTER: {
		Width: 216,
		Height: 279
	},
	HALFLETTER: {
		Width: 140,
		Height: 216
	},
	LEGAL: {
		Width: 216,
		Height: 356
	},
	JUNIORLEGAL: {
		Width: 127,
		Height: 203
	},
	TABLOID: {
		Width: 279,
		Height: 432
	},
	LEDGER: {
		Width: 432,
		Height: 279
	}
};
