/* src/css/app.css یا src/css/loading.css  
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5); /* پس‌زمینه نیمه‌شفاف  
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3; /* رنگ روشن  
  border-top: 4px solid #3498db; /* رنگ اصلی  
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

*/


#loading-mask {
  background-color: rgba(0, 0, 0, 0.5);
}

/* استایل‌های موجود */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5); /* پس‌زمینه نیمه‌شفاف */
  z-index: 9999;
  display: flex;
  flex-direction: column; /* تغییر جهت به عمودی */
  justify-content: center;
  align-items: center;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3; /* رنگ روشن */
  border-top: 4px solid #3498db; /* رنگ اصلی */
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* استایل پیام */
.loading-message {
  margin-top: 20px; /* فاصله از اسپینر */
  font-size: 24px; /* اندازه فونت */
  color: #fff; /* رنگ متن */
  font-weight: bold; /* متن ضخیم */
  text-align: center;
}

/* انیمیشن اسپینر */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}