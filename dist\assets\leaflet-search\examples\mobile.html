<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"> 
<html xmlns="http://www.w3.org/1999/xhtml"> 
<head> 
	<title>Leaflet Search Mobile Example</title> 
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" /> 
	<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
	<link rel="stylesheet" href="https://unpkg.com/leaflet@1.3.0/dist/leaflet.css" />
	<link rel="stylesheet" href="../src/leaflet-search.css" />
	<link rel="stylesheet" href="../src/leaflet-search.mobile.css" />
	<link rel="stylesheet" href="style.css" />
	<link rel="stylesheet" href="mobile.css" />
</head>

<body>
<h3><a href="../"><big>◄</big> Leaflet.Control.Search</a> <small>&nbsp;&nbsp;Mobile example </small></h3> 

<div id="map"></div>

<div id="post-it">
<b>Search values:</b><br />
OpenStreetMap Data offer by MapQuest Open Platform<br />
<small><a href="http://open.mapquestapi.com/nominatim/">open.mapquestapi.com</a></small>
</div>

<script src="https://unpkg.com/leaflet@1.3.0/dist/leaflet.js"></script>
<script src="../src/leaflet-search.js"></script>
<script>

	var map = new L.Map('map', {zoom: 9, center: new L.latLng([41.575730,13.002411]),zoomControl: false });

	map.addLayer(new L.TileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png'));	//base layer

	function formatJSON(rawjson) {	//callback that remap fields name
		var json = {},
			key, loc, disp = [];
			
		for(var i in rawjson)
		{	
			disp = rawjson[i].display_name.split(',');	

			key = disp[0] +', '+ disp[1];
			
			loc = L.latLng( rawjson[i].lat, rawjson[i].lon );
			
			json[ key ]= loc;	//key,value format
		}
		
		return json;
	}
			
	var mobileOpts = {
		url: 'http://nominatim.openstreetmap.org/search?format=json&q={s}',
		jsonpParam: 'json_callback',
		formatData: formatJSON,		
		textPlaceholder: 'Color...',
		autoType: false,
		tipAutoSubmit: true,
		autoCollapse: false,
		autoCollapseTime: 20000,
		delayType: 800,	//with mobile device typing is more slow
		marker: {
			icon: true
		}		
	};
	
	map.addControl( new L.Control.Search(mobileOpts) );
	//view source of search.php for more details
	
	map.addControl(new L.Control.Zoom());
	
</script>

<div id="copy"><a href="https://opengeo.tech/">Website</a> &bull; <a rel="author" href="https://opengeo.tech/stefano-cudini/">Stefano Cudini</a></div>



</body>
</html>
