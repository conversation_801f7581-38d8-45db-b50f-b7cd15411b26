{"name": "c3", "version": "0.7.20", "description": "D3-based reusable chart library", "main": "c3.js", "files": ["c3.js", "c3.min.js", "c3.esm.js", "c3.css", "c3.min.css", "src"], "scripts": {"start": "run-p serve-static watch", "serve-static": "static -p 8080 htdocs/", "lint": "jshint --reporter=node_modules/jshint-stylish src/ spec/", "type": "tsc --noEmit", "fmt": "prettier --write rollup.config.js karma.conf.js \"src/**/*.ts\" \"spec/**/*.ts\" *.json", "docs": "bundle exec middleman", "build": "run-s build:js build:css", "build:js": "run-s build:js:rollup build:js:uglify", "build:js:rollup": "rollup -c", "build:js:uglify": "uglifyjs htdocs/js/c3.js --compress --mangle --comments -o htdocs/js/c3.min.js", "build:css": "run-s build:css:sass build:css:min", "build:css:sass": "sass src/scss/main.scss > htdocs/css/c3.css", "build:css:min": "cleancss -o htdocs/css/c3.min.css htdocs/css/c3.css", "build:docs": "bundle exec middleman build", "publish-docs": "npm run build:docs && gh-pages -d build -m \"chore: update gh-pages [skip ci]\"", "watch": "nodemon -e js,scss --watch src -x npm run build:js:rollup && npm run build:css:sass", "watch:js": "nodemon -e js --watch src --ignore src/scss -x 'npm run build:js:rollup'", "watch:css": "nodemon -e scss --watch src -x 'npm run build:css:sass'", "watch:docs": "bundle exec middleman", "karma": "karma start karma.conf.js", "test": "run-s build lint karma", "dist": "run-s build copy-to-root copy-to-docs", "copy-to-docs": "cp htdocs/js/c3.* docs/js/ && cp htdocs/css/c3.* docs/css/", "copy-to-root": "cp htdocs/{css,js}/c3.* ./", "codecov": "codecov"}, "repository": {"type": "git", "url": "git://github.com/c3js/c3.git"}, "keywords": ["d3", "chart", "graph"], "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "license": "MIT", "gitHead": "84e03109d9a590f9c8ef687c03d751f666080c6f", "readmeFilename": "README.md", "dependencies": {"d3": "^5.8.0"}, "devDependencies": {"@types/d3": "^5.7.2", "@types/jasmine": "^3.5.10", "clean-css-cli": "^4.1.11", "codecov": "^3.0.4", "gh-pages": "^2.0.0", "jasmine-core": "^2.3.4", "jshint": "^2.9.7", "jshint-stylish": "^2.1.0", "karma": "^4.4.1", "karma-chrome-launcher": "^3.0.0", "karma-jasmine": "^1.1.0", "karma-spec-reporter": "^0.0.32", "karma-typescript": "^5.0.3", "node-static": "^0.7.9", "nodemon": "^2.0.0", "npm-run-all": "^4.1.3", "prettier": "^1.19.1", "rollup": "^1.32.0", "rollup-plugin-typescript2": "^0.27.1", "sass": "^1.10.3", "status-back": "^1.1.0", "typescript": "^3.9.5", "uglify-js": "^3.6.4", "watchify": "^3.11.1"}, "nyc": {"exclude": ["src/polyfill.js", "spec/"]}}