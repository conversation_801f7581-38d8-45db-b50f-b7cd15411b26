{"name": "d3-brush", "version": "1.1.6", "description": "Select a one- or two-dimensional region using the mouse or touch.", "keywords": ["d3", "d3-module", "brush", "interaction"], "homepage": "https://d3js.org/d3-brush/", "license": "BSD-3-<PERSON><PERSON>", "author": {"name": "<PERSON>", "url": "http://bost.ocks.org/mike"}, "main": "dist/d3-brush.js", "unpkg": "dist/d3-brush.min.js", "jsdelivr": "dist/d3-brush.min.js", "module": "src/index.js", "repository": {"type": "git", "url": "https://github.com/d3/d3-brush.git"}, "files": ["dist/**/*.js", "src/**/*.js"], "scripts": {"pretest": "rollup -c", "test": "tape 'test/**/*-test.js' && eslint src", "prepublishOnly": "rm -rf dist && yarn test", "postpublish": "git push && git push --tags && cd ../d3.github.com && git pull && cp ../${npm_package_name}/dist/${npm_package_name}.js ${npm_package_name}.v${npm_package_version%%.*}.js && cp ../${npm_package_name}/dist/${npm_package_name}.min.js ${npm_package_name}.v${npm_package_version%%.*}.min.js && git add ${npm_package_name}.v${npm_package_version%%.*}.js ${npm_package_name}.v${npm_package_version%%.*}.min.js && git commit -m \"${npm_package_name} ${npm_package_version}\" && git push && cd - && zip -j dist/${npm_package_name}.zip -- LICENSE README.md dist/${npm_package_name}.js dist/${npm_package_name}.min.js"}, "dependencies": {"d3-dispatch": "1", "d3-drag": "1", "d3-interpolate": "1", "d3-selection": "1", "d3-transition": "1"}, "devDependencies": {"eslint": "6", "rollup": "1", "rollup-plugin-terser": "5", "tape": "4"}}