html,
body {
  padding: 0;
  margin: 0;
}

#map {
  position: absolute;
  width: 100%;
  height: 100%;
}

/*
   The part below is for a fancy "Fork me on GitHub" ribbon in the top right corner.
   It was created with the help of https://github.com/codepo8/css-fork-on-github-ribbon.
*/

#forkmeongithub a {
  background: #bb1111cc;
  color: #fff;
  font-family: arial, sans-serif;
  font-size: 1rem;
  font-weight: bold;
  text-align: center;
  text-decoration: none;
  text-shadow: 2px 2px #00000055;
  line-height: 1.4rem;
  padding: 5px 40px;
  top: -150px;
}

#forkmeongithub a:hover {
  background: #333388bb;
}

#forkmeongithub a::before,
#forkmeongithub a::after {
  content: "";
  width: 100%;
  display: block;
  position: absolute;
  top: 1px;
  left: 0;
  height: 1px;
  background: #fff;
}

#forkmeongithub a::after {
  bottom: 1px;
  top: auto;
}

@media screen and (min-width: 800px) {
  #forkmeongithub {
    position: fixed;
    display: block;
    top: -10px;
    right: -10px;
    width: 200px;
    overflow: hidden;
    height: 200px;
    z-index: 9999;
  }
  #forkmeongithub a {
    width: 200px;
    position: absolute;
    top: 60px;
    right: -60px;
    transform: rotate(45deg);
    box-shadow: 4px 4px 10px rgba(0, 0, 0, 0.8);
  }
}
