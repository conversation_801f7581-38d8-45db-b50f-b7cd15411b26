{"nodes": {"nixpkgs": {"locked": {"lastModified": 1613582597, "narHash": "sha256-6LvipIvFuhyorHpUqK3HjySC5Y6gshXHFBhU9EJ4DoM=", "path": "/nix/store/srvplqq673sqd9vyfhyc5w1p88y1gfm4-source", "rev": "6b1057b452c55bb3b463f0d7055bc4ec3fd1f381", "type": "path"}, "original": {"id": "nixpkgs", "type": "indirect"}}, "root": {"inputs": {"nixpkgs": "nixpkgs", "utils": "utils"}}, "utils": {"locked": {"lastModified": 1610051610, "narHash": "sha256-U9rPz/usA1/Aohhk7Cmc2gBrEEKRzcW4nwPWMPwja4Y=", "owner": "numtide", "repo": "flake-utils", "rev": "3982c9903e93927c2164caa727cd3f6a0e6d14cc", "type": "github"}, "original": {"owner": "numtide", "repo": "flake-utils", "type": "github"}}}, "root": "root", "version": 7}