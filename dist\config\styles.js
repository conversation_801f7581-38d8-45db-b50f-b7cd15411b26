//styles.js
function loadStyles() {
  const styles = [
    "./assets/bootstrap-3.3.6/dist/css/bootstrap.min.css",
    "./assets/jquerySelect2/dist/css/select2.min.css",
    "./assets/font-awesome4/4.6.3/css/font-awesome.min.css", // 2. Font Awesome
    "./assets/fontawesome-5.15.2/css/all.css",
    "./assets/leaflet@1.9.4/dist/leaflet.css", // 3. Leaflet
    // "./assets/bootstrap@5.3.0/bootstrap/dist/css/bootstrap.min.css",
    "./assets/leaflet-bookmarks/dist/leaflet.bookmarks.min.css",
    "./assets/leaflet-minimap/dist/Control.MiniMap.min.css",
    "./assets/leaflet-compass/dist/leaflet-compass.min.css",
    "./assets/leaflet-ruler/src/leaflet-ruler.css",
    "./assets/Leaflet.NavBar/src/Leaflet.NavBar.css",
    "./assets/leaflet-toolbar/dist/leaflet.toolbar.css",
    "./assets/leaflet.locatecontrol/dist/L.Control.Locate.min.css",
    "./assets/LeafletPlugins/sidebar/css/leaflet-sidebar.css", // 3.1 Leaflet Sidebar
    "./assets/bootstrap-table@1.20.2/bootstrap-table/dist/bootstrap-table.min.css", // 4. Bootstrap Table
    "./assets/c3@0.7.20/c3/c3.min.css", // 5. C3.js
    "./assets/geocoder/leaflet-control-geocoder/leaflet-control-geocoder/dist/Control.Geocoder.css",
    "./assets/jQuery-QueryBuilder@3.0.0/jQuery-QueryBuilder/dist/css/query-builder.default.css", // 7. QueryBuilder
    "./assets/LeafletDraw/dist/leaflet.draw.css", 
    "./assets/LeafletMeasure/leaflet-measure/dist/leaflet-measure.css", 
    "./assets/lightbox2/dist/css/lightbox.min.css",
    "./assets/coordinatedimagepreview/coordinatedimagepreview.css",
    "./assets/Leaflet.label/dist/leaflet.label.css",
    "./assets/leaflet-search/dist/leaflet-search.src.css",
    "./css/app.css",  
    "./css/loading.css",
    "./css/mousePosition.css",
    "./css/search-custom.css",
    "./css/widget-toolbar.css"
    
  ];

  const loadStyle = (href) => {
    return new Promise((resolve, reject) => {
      const link = document.createElement("link");
      link.rel = "stylesheet";
      link.href = href;
      link.onload = () => {
        resolve();
      };
      link.onerror = () => reject(new Error(`خطا در لود ${href}`));
      document.head.appendChild(link);
    });
  };

  return styles.reduce((promise, href) => {
    return promise.then(() => loadStyle(href));
  }, Promise.resolve());
}

export { loadStyles };