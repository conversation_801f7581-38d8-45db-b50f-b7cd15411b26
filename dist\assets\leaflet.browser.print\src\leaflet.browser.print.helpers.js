/**
	MIT License http://www.opensource.org/licenses/mit-license.php
	نویسنده: ایگور ولادیکا <<EMAIL>> (https://github.com/<PERSON>-<PERSON><PERSON><PERSON>/leaflet.browser.print)
**/
// leaflet.browser.print.helpers.js
L.BrowserPrint.Helper = {
	// تابع برای محاسبه حاشیه‌های صفحه
	getPageMargin: function (mode, type) {
		var margin = mode.options.margin; // حاشیه‌های تعریف‌شده در حالت چاپ
		var size = this.getPaperSize(mode); // اندازه کاغذ
		var marginInMm = ((size.Width + size.Height) / 39.9); // محاسبه حاشیه پیش‌فرض بر اساس اندازه کاغذ

		if (!type && type !== 0) {
			type = ""; // اگر نوع واحد مشخص نشده باشد، پیش‌فرض خالی است
		}

		// اگر حاشیه به صورت یک عدد مشخص شده باشد، آن را به تمام جهات اعمال می‌کنیم
		if (margin >= 0) {
			margin = {
				top: margin,
				right: margin,
				bottom: margin,
				left: margin
			}
		} else if (!margin) {
			margin = {}; // اگر حاشیه تعریف نشده باشد، یک شیء خالی ایجاد می‌کنیم
		}

		var faktor = 1; // فاکتور تبدیل واحد
		if (type === "in") { // اگر واحد اینچ باشد، فاکتور تبدیل به میلی‌متر اعمال می‌شود
			faktor = 25.4;
		}

		// محاسبه حاشیه‌ها برای هر جهت
		var topMargin = margin.top >= 0 ? margin.top : marginInMm;
		var rightMargin = margin.right >= 0 ? margin.right : marginInMm;
		var bottomMargin = margin.bottom >= 0 ? margin.bottom : marginInMm;
		var leftMargin = margin.left >= 0 ? margin.left : marginInMm;

		// تبدیل حاشیه‌ها به واحد مورد نظر و اضافه کردن واحد به مقدار
		var top = (topMargin / faktor).toFixed(2) + type;
		var right = (rightMargin / faktor).toFixed(2) + type;
		var bottom = (bottomMargin / faktor).toFixed(2) + type;
		var left = (leftMargin / faktor).toFixed(2) + type;

		return {
			top,
			right,
			bottom,
			left
		};
	},

	// تابع برای محاسبه اندازه کاغذ
	getPaperSize: function (mode) {
		if (mode.options.pageSeries) { // اگر سری کاغذ مشخص شده باشد
			var series = L.BrowserPrint.Size[mode.options.pageSeries]; // اندازه‌های استاندارد سری کاغذ
			var w = series.Width;
			var h = series.Height;
			var switchSides = false; // آیا اندازه‌ها باید جابجا شوند؟

			if (mode.options.pageSeriesSize && mode.options.pageSeriesSize !== '0') {
				mode.options.pageSeriesSize = +mode.options.pageSeriesSize; // تبدیل به عدد
				switchSides = mode.options.pageSeriesSize % 2 === 1; // اگر اندازه فرد باشد، جابجا می‌شود
				if (switchSides) {
					w = w / (mode.options.pageSeriesSize - 1 || 1);
					h = h / (mode.options.pageSeriesSize + 1);
				} else {
					w = w / mode.options.pageSeriesSize;
					h = h / mode.options.pageSeriesSize;
				}
			}

			return {
				Width: switchSides ? h : w, // اگر جابجا شده باشد، ارتفاع به جای عرض و برعکس
				Height: switchSides ? w : h
			};
		} else { // اگر سری کاغذ مشخص نشده باشد
			var size = L.BrowserPrint.Size[mode.options.pageSeriesSize]; // اندازه استاندارد کاغذ
			return {
				Width: size.Width,
				Height: size.Height
			};
		}
	},

	// تابع برای محاسبه اندازه نهایی با توجه به جهت صفحه (عمودی یا افقی)
	getSize: function (mode, orientaion = 'Portrait') {
		var size = this.getPaperSize(mode); // اندازه کاغذ
		var pageMargin = this.getPageMargin(mode, 0); // حاشیه‌های صفحه

		// محاسبه حاشیه‌های بالا و پایین یا چپ و راست بر اساس جهت صفحه
		var topbottom = orientaion === 'Portrait' ? parseFloat(pageMargin.top) + parseFloat(pageMargin.bottom) : parseFloat(pageMargin.left) + parseFloat(pageMargin.right);
		var leftright = orientaion === 'Portrait' ? parseFloat(pageMargin.left) + parseFloat(pageMargin.right) : parseFloat(pageMargin.top) + parseFloat(pageMargin.bottom);

		// محاسبه ارتفاع و عرض نهایی با کم کردن حاشیه‌ها
		var height = Math.floor(size.Height - topbottom);
		var width = Math.floor(size.Width - leftright);

		// اعمال نسبت پیکسل دستگاه و تبدیل به میلی‌متر
		size.Width = width * (window.devicePixelRatio || 1) + 'mm';
		size.Height = height * (window.devicePixelRatio || 1) + 'mm';

		return size;
	}
};