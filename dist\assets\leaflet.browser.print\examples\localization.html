<!DOCTYPE html>
<html>
<head>
    <title>Leaflet browser print plugin | Localization</title>
    <meta charset="utf-8"/>

    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="https://unpkg.com/leaflet@latest/dist/leaflet.css"/>
    <script src="https://unpkg.com/leaflet@latest/dist/leaflet.js"></script>
    <script src="../dist/leaflet.browser.print.js"></script>
    <style>
        html, body, #map {
            width: 100%;
            height: 100%;
            margin: 0px;
            padding: 0px;
        }
    </style>
</head>

<body>
<div id="map"></div>

<script>

    var map = L.map('map').setView([51.505, 1], 6);

    <PERSON>.tileLayer('//{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        maxZoom: 19,
        attribution: '&copy; <a href="http://www.openstreetmap.org/copyright">OpenStreetMap</a>'
    }).addTo(map);

    L.circle([51.505, 1], 500000, {
        color: 'red',
        fillColor: '#f03',
        fillOpacity: 0.5
    }).addTo(map);
    L.circle([55, 5], 50000, {
        color: 'green',
        fillColor: 'darkgreen',
        fillOpacity: 0.8
    }).addTo(map);

    var browserPrint = L.browserPrint(map,{debug:false, cancelWithEsc: true});

    var c = L.control.browserPrint({
        printModes: [
            L.BrowserPrint.Mode.Landscape(),
            L.BrowserPrint.Mode.Landscape('A4',{margin: 0, title: 'Header / Footer', header: {
                enabled: true,
                text: "<span>A wonderful print Plugin</span>",
                size: "10mm",
                overTheMap: false,
            },
            footer: {
                enabled: true,
                text: "<span>Created by Leaflet-Browser-Plugin</span>",
                size: "10mm",
                overTheMap: false,
            }
        }), L.BrowserPrint.Mode.Custom("A4", {title: "Select area (Custom)"})]
    }, browserPrint).addTo(map);

</script>
</body>

</html>
