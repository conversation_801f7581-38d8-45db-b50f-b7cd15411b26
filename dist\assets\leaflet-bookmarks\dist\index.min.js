/**
 * L.Control.Bookmarks v0.5.1
 * Leaflet plugin for user-generated bookmarks
 *
 * <AUTHOR>
 * @license MIT
 * @preserve
 */
!function(t,o){"object"==typeof exports&&"undefined"!=typeof module?module.exports=o(require("leaflet")):"function"==typeof define&&define.amd?define(["leaflet"],o):((t="undefined"!=typeof globalThis?globalThis:t||self).L=t.L||{},t.L.Control=t.L.Control||{},t.L.Control.Bookmarks=o(t.L))}(this,(function(t){"use strict";function o(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var e=o(t);function s(t,o,e){return t.replace(e||/{{([\s\S]+?)}}/g,(function(t,e){if(-1===(e=function(t){return t.replace(/^\s+|\s+$/g,"")}(e)).indexOf("."))return"\\"==t.charAt(0)?t.slice(1):null!=o[e]?o[e]:"";for(var s=o,i=0,a=(e=e.split(".")).length;i<a;i++){if(!(e[i]in s))return"";s=s[e[i]]}return s}))}var i="abcdefghijklmnopqrstuvwxyz";function a(t){return(t||i[Math.floor(Math.random()*i.length)])+(new Date).getTime().toString(16)}var n={},r=function(t){this._prefix=t};r.prototype.getItem=function(t,o){o(n[this._prefix+t])},r.prototype.setItem=function(t,o,e){n[this._prefix+t]=o,e(o)},r.prototype.getAllItems=function(t){var o=[];for(var e in n)n.hasOwnProperty(e)&&0===e.indexOf(this_prefix)&&o.push(n[e]);t(o)},r.prototype.removeItem=function(t,o){var e=this;this.getItem(t,(function(s){s?delete n[e._prefix+t]:s=null,o&&o(s)}))};var l=/^[\{\[](.)*[\]\}]$/,p=function(t){this._prefix=t,this._storage=window.localStorage};p.prototype.getItem=function(t,o){var e=this._storage.getItem(this._prefix+t);e&&l.test(e)&&(e=JSON.parse(e)),o(e)},p.prototype.getAllItems=function(t){var o=[],e=this._prefix.length;for(var s in this._storage)null!==this._storage.getItem(s)&&0===s.indexOf(this._prefix)&&this.getItem(s.substring(e),(function(t){return o.push(t)}));t(o)},p.prototype.removeItem=function(t,o){var e=this,s=this;this.getItem(t,(function(i){e._storage.removeItem(s._prefix+t),o&&o(i)}))},p.prototype.setItem=function(t,o,e){var s=o.toString();"[object Object]"===s&&(s=JSON.stringify(o)),this._storage.setItem(this._prefix+t,s),e(o)};var m=2,h=3,u=function t(o,e){"string"!=typeof o&&(e=o,o=a()),this._name=o,this._engine=t.createEngine(e,this._name,Array.prototype.slice.call(arguments,2))};u.createEngine=function(t,o,e){return t===m?new r(o):t===h?new p(o):void 0},u.prototype.setItem=function(t,o,e){return this._engine.setItem(t,o,e),this},u.prototype.getItem=function(t,o){return this._engine.getItem(t,o),this},u.prototype.getAllItems=function(t){this._engine.getAllItems(t)},u.prototype.removeItem=function(t,o){this._engine.removeItem(t,o)};var d,k,c={CREATE:1,UPDATE:2,SHOW:3,OPTIONS:4},f=e.default.Popup.extend({statics:{modes:c},options:{mode:c.CREATE,className:"leaflet-bookmarks-form-popup",templateOptions:{formClass:"leaflet-bookmarks-form",inputClass:"leaflet-bookmarks-form-input",inputErrorClass:"has-error",idInputClass:"leaflet-bookmarks-form-id",coordsClass:"leaflet-bookmarks-form-coords",submitClass:"leaflet-bookmarks-form-submit",inputPlaceholder:"Bookmark name",removeClass:"leaflet-bookmarks-form-remove",editClass:"leaflet-bookmarks-form-edit",cancelClass:"leaflet-bookmarks-form-cancel",editableClass:"editable",removableClass:"removable",menuItemClass:"nav-item",editMenuText:"Edit",removeMenuText:"Remove",cancelMenuText:"Cancel",submitTextCreate:"+",submitTextEdit:'<span class="icon-checkmark"></span>'},generateNames:!1,minWidth:160,generateNamesPrefix:"Bookmark ",template:'<form class="{{ formClass }}"><div class="input-group"><input type="text" name="bookmark-name" placeholder="{{ inputPlaceholder }}" class="form-control {{ inputClass }}" value="{{ name }}"><input type="hidden" class={{ idInputClass }} value="{{ id }}"><button type="submit" class="input-group-addon {{ submitClass }}">{{ submitText }}</button></div><div class="{{ coordsClass }}">{{ coords }}</div></form>',menuTemplate:'<ul class="nav {{ mode }}" role="menu"><li class="{{ editClass }}"><a href="#" class="{{ menuItemClass }}">{{ editMenuText }}</a></li><li class="{{ removeClass }}"><a href="#" class="{{ menuItemClass }}">{{ removeMenuText }}</a></li><li><a href="#" class="{{ menuItemClass }} {{ cancelClass }}">{{ cancelMenuText }}</a></li></ul>'},initialize:function(t,o,s,i){this._bookmark=i,this._control=s,this._latlng=o.getLatLng(),o._popup_=this,e.default.Popup.prototype.initialize.call(this,t,o)},_initLayout:function(){if(e.default.Popup.prototype._initLayout.call(this),this.options.mode===c.SHOW&&(this._bookmark.editable||this._bookmark.removable)){var t=this._menuButton=e.default.DomUtil.create("a","leaflet-popup-menu-button");this._container.insertBefore(t,this._closeButton),t.href="#menu",t.innerHTML='<span class="menu-icon"></span>',e.default.DomEvent.disableClickPropagation(t),e.default.DomEvent.on(t,"click",this._onMenuButtonClick,this)}},_showMenu:function(){this._map.fire("bookmark:options",{data:this._bookmark})},_onMenuButtonClick:function(t){e.default.DomEvent.preventDefault(t),this._showMenu(),this.close()},_updateContent:function(){var t;if(this.options.mode===c.SHOW)t=this._control._getPopupContent(this._bookmark);else{var o=this.options.template,i=this.options.templateOptions.submitTextCreate;this.options.mode===c.OPTIONS&&(o=this.options.menuTemplate),this.options.mode===c.UPDATE&&(i=this.options.templateOptions.submitTextEdit);var a=[];this._bookmark.editable&&a.push(this.options.templateOptions.editableClass),this._bookmark.removable&&a.push(this.options.templateOptions.removableClass),t=s(o,e.default.Util.extend({},this._bookmark||{},this.options.templateOptions,{submitText:i,coords:this.formatCoords(this._source.getLatLng(),this._map.getZoom()),mode:a.join(" ")}))}this._content=t,e.default.Popup.prototype._updateContent.call(this),this._onRendered()},_onRendered:function(){if(this.options.mode===c.CREATE||this.options.mode===c.UPDATE){var t=this._contentNode.querySelector("."+this.options.templateOptions.formClass);t.querySelector("."+this.options.templateOptions.inputClass),e.default.DomEvent.on(t,"submit",this._onSubmit,this),setTimeout(this._setFocus.bind(this),250)}else this.options.mode===c.OPTIONS&&(e.default.DomEvent.delegate(this._container,"."+this.options.templateOptions.editClass,"click",this._onEditClick,this),e.default.DomEvent.delegate(this._container,"."+this.options.templateOptions.removeClass,"click",this._onRemoveClick,this),e.default.DomEvent.delegate(this._container,"."+this.options.templateOptions.cancelClass,"click",this._onCancelClick,this))},_setFocus:function(){var t=this._contentNode.querySelector("."+this.options.templateOptions.inputClass),o=2*t.value.length;t.focus(),t.setSelectionRange(o,o)},_onEditClick:function(t){e.default.DomEvent.preventDefault(t),this._map.fire("bookmark:edit",{data:this._bookmark}),this.close()},_onRemoveClick:function(t){e.default.DomEvent.preventDefault(t),this._map.fire("bookmark:remove",{data:this._bookmark}),this.close()},_onCancelClick:function(t){e.default.DomEvent.preventDefault(t),this._map.fire("bookmark:show",{data:this._bookmark}),this.close()},_getBookmarkData:function(){var t=this.options;if(t.getBookmarkData)return t.getBookmarkData.call(this);var o=this._contentNode.querySelector("."+t.templateOptions.inputClass),e=this._contentNode.querySelector("."+t.templateOptions.idInputClass);return{latlng:this._source.getLatLng(),zoom:this._map.getZoom(),name:o.value,id:e.value||a()}},_onSubmit:function(t){e.default.DomEvent.stop(t);var o=this._contentNode.querySelector("."+this.options.templateOptions.inputClass);o.classList.remove(this.options.templateOptions.inputErrorClass),""===o.value&&this.options.generateNames&&(o.value=a(this.options.generateNamesPrefix));var s=this.options.validateInput||function(){return!0};if(""!==o.value&&s.call(this,o.value)){var i=e.default.Util.extend({},this._bookmark,this._getBookmarkData()),n=this._map;this.close(),this.options.mode===c.CREATE?n.fire("bookmark:add",{data:i}):n.fire("bookmark:edited",{data:i})}else o.classList.add(this.options.templateOptions.inputErrorClass)},formatCoords:function(t,o){return this.options.formatCoords?this.options.formatCoords.call(this,t,o):[t.lat.toFixed(4),t.lng.toFixed(4),o].join(",&nbsp;")},onAdd:function(t){return this._source.on("dragend",this._onSourceMoved,this),this._source.on("dragstart",this._onSourceMoveStart,this),e.default.Popup.prototype.onAdd.call(this,t)},onRemove:function(t){this._source.off("dragend",this._onSourceMoved,this),e.default.Popup.prototype.onRemove.call(this,t)},_onSourceMoveStart:function(){this._bookmark=e.default.Util.extend(this._bookmark||{},this._getBookmarkData()),this._container.style.display="none"},_onSourceMoved:function(t){this._latlng=this._source.getLatLng(),this._container.style.display="",this._source.openPopup(),this.update()}}),_=(d=Element.prototype,k=d.matches||d.webkitMatchesSelector||d.mozMatchesSelector||d.msMatchesSelector||d.oMatchesSelector||function(t){for(var o=(this.parentNode||this.document).querySelectorAll(t),e=0,s=o.length;e<s;++e)if(o[e]==this)return!0;return!1},function(t,o){return k.call(t,o)});e.default.DomEvent.delegate=function(t,o,s,i,a){return e.default.DomEvent.on(t,s,(function(e){var s=e.target||e.srcElement;e.delegateTarget=function(t,o,e,s){for(t=e?{parentNode:t}:t,s=s||document;(t=t.parentNode)&&t!==document;){if(_(t,o))return t;if(t===s)return null}}(s,o,!0,t),e.delegateTarget&&!e.propagationStopped&&i.call(a||t,e)}))},e.default.Util._template=e.default.Util._template||s;var g=e.default.Control.extend({statics:{Storage:u,FormPopup:f},options:{localStorage:!0,storage:null,name:"leaflet-bookmarks",position:"topright",containerClass:"leaflet-bar leaflet-bookmarks-control",expandedClass:"expanded",headerClass:"bookmarks-header",listClass:"bookmarks-list",iconClass:"bookmarks-icon",iconWrapperClass:"bookmarks-icon-wrapper",listWrapperClass:"bookmarks-list-wrapper",listWrapperClassAdd:"list-with-button",wrapperClass:"bookmarks-container",addBookmarkButtonCss:"add-bookmark-button",animateClass:"bookmark-added-anim",animateDuration:150,formPopup:{popupClass:"bookmarks-popup"},bookmarkTemplate:'<li class="{{ itemClass }}" data-id="{{ data.id }}"><span class="{{ removeClass }}">&times;</span><span class="{{ nameClass }}">{{ data.name }}</span><span class="{{ coordsClass }}">{{ data.coords }}</span></li>',emptyTemplate:'<li class="{{ itemClass }} {{ emptyClass }}">{{ data.emptyMessage }}</li>',dividerTemplate:'<li class="divider"></li>',bookmarkTemplateOptions:{itemClass:"bookmark-item",nameClass:"bookmark-name",coordsClass:"bookmark-coords",removeClass:"bookmark-remove",emptyClass:"bookmarks-empty"},defaultBookmarkOptions:{editable:!0,removable:!0},title:"Bookmarks",emptyMessage:"No bookmarks yet",addBookmarkMessage:"Add new bookmark",collapseOnClick:!0,scrollOnAdd:!0,scrollDuration:1e3,popupOnShow:!0,addNewOption:!0,popupTemplate:"<div><h3>{{ name }}</h3><p>{{ latlng }}, {{ zoom }}</p></div>",getPopupContent:function(t){return s(this.options.popupTemplate,{latlng:this.formatCoords(t.latlng),name:t.name,zoom:this._map.getZoom()})}},initialize:function(t){t=t||{},this._data=[],this._list=null,this._marker=null,this._addButton=null,this._icon=null,this._isCollapsed=!0,e.default.Util.setOptions(this,t),this._storage=t.storage||(this.options.localStorage?new u(this.options.name,h):new u(this.options.name,m)),e.default.Control.prototype.initialize.call(this,this.options)},onAdd:function(t){var o=this._container=e.default.DomUtil.create("div",this.options.containerClass);return e.default.DomEvent.disableClickPropagation(o).disableScrollPropagation(o),o.innerHTML='<div class="'+this.options.headerClass+'"><span class="'+this.options.iconWrapperClass+'"><span class="'+this.options.iconClass+'"></span></span>',this._icon=o.querySelector("."+this.options.iconClass),this._icon.title=this.options.title,this._createList(this.options.bookmarks),e.default.DomUtil.create("div",this.options.wrapperClass,this._container).appendChild(this._listwrapper),this._initLayout(),e.default.DomEvent.on(o,"click",this._onClick,this).on(o,"contextmenu",e.default.DomEvent.stopPropagation),t.on("bookmark:new",this._onBookmarkAddStart,this).on("bookmark:add",this._onBookmarkAdd,this).on("bookmark:edited",this._onBookmarkEdited,this).on("bookmark:show",this._onBookmarkShow,this).on("bookmark:edit",this._onBookmarkEdit,this).on("bookmark:options",this._onBookmarkOptions,this).on("bookmark:remove",this._onBookmarkRemove,this).on("resize",this._initLayout,this),o},onRemove:function(t){t.off("bookmark:new",this._onBookmarkAddStart,this).off("bookmark:add",this._onBookmarkAdd,this).off("bookmark:edited",this._onBookmarkEdited,this).off("bookmark:show",this._onBookmarkShow,this).off("bookmark:edit",this._onBookmarkEdit,this).off("bookmark:options",this._onBookmarkOptions,this).off("bookmark:remove",this._onBookmarkRemove,this).off("resize",this._initLayout,this),this._marker&&this._marker._popup_.close(),this.options.addNewOption&&e.default.DomEvent.off(this._container.querySelector("."+this.options.addBookmarkButtonCss),"click",this._onAddButtonPressed,this),this._marker=null,this._popup=null,this._container=null},getData:function(){return this._filterBookmarksOutput(this._data)},_createList:function(t){var o=this;this._listwrapper=e.default.DomUtil.create("div",this.options.listWrapperClass,this._container),this._list=e.default.DomUtil.create("ul",this.options.listClass,this._listwrapper),e.default.DomEvent.delegate(this._list,"."+this.options.bookmarkTemplateOptions.itemClass,"click",this._onBookmarkClick,this),this._setEmptyListContent(),e.default.Util.isArray(t)?this._appendItems(t):"function"==typeof t?this._appendItems(t()):this._storage.getAllItems((function(t){return o._appendItems(t)}))},_setEmptyListContent:function(){this._list.innerHTML=s(this.options.emptyTemplate,e.default.Util.extend(this.options.bookmarkTemplateOptions,{data:{emptyMessage:this.options.emptyMessage}}))},_initLayout:function(){var t=this._map.getSize();if(this._listwrapper.style.maxHeight=Math.min(.6*t.y,t.y-100)+"px","topleft"===this.options.position&&e.default.DomUtil.addClass(this._container,"leaflet-bookmarks-to-right"),this.options.addNewOption){var o=e.default.DomUtil.create("div",this.options.addBookmarkButtonCss);null===this._addButton&&(this._listwrapper.parentNode.appendChild(o),this._addButton=o,this._listwrapper.parentNode.classList.add(this.options.listWrapperClassAdd),o.innerHTML='<span class="plus">+</span><span class="content">'+this.options.addBookmarkMessage+"</span>",e.default.DomEvent.on(o,"click",this._onAddButtonPressed,this))}},_onAddButtonPressed:function(t){e.default.DomEvent.stop(t),this.collapse(),this._map.fire("bookmark:new",{latlng:this._map.getCenter()})},_filterBookmarks:function(t){return this.options.filterBookmarks?this.options.filterBookmarks.call(this,t):t},_filterBookmarksOutput:function(t){return this.options.filterBookmarksOutput?this.options.filterBookmarksOutput.call(this,t):t},_appendItems:function(t){var o="",e=0===this._data.length;t=this._filterBookmarks(t),this._data=this._data.concat(t);for(var s=0,i=t.length;s<i;s++)o+=this._renderBookmarkItem(t[s]);if(""!==o&&(e?this._list.innerHTML=o:this._list.innerHTML+=o),this._isCollapsed){var a=this._container,n=this.options.animateClass;a.classList.add(n),window.setTimeout((function(){a.classList.remove(n)}),this.options.animateDuration)}else this._scrollToLast()},_scrollToLast:function(){var t=this._listwrapper,o=this._listwrapper.scrollTop,s=this._list.lastChild.offsetTop,i=(s-o)/(this.options.scrollDuration/62.5);e.default.Util.requestAnimFrame((function a(n){o=Math.min(o+i,s),t.scrollTop=o,o!==s&&e.default.Util.requestAnimFrame(a)}))},_renderBookmarkItem:function(t){return t.divider?s(this.options.dividerTemplate,t):(this.options.bookmarkTemplateOptions.data=this._getBookmarkDataForTemplate(t),s(this.options.bookmarkTemplate,this.options.bookmarkTemplateOptions))},_getBookmarkDataForTemplate:function(t){return this.options.getBookmarkDataForTemplate?this.options.getBookmarkDataForTemplate.call(this,t):{coords:this.formatCoords(t.latlng),name:this.formatName(t.name),zoom:t.zoom,id:t.id}},formatCoords:function(t){return this.options.formatCoords?this.options.formatCoords.call(this,t):t[0].toFixed(4)+",&nbsp;"+t[1].toFixed(4)},formatName:function(t){return this.options.formatName?this.options.formatName.call(this,t):t},expand:function(){e.default.DomUtil.addClass(this._container,this.options.expandedClass),this._isCollapsed=!1},collapse:function(){e.default.DomUtil.removeClass(this._container,this.options.expandedClass),this._isCollapsed=!0},_onClick:function(t){var o=e.default.DomUtil.hasClass(this._container,this.options.expandedClass),s=t.target||t.srcElement;if(o){if(s===this._container)return this.collapse();for(;s!==this._container;){if(e.default.DomUtil.hasClass(s,this.options.headerClass)||e.default.DomUtil.hasClass(s,this.options.listWrapperClass)){this.collapse();break}s=s.parentNode}}else this.expand()},_onBookmarkAddStart:function(t){this._marker&&this._popup.close(),this._marker=new e.default.Marker(t.latlng,{icon:this.options.icon||new e.default.Icon.Default,draggable:!0,riseOnHover:!0}).addTo(this._map),this._marker.on("popupclose",this._onPopupClosed,this),this._popup=new e.default.Control.Bookmarks.FormPopup(e.default.Util.extend(this.options.formPopup,{mode:e.default.Control.Bookmarks.FormPopup.modes.CREATE}),this._marker,this,e.default.Util.extend({},t.data,this.options.defaultBookmarkOptions)).addTo(this._map)},_onBookmarkAdd:function(t){var o=this,e=this._map;t=this._cleanBookmark(t.data),this._storage.setItem(t.id,t,(function(t){e.fire("bookmark:saved",{data:t}),o._appendItems([t])})),this._showBookmark(t)},_onBookmarkEdited:function(t){var o=this,e=this._map,s=this._cleanBookmark(t.data);this._storage.setItem(s.id,s,(function(t){e.fire("bookmark:saved",{data:t});var i=o._data;o._data=[];for(var a=0,n=i.length;a<n;a++)i[a].id===s.id&&i.splice(a,1,s);o._appendItems(i)})),this._showBookmark(s)},_cleanBookmark:function(t){return e.default.Util.isArray(t.latlng)||(t.latlng=[t.latlng.lat,t.latlng.lng]),t},_onPopupClosed:function(t){this._map.removeLayer(this._marker),this._marker=null,this._popup=null},_getBookmark:function(t){for(var o=0,e=this._data.length;o<e;o++)if(this._data[o].id===t)return this._data[o];return null},_onBookmarkShow:function(t){this._gotoBookmark(t.data)},_onBookmarkEdit:function(t){this._editBookmark(t.data)},_onBookmarkRemove:function(t){this._removeBookmark(t.data)},_onBookmarkOptions:function(t){this._bookmarkOptions(t.data)},_bookmarkOptions:function(t){var o=e.default.latLng(t.latlng),s=this._marker=this._createMarker(o,t);this._popup=new e.default.Control.Bookmarks.FormPopup(e.default.Util.extend(this.options.formPopup,{mode:e.default.Control.Bookmarks.FormPopup.modes.OPTIONS}),s,this,t).addTo(this._map)},_editBookmark:function(t){var o=e.default.latLng(t.latlng),s=this._marker=this._createMarker(o,t);s.dragging.enable(),this._popup=new e.default.Control.Bookmarks.FormPopup(e.default.Util.extend(this.options.formPopup,{mode:e.default.Control.Bookmarks.FormPopup.modes.UPDATE}),s,this,t).addTo(this._map)},_getOnRemoveHandler:function(t,o){return function(e){e.data.id===t.id&&(o.clearAllEventListeners(),o._popup_&&o._popup_.close(),this.removeLayer(o))}},_createMarker:function(t,o){var s=this,i=new e.default.Marker(t,{icon:this.options.icon||new e.default.Icon.Default,riseOnHover:!0}).addTo(this._map),a=this._getOnRemoveHandler(o,i);return this._map.on("bookmark:removed",a,this._map),i.on("popupclose",(function(){return s._map.removeLayer(s)})).on("remove",(function(){return s._map.off("bookmark:removed",a)})),i},_showBookmark:function(t){this._marker&&this._marker._popup_.close();var o=e.default.latLng(t.latlng),s=this._createMarker(o,t),i=new e.default.Control.Bookmarks.FormPopup(e.default.Util.extend(this.options.formPopup,{mode:e.default.Control.Bookmarks.FormPopup.modes.SHOW}),s,this,t);this.options.popupOnShow&&i.addTo(this._map),this._popup=i,this._marker=s},_gotoBookmark:function(t){this._map.setView(t.latlng,t.zoom),this._showBookmark(t)},_removeBookmark:function(t){var o=this,e=function(e){if(!e)return o._showBookmark(t);o._data.splice(o._data.indexOf(t),1),o._storage.removeItem(t.id,(function(t){o._onBookmarkRemoved(t)}))};"function"==typeof this.options.onRemove?this.options.onRemove(t,e):e(!0)},_onBookmarkRemoved:function(t){var o=this,s=this._list.querySelector("."+this.options.bookmarkTemplateOptions.itemClass+"[data-id='"+t.id+"']");this._map.fire("bookmark:removed",{data:t}),s&&(e.default.DomUtil.setOpacity(s,0),setTimeout((function(){s.parentNode&&s.parentNode.removeChild(s),0===o._data.length&&o._setEmptyListContent()}),250))},_getPopupContent:function(t){return this.options.getPopupContent?this.options.getPopupContent.call(this,t):JSON.stringify(t)},_onBookmarkClick:function(t){var o=this._getBookmarkFromListItem(t.delegateTarget);o&&(e.default.DomEvent.stopPropagation(t),e.default.DomUtil.hasClass(t.target||t.srcElement,this.options.bookmarkTemplateOptions.removeClass)?this._removeBookmark(o):(this._map.fire("bookmark:show",{data:o}),this.options.collapseOnClick&&this.collapse()))},_getBookmarkFromListItem:function(t){return this.options.getBookmarkFromListItem?this.options.getBookmarkFromListItem.call(this,t):this._getBookmark(t.getAttribute("data-id"))},bookmarkToFeature:function(t){var o=this._getBookmarkCoords(t);return delete(t=JSON.parse(JSON.stringify(t))).latlng,e.default.GeoJSON.getFeature({feature:{type:"Feature",id:t.id,properties:t}},{type:"Point",coordinates:o})},_getBookmarkCoords:function(t){return t.latlng instanceof e.default.LatLng?[t.latlng.lat,t.latlng.lng]:t.latlng.reverse()},fromGeoJSON:function(t){for(var o=[],e=0,s=t.features.length;e<s;e++){var i=t.features[e];i.properties.divider||(i.properties.latlng=i.geometry.coordinates.concat().reverse()),o.push(i.properties)}return o},toGeoJSON:function(){var t=this;return{type:"FeatureCollection",features:function(o){for(var e=[],s=0,i=o.length;s<i;s++)o[s].divider||e.push(t.bookmarkToFeature(o[s]));return e}(this._data)}}});return e.default.Control.Bookmarks=g,g}));
//# sourceMappingURL=index.min.js.map
