// scripts.js
function loadScripts() {
  const scripts = [
    "./assets/ejs@3.1.10/ejs/ejs.min.js",
    "./assets/jquery@2.2.4/jquery/dist/jquery.min.js", // 1. jQuery
    "./assets/jquerySelect2/dist/js/select2.full.min.js",
    "./assets/bootstrap-3.3.6/dist/js/bootstrap.min.js", // 2. Bootstrap
    // "./assets/bootstrap@5.3.0/bootstrap/dist/js/bootstrap.bundle.min.js",
    // "./assets/bootstrap@5.3.0/@popperjs/core/dist/umd/popper.min.js",
    "./assets/leaflet@1.9.4/dist/leaflet.js", // 3. Leaflet
    "./assets/leaflet-hash/dist/leaflet-hash.min.js",
    "./assets/leaflet-bookmarks/dist/index.min.js",
    "./assets/leaflet-minimap/dist/Control.MiniMap.min.js",
    "./assets/leaflet-compass/dist/leaflet-compass.min.js",
    "./assets/leaflet-ruler/src/leaflet-ruler.js",
    "./assets/leaflet.browser.print/dist/leaflet.browser.print.js",
    "./assets/Leaflet.NavBar/src/Leaflet.NavBar.js",
    "./assets/leaflet-toolbar/dist/leaflet.toolbar.js",
    "./assets/leaflet.locatecontrol/dist/L.Control.Locate.min.js",
    "./assets/LeafletPlugins/sidebar/js/leaflet-sidebar.js", // 3.1 Leafletsidebar
    "./assets/bootstrap-table@1.20.2/bootstrap-table/dist/bootstrap-table.min.js", // 4. Bootstrap Table
    "./assets/d3@7.8.2/d3/dist/d3.min.js", // 5. D3.js
    "./assets/c3@0.7.20/c3/c3.min.js", // 6. C3.js
   // "./assets/esri-leaflet@3.0.5/esri-leaflet/dist/esri-leaflet.js", // 7. Esri Leaflet
    "./assets/geocoder/leaflet-control-geocoder/leaflet-control-geocoder/dist/Control.Geocoder.js", // 8. Esri Leaflet Geocoder
    "./assets/alasql@4.6.4/alasql/dist/alasql.min.js", // 9. AlaSQL
    "./assets/jQuery-QueryBuilder@3.0.0/jQuery-QueryBuilder/dist/js/query-builder.standalone.js", // 10. QueryBuilder
    "./assets/jspdf@2.5.1/jspdf/dist/jspdf.umd.min.js", // 11. jsPDF
    "./assets/jspdf-autotable@3.5.23/jspdf-autotable/dist/jspdf.plugin.autotable.min.js", // 12. jsPDF-AutoTable
    "./assets/LeafletDraw/dist/leaflet.draw.js",  
    "./assets/LeafletMeasure/leaflet-measure/dist/leaflet-measure.fa.js",
    "./assets/Leaflet.bouncemarker/bouncemarker.js",
    "./assets/coordinatedimagepreview/coordinatedimagepreview.js",
    "./assets/lightbox2/dist/js/lightbox.min.js",
    "./assets/Leaflet.label/dist/leaflet.label-src.js",
    "./assets/leaflet-search/dist/leaflet-search.src.js"

  ];
  
    const loadScript = (src) => {
      return new Promise((resolve, reject) => {
        const script = document.createElement("script");
        script.src = src;
        script.async = false;
        script.onload = () => {
          resolve();
        };
        script.onerror = () => {
          console.error(` خطا در لود ${src}`);
          reject(new Error(`خطا در لود ${src}`));
        };
        document.body.appendChild(script);
      });
    };
  
    return scripts.reduce((promise, src) => {
      return promise.then(() => loadScript(src));
    }, Promise.resolve());
  }
  
  export { loadScripts };