<div class="modal fade" id="aboutModal" tabindex="-1" role="dialog">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <button class="close" type="button" data-dismiss="modal" aria-hidden="true">×</button>
        <h4 class="modal-title">نحوه استفاده از برنامه</h4>
      </div>
      <div class="modal-body">
        <ul class="nav nav-tabs" id="aboutTabs">
          <li class="active"><a href="#about-tab" data-toggle="tab"><i class="fa fa-question-circle"></i> درباره پروژه</a></li>
          <li><a href="#configuration-tab" data-toggle="tab"><i class="fa fa-gear"></i> پیکره بندی</a></li>
        </ul>
        <div class="tab-content" id="aboutTabsContent">
          <div class="tab-pane fade active in" id="about-tab">
            <p>این برنامه با استفاده از لیفلت جاواسکریپت طراحی شده و داده‌های مکانی را از ژئوجیسون دریافت می‌کند.</p>
            <div class="panel panel-primary">
              <div class="panel-heading">ویژگی های برنامه</div>
              <ul class="list-group">
                <li class="list-group-item">قالب انعطاف‌ و ساخته‌شده بر اساس کامپوننت‌های متن‌باز فعال</li>
                <li class="list-group-item">بخش‌هایی از برنامه سمت کاربر و بخش‌هایی سمت سرور طراحی شده است.</li>
                <li class="list-group-item">ساخته‌شده بر اساس چارچوب UI محبوب <a href="http://getbootstrap.com/" target="_blank">Bootstrap</a></li>
                <li class="list-group-item">نقشه‌ها از طریق <a href="http://leafletjs.com/" target="_blank">Leaflet</a>،کتابخانه پیشرو نفشه های تحت وب در جاوااسکریپت متن‌باز</li>
                <li class="list-group-item">جدول داده‌های تعاملی با قابلیت‌های مرتب‌سازی، جستجو، تغییر ستون‌ها و خروجی داده‌ها از طریق پلاگین <a href="http://bootstrap-table.wenzhixin.net.cn/" target="_blank">Bootstrap Table</a></li>
                <li class="list-group-item">از کتابخانه <a href="https://jquery.com/" target="_blank">jQuery</a> برای بخش‌هایی از برنامه استفاده شده است.</li>
                <li class="list-group-item">نمودارهای پیشرفته از طریق <a href="http://c3js.org/" target="_blank">C3.js</a>، کتابخانه نمودارهای قابل استفاده مجدد مبتنی بر D3</li>
              </ul>
            </div>
          </div>
          <div class="tab-pane fade" id="configuration-tab">
            <p>این برنامه برای داده‌های حوزه 223 انصارالمهدی تهیه شده و بطور کلی ویژگی‌های زیر را دارد。</p>
            <ol>
              <li>این برنامه از پنل ساید بار برای استفاده از ابزارهای نقشه استفاده می‌کند.</li>
              <li>در هر پنل ساید بار یک کار خاص انجام می‌شود مانند نمایش نقشه و ابزارهای مختصات و غیره.</li>
              <li>روی نقشه اطلاعات مختصات محل موس و مینی مپ برای موقعیت کلی ارائه شده است.</li>
              <li>سمت چپ نقشه ابزارهای دم دستی مانند زوم و جابجایی روی نقشه و غیره ارائه شده است.</li>
              <li>هر کاربر با رمز خود وارد می‌شود و پنل کاربر اطلاعات کاربر را نشان می‌دهد.</li>
            </ol>
            <p><strong>موارد مهم</strong></p>
            <ul>
              <li>روی هر عارضه نقشه کلیک کنید فرم اطلاعات توصیفی آن عارضه باز می‌شود.</li>
              <li>برای هر لایه یک جدول توصیفی می‌توانید باز کنید که اطلاعات را نشان می‌دهد.</li>
              <li>کنار جدول توصیفی دو آیکون هست؛ یکی برای اطلاعات توصیفی و دیگری برای زوم روی عارضه.</li>
              <h5>برنامه نویس : حسنوند</h5>
             </ul>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">بستن</button>
      </div>
    </div>
  </div>
</div>