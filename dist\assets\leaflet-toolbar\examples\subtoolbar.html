<!DOCTYPE html>
<html>
<head>
 
    <link rel="stylesheet" type="text/css" href="../leaflet@1.9.4/dist/leaflet.css" />
    <link rel="stylesheet" href="../dist/leaflet.toolbar.css"/>
    <script src="../leaflet@1.9.4/dist/leaflet-src.js"></script>
    <script src="../dist/leaflet.toolbar-src.js"></script>
    <style>
        html, body, #map { margin: 0; height: 100%; width: 100%; }
    </style>
</head>
<body>
    <div id="map"></div>
    <script>
        // ایجاد نقشه
        var map = L.map('map').setView([41.7896, -87.5996], 15);

        // اضافه کردن لایه نقشه
        L.tileLayer("http://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png", {
            attribution: '<a href="http://openstreetmap.org/copyright">OpenStreetMap Contributors</a>'
        }).addTo(map);

        // ایجاد Action برای Zoom In
        var ZoomInAction = L.Toolbar2.Action.extend({
            options: {
                toolbarIcon: {
                    html: '+', // آیکون Zoom In
                    tooltip: 'Zoom In' // متن راهنما
                }
            },
            addHooks: function () {
                map.zoomIn(); // افزایش زوم
            }
        });

        // ایجاد Action برای Zoom Out
        var ZoomOutAction = L.Toolbar2.Action.extend({
            options: {
                toolbarIcon: {
                    html: '-', // آیکون Zoom Out
                    tooltip: 'Zoom Out' // متن راهنما
                }
            },
            addHooks: function () {
                map.zoomOut(); // کاهش زوم
            }
        });

        // اضافه کردن تولبار به نقشه
        new L.Toolbar2.Control({
            actions: [ZoomInAction, ZoomOutAction] // اضافه کردن Zoom In و Zoom Out به تولبار
        }).addTo(map);
    </script>
</body>
</html>