{"env": {"es6": true, "node": true}, "parserOptions": {"ecmaVersion": 2020, "sourceType": "script"}, "plugins": ["import", "node", "promise"], "rules": {"import/no-unresolved": [2, {"commonjs": true}], "import/named": 2, "import/default": 2, "import/namespace": 2, "import/no-restricted-paths": 0, "import/no-absolute-path": 2, "import/no-dynamic-require": 0, "import/no-internal-modules": 0, "import/no-webpack-loader-syntax": 2, "import/no-self-import": 2, "import/no-cycle": 2, "import/no-useless-path-segments": 2, "import/no-relative-parent-imports": 2, "import/no-unused-modules": 0, "import/export": 2, "import/no-named-as-default": 1, "import/no-named-as-default-member": 1, "import/no-deprecated": 0, "import/no-extraneous-dependencies": [2, {"devDependencies": false, "optionalDependencies": false, "peerDependencies": false, "bundledDependencies": false}], "import/no-mutable-exports": 2, "import/unambiguous": 0, "import/no-amd": 2, "import/no-nodejs-modules": 0, "import/first": 2, "import/exports-last": 2, "import/no-duplicates": 2, "import/no-namespace": 2, "import/extensions": 0, "import/order": 1, "import/newline-after-import": 2, "import/prefer-default-export": 1, "import/max-dependencies": [0], "import/no-unassigned-import": 1, "import/no-named-default": 0, "import/no-default-export": 0, "import/no-named-export": 0, "import/no-anonymous-default-export": 0, "import/group-exports": 2, "dynamic-import-chunkname": 0, "comma-dangle": [2, "never"], "no-cond-assign": [2, "always"], "no-console": 1, "no-constant-condition": 2, "no-control-regex": 2, "no-debugger": 2, "no-dupe-args": 2, "no-dupe-keys": 2, "no-duplicate-case": 2, "no-empty-character-class": 2, "no-empty": 2, "no-ex-assign": 1, "no-extra-boolean-cast": 1, "no-extra-parens": [2, "functions"], "no-extra-semi": 2, "no-func-assign": 1, "no-inner-declarations": [2, "both"], "no-invalid-regexp": 2, "no-irregular-whitespace": 2, "no-negated-in-lhs": 2, "no-obj-calls": 2, "no-regex-spaces": 2, "no-sparse-arrays": 2, "no-unexpected-multiline": 2, "no-unreachable": 2, "use-isnan": 2, "valid-typeof": 2, "accessor-pairs": 2, "block-scoped-var": 2, "consistent-return": 0, "curly": [2, "all"], "default-case": 2, "dot-location": [2, "property"], "dot-notation": 2, "eqeqeq": [2, "always", {"null": "ignore"}], "guard-for-in": 1, "no-alert": 1, "no-caller": 2, "no-case-declarations": 0, "no-div-regex": 2, "no-else-return": 2, "no-labels": 2, "no-empty-pattern": 2, "no-eval": 2, "no-extend-native": 2, "no-extra-bind": 2, "no-fallthrough": 2, "no-floating-decimal": 2, "no-implicit-coercion": [2, {"boolean": true, "number": true, "string": true}], "no-implied-eval": 2, "no-invalid-this": 2, "no-iterator": 2, "no-lone-blocks": 2, "no-loop-func": 2, "no-magic-numbers": 0, "no-multi-spaces": 2, "no-multi-str": 2, "no-native-reassign": 2, "no-new-func": 2, "no-new-wrappers": 2, "no-new": 2, "no-octal-escape": 2, "no-octal": 2, "no-param-reassign": 2, "no-process-env": 0, "no-proto": 2, "no-redeclare": [2, {"builtinGlobals": true}], "no-return-assign": 2, "no-script-url": 2, "no-self-compare": 2, "no-sequences": 2, "no-throw-literal": 1, "no-unused-expressions": 2, "no-useless-call": 2, "no-useless-concat": 2, "no-void": 2, "no-with": 2, "radix": 2, "vars-on-top": 2, "wrap-iife": [2, "outside"], "yoda": 2, "strict": [2, "safe"], "no-catch-shadow": 2, "no-delete-var": 2, "no-label-var": 2, "no-shadow-restricted-names": 2, "no-shadow": [2, {"builtinGlobals": true}], "no-undef-init": 2, "no-undef": 2, "no-unused-vars": [2, {"vars": "all", "args": "after-used"}], "no-use-before-define": 2, "callback-return": ["error", ["done", "cb"]], "handle-callback-err": 2, "no-new-require": 2, "no-path-concat": 2, "no-process-exit": 1, "no-sync": 1, "array-bracket-spacing": [2, "never"], "block-spacing": [2, "always"], "brace-style": [2, "1tbs", {"allowSingleLine": false}], "camelcase": [2, {"properties": "always"}], "comma-spacing": [2, {"before": false, "after": true}], "comma-style": [2, "first", {"exceptions": {"ArrayExpression": true, "ArrayPattern": true, "ArrowFunctionExpression": true, "CallExpression": true, "FunctionDeclaration": true, "FunctionExpression": true, "ImportDeclaration": true, "ObjectExpression": true, "ObjectPattern": true, "NewExpression": true}}], "computed-property-spacing": [2, "never"], "consistent-this": [2, "that"], "eol-last": 2, "func-names": 1, "func-style": [1, "expression", {"allowArrowFunctions": true}], "id-length": 1, "key-spacing": [2, {"beforeColon": false, "afterColon": true}], "linebreak-style": [2, "unix"], "new-cap": 2, "new-parens": 2, "newline-after-var": [2, "always"], "no-array-constructor": 2, "no-bitwise": 2, "no-continue": 2, "no-lonely-if": 2, "no-mixed-spaces-and-tabs": 2, "no-multiple-empty-lines": [1, {"max": 2, "maxEOF": 1}], "no-negated-condition": 1, "no-nested-ternary": 2, "no-new-object": 2, "no-plusplus": 2, "no-spaced-func": 2, "no-ternary": 0, "no-trailing-spaces": [2, {"skipBlankLines": false}], "no-underscore-dangle": 2, "no-unneeded-ternary": 2, "object-curly-spacing": [2, "never"], "one-var": [2, {"var": "always", "let": "always"}], "operator-assignment": [2, "always"], "operator-linebreak": [2, "after"], "quote-props": 2, "quotes": [2, "single", "avoid-escape"], "semi-spacing": 2, "semi": [2, "always"], "keyword-spacing": [2, {"before": true, "after": true}], "space-before-blocks": [2, "always"], "space-before-function-paren": [2, "never"], "space-in-parens": [2, "never"], "space-infix-ops": 2, "space-unary-ops": [2, {"words": true, "nonwords": false}], "wrap-regex": 2, "arrow-parens": [2, "as-needed"], "arrow-spacing": [2, {"before": true, "after": true}], "constructor-super": 2, "generator-star-spacing": [2, {"before": false, "after": true}], "no-confusing-arrow": 1, "no-class-assign": 2, "no-const-assign": 2, "no-dupe-class-members": 1, "no-this-before-super": 2, "no-var": 1, "object-shorthand": [2, "always"], "prefer-arrow-callback": 1, "prefer-const": 1, "prefer-spread": 1, "prefer-template": 2, "require-yield": 2}}