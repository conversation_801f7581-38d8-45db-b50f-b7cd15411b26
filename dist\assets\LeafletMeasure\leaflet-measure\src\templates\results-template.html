<div class="group">
  <p class="lastpoint heading">${ __('lastPoint') }</p>
  <p>{{ model.lastCoord.dms.y }} <span class="coorddivider">/</span> {{ model.lastCoord.dms.x }}</p>
  <p>{{ numberFormat(model.lastCoord.dd.y, 6) }} <span class="coorddivider">/</span> {{ numberFormat(model.lastCoord.dd.x, 6) }}</p>
</div>
<% if (model.pointCount > 1) { %>
<div class="group">
  <p><span class="heading">${ __('pathDistance') }</span> {{ model.lengthDisplay }}</p>
</div>
<% } %>
<% if (model.pointCount > 2) { %>
<div class="group">
  <p><span class="heading">${ __('area') }</span> {{ model.areaDisplay }}</p>
</div>
<% } %>
