{"name": "d3-selection", "version": "1.4.2", "description": "Data-driven DOM manipulation: select elements and join them to data.", "keywords": ["d3", "d3-module", "dom", "selection", "data-join"], "homepage": "https://d3js.org/d3-selection/", "license": "BSD-3-<PERSON><PERSON>", "author": {"name": "<PERSON>", "url": "https://bost.ocks.org/mike"}, "main": "dist/d3-selection.js", "unpkg": "dist/d3-selection.min.js", "jsdelivr": "dist/d3-selection.min.js", "module": "src/index.js", "repository": {"type": "git", "url": "https://github.com/d3/d3-selection.git"}, "files": ["dist/**/*.js", "src/**/*.js"], "scripts": {"pretest": "rollup -c", "test": "tape 'test/**/*-test.js' && eslint src", "prepublishOnly": "rm -rf dist && yarn test", "postpublish": "git push && git push --tags && cd ../d3.github.com && git pull && cp ../${npm_package_name}/dist/${npm_package_name}.js ${npm_package_name}.v${npm_package_version%%.*}.js && cp ../${npm_package_name}/dist/${npm_package_name}.min.js ${npm_package_name}.v${npm_package_version%%.*}.min.js && git add ${npm_package_name}.v${npm_package_version%%.*}.js ${npm_package_name}.v${npm_package_version%%.*}.min.js && git commit -m \"${npm_package_name} ${npm_package_version}\" && git push && cd - && zip -j dist/${npm_package_name}.zip -- LICENSE README.md dist/${npm_package_name}.js dist/${npm_package_name}.min.js"}, "sideEffects": false, "devDependencies": {"eslint": "6", "jsdom": "15", "rollup": "1", "rollup-plugin-terser": "5", "tape": "4"}}