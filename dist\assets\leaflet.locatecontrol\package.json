{"name": "leaflet.locatecontrol", "version": "0.83.1", "homepage": "https://github.com/domoritz/leaflet-locatecontrol", "description": "A useful control to geolocate the user with many options. Used by osm.org and mapbox among many others.", "main": "dist/L.Control.Locate.min.js", "unpkg": "dist/L.Control.Locate.min.js", "jsdelivr": "dist/L.Control.Locate.min.js", "module": "dist/L.Control.Locate.esm.js", "types": "dist/L.Control.Locate.d.ts", "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "repository": {"type": "git", "url": "**************:domoritz/leaflet-locatecontrol.git"}, "keywords": ["leaflet", "locate", "plugin"], "license": "MIT", "readmeFilename": "README.md", "scripts": {"build": "grunt", "bump:minor": "grunt bump-only:minor && grunt && grunt bump-commit", "bump:patch": "grunt bump-only:patch && grunt && grunt bump-commit", "lint": "eslint && stylelint {**/style.css,**/*.scss} && prettier --check .", "lint:fix": "eslint --fix && stylelint --fix {**/style.css,**/*.scss} && prettier --write .", "start": "grunt connect"}, "devDependencies": {"@rollup/plugin-commonjs": "^28.0.2", "@rollup/plugin-node-resolve": "^16.0.0", "eslint": "^9.17.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "grunt": "^1.6.1", "grunt-bump": "0.8.0", "grunt-contrib-connect": "^4.0.0", "grunt-contrib-copy": "^1.0.0", "grunt-contrib-uglify": "^5.2.2", "grunt-rollup": "^12.0.0", "grunt-sass": "^3.1.0", "leaflet": "^1.9.4", "prettier": "^3.4.2", "rollup": "^4.29.1", "sass": "^1.83.0", "stylelint": "^16.12.0", "stylelint-config-prettier-scss": "^1.0.0", "stylelint-config-recommended-scss": "^14.1.0", "stylelint-prettier": "^5.0.2", "stylelint-scss": "^6.8.1", "typescript": "^5.7.2"}}