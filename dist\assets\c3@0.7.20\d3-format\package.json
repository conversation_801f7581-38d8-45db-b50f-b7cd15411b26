{"name": "d3-format", "version": "1.4.5", "description": "Format numbers for human consumption.", "keywords": ["d3", "d3-module", "format", "localization"], "homepage": "https://d3js.org/d3-format/", "license": "BSD-3-<PERSON><PERSON>", "author": {"name": "<PERSON>", "url": "http://bost.ocks.org/mike"}, "main": "dist/d3-format.js", "unpkg": "dist/d3-format.min.js", "jsdelivr": "dist/d3-format.min.js", "module": "src/index.js", "repository": {"type": "git", "url": "https://github.com/d3/d3-format.git"}, "files": ["dist/**/*.js", "src/**/*.js", "locale/*.json"], "scripts": {"pretest": "rollup -c", "test": "tape 'test/**/*-test.js' && eslint src test", "prepublishOnly": "rm -rf dist && yarn test", "postpublish": "git push && git push --tags && cd ../d3.github.com && git pull && cp ../${npm_package_name}/dist/${npm_package_name}.js ${npm_package_name}.v${npm_package_version%%.*}.js && cp ../${npm_package_name}/dist/${npm_package_name}.min.js ${npm_package_name}.v${npm_package_version%%.*}.min.js && git add ${npm_package_name}.v${npm_package_version%%.*}.js ${npm_package_name}.v${npm_package_version%%.*}.min.js && git commit -m \"${npm_package_name} ${npm_package_version}\" && git push && cd - && zip -j dist/${npm_package_name}.zip -- LICENSE README.md dist/${npm_package_name}.js dist/${npm_package_name}.min.js"}, "sideEffects": ["./src/defaultLocale.js"], "devDependencies": {"eslint": "7", "rollup": "2", "rollup-plugin-terser": "7", "tape": "5"}}