# @turf/invariant

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## getCoord

Unwrap a coordinate from a Point Feature, Geometry or a single coordinate.

**Parameters**

-   `coord` **([Array](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array)&lt;[number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)> | [Geometry](https://tools.ietf.org/html/rfc7946#section-3.1)&lt;[Point](https://tools.ietf.org/html/rfc7946#section-3.1.2)> | [Feature](https://tools.ietf.org/html/rfc7946#section-3.2)&lt;[Point](https://tools.ietf.org/html/rfc7946#section-3.1.2)>)** GeoJSON Point or an Array of numbers

**Examples**

```javascript
var pt = turf.point([10, 10]);

var coord = turf.getCoord(pt);
//= [10, 10]
```

Returns **[Array](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array)&lt;[number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)>** coordinates

## getCoords

Unwrap coordinates from a Feature, Geometry Object or an Array

**Parameters**

-   `coords` **([Array](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array)&lt;any> | [Geometry](https://tools.ietf.org/html/rfc7946#section-3.1) \| [Feature](https://tools.ietf.org/html/rfc7946#section-3.2))** Feature, Geometry Object or an Array

**Examples**

```javascript
var poly = turf.polygon([[[119.32, -8.7], [119.55, -8.69], [119.51, -8.54], [119.32, -8.7]]]);

var coords = turf.getCoords(poly);
//= [[[119.32, -8.7], [119.55, -8.69], [119.51, -8.54], [119.32, -8.7]]]
```

Returns **[Array](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array)&lt;any>** coordinates

## containsNumber

Checks if coordinates contains a number

**Parameters**

-   `coordinates` **[Array](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array)&lt;any>** GeoJSON Coordinates

Returns **[boolean](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean)** true if Array contains a number

## geojsonType

Enforce expectations about types of GeoJSON objects for Turf.

**Parameters**

-   `value` **[GeoJSON](https://tools.ietf.org/html/rfc7946#section-3)** any GeoJSON object
-   `type` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** expected GeoJSON type
-   `name` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** name of calling function


-   Throws **[Error](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Error)** if value is not the expected type.

## featureOf

Enforce expectations about types of [Feature](https://tools.ietf.org/html/rfc7946#section-3.2) inputs for Turf.
Internally this uses [geojsonType](#geojsontype) to judge geometry types.

**Parameters**

-   `feature` **[Feature](https://tools.ietf.org/html/rfc7946#section-3.2)** a feature with an expected geometry type
-   `type` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** expected GeoJSON type
-   `name` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** name of calling function


-   Throws **[Error](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Error)** error if value is not the expected type.

## collectionOf

Enforce expectations about types of [FeatureCollection](https://tools.ietf.org/html/rfc7946#section-3.3) inputs for Turf.
Internally this uses [geojsonType](#geojsontype) to judge geometry types.

**Parameters**

-   `featureCollection` **[FeatureCollection](https://tools.ietf.org/html/rfc7946#section-3.3)** a FeatureCollection for which features will be judged
-   `type` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** expected GeoJSON type
-   `name` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** name of calling function


-   Throws **[Error](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Error)** if value is not the expected type.

## getGeom

Get Geometry from Feature or Geometry Object

**Parameters**

-   `geojson` **([Feature](https://tools.ietf.org/html/rfc7946#section-3.2) \| [Geometry](https://tools.ietf.org/html/rfc7946#section-3.1))** GeoJSON Feature or Geometry Object

**Examples**

```javascript
var point = {
  "type": "Feature",
  "properties": {},
  "geometry": {
    "type": "Point",
    "coordinates": [110, 40]
  }
}
var geom = turf.getGeom(point)
//={"type": "Point", "coordinates": [110, 40]}
```

-   Throws **[Error](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Error)** if geojson is not a Feature or Geometry Object

Returns **([Geometry](https://tools.ietf.org/html/rfc7946#section-3.1) | null)** GeoJSON Geometry Object

## getGeomType

Get Geometry Type from Feature or Geometry Object

-   Throws **[Error](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Error)** **DEPRECATED** in v5.0.0 in favor of getType

## getType

Get GeoJSON object's type, Geometry type is prioritize.

**Parameters**

-   `geojson` **[GeoJSON](https://tools.ietf.org/html/rfc7946#section-3)** GeoJSON object
-   `name` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** name of the variable to display in error message (optional, default `"geojson"`)

**Examples**

```javascript
var point = {
  "type": "Feature",
  "properties": {},
  "geometry": {
    "type": "Point",
    "coordinates": [110, 40]
  }
}
var geom = turf.getType(point)
//="Point"
```

Returns **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** GeoJSON type

<!-- This file is automatically generated. Please don't edit it directly:
if you find an error, edit the source file (likely index.js), and re-run
./scripts/generate-readmes in the turf project. -->

---

This module is part of the [Turfjs project](http://turfjs.org/), an open source
module collection dedicated to geographic algorithms. It is maintained in the
[Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create
PRs and issues.

### Installation

Install this module individually:

```sh
$ npm install @turf/invariant
```

Or install the Turf module that includes it as a function:

```sh
$ npm install @turf/turf
```
