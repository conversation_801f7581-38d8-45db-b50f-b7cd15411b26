<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for All files</title>
    <meta charset="utf-8">

    <link rel="stylesheet" href="prettify.css">

    <style>
        body, html {
            margin:0; padding: 0;
        }
        body {
            font-family: Helvetica Neue, Helvetica,Arial;
            font-size: 10pt;
        }
        div.header, div.footer {
            background: #eee;
            padding: 1em;
        }
        div.header {
            z-index: 100;
            position: fixed;
            top: 0;
            border-bottom: 1px solid #666;
            width: 100%;
        }
        div.footer {
            border-top: 1px solid #666;
        }
        div.body {
            margin-top: 10em;
        }
        div.meta {
            font-size: 90%;
            text-align: center;
        }
        h1, h2, h3 {
            font-weight: normal;
        }
        h1 {
            font-size: 12pt;
        }
        h2 {
            font-size: 10pt;
        }
        pre {
            font-family: Consolas, Menlo, Monaco, monospace;
            margin: 0;
            padding: 0;
            line-height: 14px;
            font-size: 14px;
            -moz-tab-size: 2;
            -o-tab-size:  2;
            tab-size: 2;
        }

        div.path { font-size: 110%; }
        div.path a:link, div.path a:visited { color: #000; }
        table.coverage { border-collapse: collapse; margin:0; padding: 0 }

        table.coverage td {
            margin: 0;
            padding: 0;
            color: #111;
            vertical-align: top;
        }
        table.coverage td.line-count {
            width: 50px;
            text-align: right;
            padding-right: 5px;
        }
        table.coverage td.line-coverage {
            color: #777 !important;
            text-align: right;
            border-left: 1px solid #666;
            border-right: 1px solid #666;
        }

        table.coverage td.text {
        }

        table.coverage td span.cline-any {
            display: inline-block;
            padding: 0 5px;
            width: 40px;
        }
        table.coverage td span.cline-neutral {
            background: #eee;
        }
        table.coverage td span.cline-yes {
            background: #b5d592;
            color: #999;
        }
        table.coverage td span.cline-no {
            background: #fc8c84;
        }

        .cstat-yes { color: #111; }
        .cstat-no { background: #fc8c84; color: #111; }
        .fstat-no { background: #ffc520; color: #111 !important; }
        .cbranch-no { background:  yellow !important; color: #111; }

        .cstat-skip { background: #ddd; color: #111; }
        .fstat-skip { background: #ddd; color: #111 !important; }
        .cbranch-skip { background: #ddd !important; color: #111; }

        .missing-if-branch {
            display: inline-block;
            margin-right: 10px;
            position: relative;
            padding: 0 4px;
            background: black;
            color: yellow;
        }

        .skip-if-branch {
            display: none;
            margin-right: 10px;
            position: relative;
            padding: 0 4px;
            background: #ccc;
            color: white;
        }

        .missing-if-branch .typ, .skip-if-branch .typ {
            color: inherit !important;
        }

        .entity, .metric { font-weight: bold; }
        .metric { display: inline-block; border: 1px solid #333; padding: 0.3em; background: white; }
        .metric small { font-size: 80%; font-weight: normal; color: #666; }

        div.coverage-summary table { border-collapse: collapse; margin: 3em; font-size: 110%; }
        div.coverage-summary td, div.coverage-summary table  th { margin: 0; padding: 0.25em 1em; border-top: 1px solid #666; border-bottom: 1px solid #666; }
        div.coverage-summary th { text-align: left; border: 1px solid #666; background: #eee; font-weight: normal; }
        div.coverage-summary th.file { border-right: none !important; }
        div.coverage-summary th.pic { border-left: none !important; text-align: right; }
        div.coverage-summary th.pct { border-right: none !important; }
        div.coverage-summary th.abs { border-left: none !important; text-align: right; }
        div.coverage-summary td.pct { text-align: right; border-left: 1px solid #666; }
        div.coverage-summary td.abs { text-align: right; font-size: 90%; color: #444; border-right: 1px solid #666; }
        div.coverage-summary td.file { text-align: right; border-left: 1px solid #666; white-space: nowrap;  }
        div.coverage-summary td.pic { min-width: 120px !important;  }
        div.coverage-summary a:link { text-decoration: none; color: #000; }
        div.coverage-summary a:visited { text-decoration: none; color: #333; }
        div.coverage-summary a:hover { text-decoration: underline; }
        div.coverage-summary tfoot td { border-top: 1px solid #666; }

        div.coverage-summary .yui3-datatable-sort-indicator, div.coverage-summary .dummy-sort-indicator {
            height: 10px;
            width: 7px;
            display: inline-block;
            margin-left: 0.5em;
        }
        div.coverage-summary .yui3-datatable-sort-indicator {
            background: url("https://yui-s.yahooapis.com/3.6.0/build/datatable-sort/assets/skins/sam/sort-arrow-sprite.png") no-repeat scroll 0 0 transparent;
        }
        div.coverage-summary .yui3-datatable-sorted .yui3-datatable-sort-indicator {
            background-position: 0 -20px;
        }
        div.coverage-summary .yui3-datatable-sorted-desc .yui3-datatable-sort-indicator {
            background-position: 0 -10px;
        }

        .high { background: #b5d592 !important; }
        .medium { background: #ffe87c !important; }
        .low { background: #fc8c84 !important; }

        span.cover-fill, span.cover-empty {
            display:inline-block;
            border:1px solid #444;
            background: white;
            height: 12px;
        }
        span.cover-fill {
            background: #ccc;
            border-right: 1px solid #444;
        }
        span.cover-empty {
            background: white;
            border-left: none;
        }
        span.cover-full {
            border-right: none !important;
        }
        pre.prettyprint {
            border: none !important;
            padding: 0 !important;
            margin: 0 !important;
        }
        .com { color: #999 !important; }
        .ignore-none { color: #999; font-weight: normal; }

    </style>
</head>
<body>
<div class="header high">
    <h1>Code coverage report for <span class="entity">All files</span></h1>
    <h2>
        
        Statements: <span class="metric">87.05% <small>(121 / 139)</small></span> &nbsp;&nbsp;&nbsp;&nbsp;
        
        
        Branches: <span class="metric">53.13% <small>(17 / 32)</small></span> &nbsp;&nbsp;&nbsp;&nbsp;
        
        
        Functions: <span class="metric">88.24% <small>(30 / 34)</small></span> &nbsp;&nbsp;&nbsp;&nbsp;
        
        
        Lines: <span class="metric">93.8% <small>(121 / 129)</small></span> &nbsp;&nbsp;&nbsp;&nbsp;
        
        Ignored: <span class="metric"><span class="ignore-none">none</span></span> &nbsp;&nbsp;&nbsp;&nbsp;
    </h2>
    <div class="path"></div>
</div>
<div class="body">
<div class="coverage-summary">
<table>
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file high" data-value="src/"><a href="src/index.html">src/</a></td>
	<td data-value="87.05" class="pic high"><span class="cover-fill" style="width: 87px;"></span><span class="cover-empty" style="width:13px;"></span></td>
	<td data-value="87.05" class="pct high">87.05%</td>
	<td data-value="139" class="abs high">(121&nbsp;/&nbsp;139)</td>
	<td data-value="53.13" class="pct medium">53.13%</td>
	<td data-value="32" class="abs medium">(17&nbsp;/&nbsp;32)</td>
	<td data-value="88.24" class="pct high">88.24%</td>
	<td data-value="34" class="abs high">(30&nbsp;/&nbsp;34)</td>
	<td data-value="93.8" class="pct high">93.8%</td>
	<td data-value="129" class="abs high">(121&nbsp;/&nbsp;129)</td>
	</tr>

</tbody>
</table>
</div>
</div>
<div class="footer">
    <div class="meta">Generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Mon Sep 11 2017 08:12:54 GMT-0700 (PDT)</div>
</div>

<script src="prettify.js"></script>

<script src="https://yui-s.yahooapis.com/3.6.0/build/yui/yui-min.js"></script>
<script>

    YUI().use('datatable', function (Y) {

        var formatters = {
          pct: function (o) {
              o.className += o.record.get('classes')[o.column.key];
              try {
                  return o.value.toFixed(2) + '%';
              } catch (ex) { return o.value + '%'; }
          },
          html: function (o) {
              o.className += o.record.get('classes')[o.column.key];
              return o.record.get(o.column.key + '_html');
          }
        },
          defaultFormatter = function (o) {
              o.className += o.record.get('classes')[o.column.key];
              return o.value;
          };

        function getColumns(theadNode) {
            var colNodes = theadNode.all('tr th'),
                cols = [],
                col;
            colNodes.each(function (colNode) {
                col = {
                    key: colNode.getAttribute('data-col'),
                    label: colNode.get('innerHTML') || ' ',
                    sortable: !colNode.getAttribute('data-nosort'),
                    className: colNode.getAttribute('class'),
                    type: colNode.getAttribute('data-type'),
                    allowHTML: colNode.getAttribute('data-html') === 'true' || colNode.getAttribute('data-fmt') === 'html'
                };
                col.formatter = formatters[colNode.getAttribute('data-fmt')] || defaultFormatter;
                cols.push(col);
            });
            return cols;
        }

        function getRowData(trNode, cols) {
            var tdNodes = trNode.all('td'),
                    i,
                    row = { classes: {} },
                    node,
                    name;
            for (i = 0; i < cols.length; i += 1) {
                name = cols[i].key;
                node = tdNodes.item(i);
                row[name] = node.getAttribute('data-value') || node.get('innerHTML');
                row[name + '_html'] = node.get('innerHTML');
                row.classes[name] = node.getAttribute('class');
                //Y.log('Name: ' + name + '; Value: ' + row[name]);
                if (cols[i].type === 'number') { row[name] = row[name] * 1; }
            }
            //Y.log(row);
            return row;
        }

        function getData(tbodyNode, cols) {
            var data = [];
            tbodyNode.all('tr').each(function (trNode) {
                data.push(getRowData(trNode, cols));
            });
            return data;
        }

        function replaceTable(node) {
            if (!node) { return; }
            var cols = getColumns(node.one('thead')),
                data = getData(node.one('tbody'), cols),
                table,
                parent = node.get('parentNode');

            table = new Y.DataTable({
                columns: cols,
                data: data,
                sortBy: 'file'
            });
            parent.set('innerHTML', '');
            table.render(parent);
        }

        Y.on('domready', function () {
            replaceTable(Y.one('div.coverage-summary table'));
            if (typeof prettyPrint === 'function') {
                prettyPrint();
            }
        });
    });
</script>
</body>
</html>
