<!-- src/templates/layer.html -->
<div id="table-container-layer<%= index %>">
    <h3 class="table-title" data-layer-index="<%= index %>" style="margin-bottom: 10px;"><%= layersConfig[index].name %></h3>
    <div id="toolbar-layer<%= index %>" style="width: 400px;">
      <div class="btn-group" role="group">
        <button type="button" class="btn btn-default filter-btn" data-toggle="modal" data-target="#filterModal">
          <i class="fa fa-filter"></i> فیلتر داده‌ها
        </button>
        <div class="btn-group">
          <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
            <i class="fa fa-cloud-download"></i> خروجی داده‌ها <span class="caret"></span>
          </button>
          <ul class="dropdown-menu">
            <li><a href="#" class="download-csv-btn"><i class="fa fa-file-text-o"></i> CSV</a></li>
            <li><a href="#" class="download-excel-btn"><i class="fa fa-file-excel-o"></i> Excel</a></li>
            <li><a href="#" class="download-pdf-btn"><i class="fa fa-file-pdf-o"></i> PDF</a></li>
          </ul>
        </div>
        <button type="button" class="btn btn-default toggle-fullscreen-btn" title="تمام‌صفحه/بازگشت">
          <i class="fa fa-expand"></i>
        </button>
        <button type="button" class="btn btn-default close-table-btn" title="بستن جدول">
          <i class="fa fa-times"></i>
        </button>
      </div>
      <span id="feature-count-layer<%= index %>" class="text-muted" style="padding-left: 15px;"></span>
    </div>
    <table id="table-<%= index %>"></table>
  </div>