@font-face {
  font-family: Nahid;
  src: url('nahid-font-v0.3.0/Farsi-Digits-Without-Latin/Nahid-FD-WOL.eot');
  src: url('nahid-font-v0.3.0/Farsi-Digits-Without-Latin/Nahid-FD-WOL.eot?#iefix') format('embedded-opentype'),
       url('nahid-font-v0.3.0/Farsi-Digits-Without-Latin/Nahid-FD-WOL.woff') format('woff2'),
       url('nahid-font-v0.3.0/Farsi-Digits-Without-Latin/Nahid-FD-WOL.woff') format('woff'),
       url('nahid-font-v0.3.0/Farsi-Digits-Without-Latin/Nahid-FD-WOL.ttf') format('truetype');
  font-weight: normal;
}

* {
  direction: rtl;
  font-family: "Nahid", Tahoma, Geneva, Verdana, sans-serif;
}


/* توی فایل CSS پروژه‌ت (مثل styles.css) یا توی تگ <style> توی HTML */
#sidebar.leaflet-sidebar {
  display: none !important;
}

/* تنظیمات نوار ناوبار */
.navbar-nav {
  float: right;
}

.navbar-header {
  float: right;
  width: auto;
  max-width: none;
}

/* کانتینر برای لوگو و عنوان */
.navbar-brand-container {
  display: flex;
  align-items: center;
  float: right;
  height: 50px;
  padding: 0;
  margin: 0;
}

.navbar-logo {
  height: 40px;
  width: auto;
  margin-left: 8px;
  margin-right: 0;
  margin-top: 0;
  margin-bottom: 0;
}

.navbar-brand {
  float: none;
  display: inline-block;
  margin: 0;
  padding: 15px 0;
  line-height: 20px;
  font-size: 18px;
  white-space: nowrap;
}

/* تنظیمات دسکتاپ */
@media (min-width: 1367px) {
  .navbar-toggle {
    display: none !important;
  }

  /* در دسکتاپ navbar عادی نمایش داده شود */
  .navbar-nav > li {
    display: block !important;
  }

  .navbar-nav.navbar-right,
  .navbar-nav.navbar-left,
  .navbar-nav:not(.navbar-right):not(.navbar-left) {
    display: block !important;
  }
}

.navbar-collapse {
  text-align: right;
  overflow: visible;
}

/* جلوگیری از overflow در navbar */
.navbar-nav {
  flex-wrap: nowrap;
  overflow: visible;
}

.navbar-fixed-top {
  min-height: 50px;
}

.navbar-header {
  float: right;
  width: auto;
  max-width: none;
}

.navbar-brand-container {
  display: flex;
  align-items: center;
  float: right;
  height: 50px;
  padding: 0;
  margin: 0;
}

.navbar-logo {
  height: 40px;
  width: auto;
  margin-left: 8px;
  margin-right: 0;
}

.navbar-brand {
  float: none;
  display: inline-block;
  margin: 0;
  padding: 15px 0;
  font-size: 18px;
  white-space: nowrap;
}

/* تنظیم navbar-nav برای سمت راست */
.navbar-nav.navbar-right {
  float: right;
  margin-right: 0;
}

.navbar-nav > li {
  float: right;
}

.dropdown-menu {
  right: 0;
  left: auto;
  text-align: right;
}





.navbar-nav > li {
  float: right;
}

.caret {
  margin-right: 5px;
  margin-left: 0;
}

/* استایل برای لیست انتخاب لایه‌ها */
.navbar-nav .layer-select {
  width: 150px; /* عرض ثابت برای لیست */
  height: 34px; /* هم‌اندازه دکمه‌های دیگر */
  padding: 6px 12px;
  font-size: 14px;
  line-height: 1.42857143;
  color: #555;
  background-color: #fff;
  border: 1px solid #ccc;
  border-radius: 4px;
  margin-left: 5px; /* فاصله از آیتم قبلی */
  display: inline-block;
  vertical-align: middle;
}

/* تنظیمات RTL برای select */
.navbar-nav .layer-select {
  text-align: right;
  direction: rtl;
}

/* هاور و فوکوس */
.navbar-nav .layer-select:hover,
.navbar-nav .layer-select:focus {
  border-color: #66afe9;
  outline: 0;
  box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(102,175,233,.6);
}

html, body {
  direction: rtl;
  height: 100%;
  width: 100%;
  overflow: hidden;
  font-family: "Nahid", Tahoma, 'Arial', sans-serif;
}

/* بقیه استایل‌ها بدون تغییر */
body {
  padding-top: 50px;
}

#searchContainer {
  height: 45%;
  padding: 0;
}

#map {
  height: 100%;
}

#table tr {
  cursor: pointer;
}


.loading-indicator {
  position: absolute;
  width: 220px;
  height: 19px;
  top: 50%;
  left: 50%;
  margin: -10px 0 0 -110px;
  z-index: 20001;
}

.loading-bar {
  width: 100%;
}

.rule-value-container {
  max-height: 100px;
  width: 350px;
  overflow: auto;
}

.tab-content {
  padding-top: 10px;
}

.fa-white {
  color: white;
}

.info-control {
  padding: 6px 8px;
  font: 14px/16px 'Tahoma', Arial, Helvetica, sans-serif;
  background: white;
  background: rgba(255,255,255,0.9);
  box-shadow: 0 0 15px rgba(0,0,0,0.2);
  border-radius: 5px;
  text-align: right;
}

.leaflet-control-layers {
  overflow: auto;
}

.leaflet-control-layers label {
  font-weight: normal;
  margin-bottom: 0px;
  text-align: right;
}

.leaflet-control-layers-selector {
  top: 0px;
  margin-left: 5px;
  margin-right: 0;
}

.navbar .navbar-brand {
  font-weight: bold;
  font-size: 25px;
  color: white;
  text-align: right;
}

.navbar-brand img {
  height: 100%;
  margin-top: -5px;
  margin-left: 10px;
  margin-right: 0;
  display: inline;
}

.navbar-nav img {
  height: 40px;
  margin-top: -12px;
  margin-bottom: -10px;
}




@media print {
  .navbar {
    display: none !important;
  }
  .leaflet-control-container {
    display: none !important;
  }
}



#table-container {
  height: 55%;
  padding: 5px;
  position: relative;
  box-shadow: inset 0 8px 8px -8px #696868;
  overflow: auto;
}

/* استایل‌های دکمه close جدول */
.close-table-btn {
  background-color: #d9534f !important;
  border-color: #d43f3a !important;
  color: white !important;
}

.close-table-btn:hover,
.close-table-btn:focus,
.close-table-btn:active {
  background-color: #c9302c !important;
  border-color: #ac2925 !important;
  color: white !important;
}

/* #table-container {
  height: 55%;
  padding: 5px;
  position: relative;
  box-shadow: inset 0 8px 8px -8px #696868;
  display: block !important;  
  visibility: visible !important;  
  overflow: auto;  
} */

#table {
  width: 100% !important; /* عرض کامل */
  display: block !important; /* اطمینان از نمایش */
  visibility: visible !important; /* اطمینان از نمایش */
  height: auto !important; /* ارتفاع خودکار */
}


.draw-tools button {
  margin: 5px;
  padding: 10px;
  font-size: 18px;
  background-color: #f0f0f0;
  border: none;
  border-radius: 5px;
  cursor: pointer;
}

.draw-tools button:hover {
  background-color: #ddd;
}

.leaflet-control-scale {
  font-size: 14px; /* اندازه فونت */
  padding: 5px 10px; /* پدینگ کادر */
}

.leaflet-control-scale-line {
  height: 8px; /* ارتفاع خط */
  border-width: 2px; /* ضخامت خط */
  display: flex; /* از flex استفاده کن برای چیدمان */
  align-items: center; /* عمودی وسط‌چین */
  margin-top: 5px; /* فاصله از بالا برای هر خط */
}

/* اضافه کردن فاصله بین خط و متن */
.leaflet-control-scale-line::before {
  content: ""; /* یه فضای خالی قبل از متن */
  width: 25px; /* فاصله افقی بین خط و عدد */
}


.leaflet-tooltip {
  font-size: 12px;
  background-color: #fff;
  border: 2px solid #333;
  border-radius: 5px;
  padding: 2px;
}

.result-tooltip {
  background-color: #ffcc00;
  border-color: #ff9900;
}

.moving-tooltip {
  background-color: #00ccff;
  border-color: #0099ff;
}

.leaflet-tooltip {
  direction: rtl;
  text-align: right;
}

.leaflet-tooltip.moving-tooltip {
  direction: rtl;
  text-align: right;
}

.leaflet-tooltip.result-tooltip {
  direction: rtl;
  text-align: right;
}

.print-tools button {
  font-size: 24px;
  margin: 5px;
  padding: 5px;
  background: #f0f0f0;
  border: none;
  cursor: pointer;
  width: 40px;
  height: 40px;
  border-radius: 4px;
}
.print-tools button:hover {
  background: #e0e0e0;
}

#layer-dropdown {
  border-radius: 5px;
  border: 1px solid #ccc;
  background-color: #fff;
  color: #333;
}
#layer-table-container {
  max-height: 400px;
  overflow-y: auto;
}


/* کرسر برای زوم جعبه‌ای */
.zoom-in-cursor { cursor: zoom-in !important; }
.zoom-out-cursor { cursor: zoom-out !important; }

/* استایل پنجره زوم */
.leaflet-zoom-box {
    position: absolute;
    border: 2px dashed blue;
    background: rgba(0, 0, 255, 0.2);
    pointer-events: none;
    z-index: 1000;
}

/* جلوگیری از انتخاب عناصر هنگام درگ */
.no-select {
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

/* دکمه‌های زوم جعبه‌ای */
.leaflet-control-box-zoom-in,
.leaflet-control-box-zoom-out {
    width: 32px !important;
    height: 32px !important;
    line-height: 32px !important;
    text-align: center;
    font-size: 18px !important;
    color: black !important;
    background: white !important;
    border-radius: 4px !important;
    border: 1px solid #ccc !important;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 !important; /* حذف فاصله داخلی */
}

/* تنظیم اندازه آیکون داخل دکمه */
.leaflet-control-box-zoom-in i,
.leaflet-control-box-zoom-out i {
    font-size: 16px !important; /* کوچکتر کردن آیکون */
    margin: 0 !important;
    padding: 0 !important;
}


.leaflet-control-box-zoom-in:hover,
.leaflet-control-box-zoom-out:hover {
    background: #f4f4f4 !important;
}

.large-content {
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.large-content {
  max-width: 600px;
  margin: 0 auto;
  padding: 0px; /* کاهش padding */
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.coordinate-form {
  margin: 10px 0; /* کاهش margin */
  font-size: 14px;
}

.form-group {
  margin-bottom: 10px; /* کاهش margin */
}

.coordinate-form label {
  display: inline-block;
  width: 150px;
  margin-right: 10px;
  font-weight: bold;
}

.coordinate-form input {
  height: 22px;
  padding: 5px;
  margin-right: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
}

.btn-coordinate {
  padding: 8px 15px;
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  margin-top: 10px;
}

.btn-coordinate:hover {
  background-color: #45a049;
}

.form-spacer {
  height: 10px; /* کاهش فاصله */
  border-bottom: 1px solid #c40808;
  margin: 10px 0; /* کاهش margin */
}

#utm-result, #latlng-result {
  font-weight: bold;
  color: #333;
}

.leaflet-popup-content {
  font-size: 16px !important; /* اندازه فونت دلخواه */
  line-height: 1.5; /* فاصله خطوط */
}

.leaflet-popup-content-wrapper {
  padding: 10px; /* فاصله داخلی */
}

.btn-delete {
  background-color: #ff4444;
  color: white;
  border: none;
}

.btn-delete:hover {
  background-color: #cc0000;
}

.btn-tool {
  background-color: #ee0ed0; /* رنگ سبز برای تمایز */
  color: white;
  border: none;
  margin: 5px;
  padding: 5px 10px;
  font-size: 14px;
}

.btn-tool:hover {
  background-color: #45a049; /* رنگ تیره‌تر در هاور */
}

.btn-tool.active {
  background-color: #2196F3; /* رنگ آبی وقتی فعاله */
}

#bookmarks-list ul {
  list-style: none;
  padding: 0;
}

#bookmarks-list li {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  border-bottom: 1px solid #ddd;
}

#bookmarks-list li button {
  margin-left: 5px;
  padding: 8px 15px; /* اندازه پدینگ بزرگ‌تر */
  background-color: #f44336; /* رنگ قرمز برای حذف */
  color: white;
  border: none;
  cursor: pointer;
  font-size: 14px; /* اندازه فونت دکمه */
  border-radius: 4px; /* گوشه‌های گرد */
}

#bookmarks-list li button.zoom-to {
  background-color: #2196f3; /* رنگ آبی برای بزرگ‌نمایی */
}

#bookmarks-list li button:hover {
  opacity: 0.8;
}
#bookmarks-list p {
  font-size: 16px; /* اندازه فونت بزرگ‌تر */
  color: #666; /* رنگ مناسب‌تر */
  padding: 10px; /* فاصله داخلی */
  text-align: center; /* مرکزیت متن */
}
#bookmarks-list li span {
  font-size: 13px; /* اندازه فونت بزرگ‌تر (می‌تونید 18px یا بیشتر امتحان کنید) */
  color: #333; /* رنگ متن برای خوانایی بهتر */
  margin-right: 10px; /* فاصله بین نام و مختصات */
}

/* src/css/app.css */
.leaflet-control-geocoder-form input {
  font-size: 16px !important;
  height: 36px !important;
  padding: 5px 10px !important;
}
.leaflet-control-geocoder-alternatives li {
  font-size: 16px !important;
  padding: 8px 12px !important;
}

.opacity-slider-container {
  transition: all 0.3s ease; /* انیمیشن برای نمایش/مخفی کردن */
}

.large-text {
  font-size: 16px !important;
}

.mb-3 {
  margin-bottom: 1rem !important;
}


.form-check {
  align-items: center; /* مطمئن می‌شیم همه عناصر تو یه خط باشن */
}



.btn-descriptive-table {
  padding: 2px 8px; /* عرض و ارتفاع کمتر */
  font-size: 12px; /* متن کوچکتر */
  white-space: nowrap; /* جلوگیری از شکستن متن */
  height: 24px; /* ارتفاع ثابت برای هماهنگی */
}

.large-text {
  font-size: 16px !important;
  margin-right: 10px; /* فاصله بیشتر اگه لازم بود */
}

.change-password-btn,
.logout-btn-sidebar {
  width: 100%; /* عرض کامل */
  padding: 10px 15px; /* فاصله داخلی */
  margin: 10px 0; /* فاصله خارجی */
  font-size: 16px; /* اندازه فونت */
  font-weight: bold; /* ضخامت فونت */
  color: #ece8e8; /* رنگ متن */
  border: none; /* حذف حاشیه */
  border-radius: 5px; /* گوشه‌های گرد */
  cursor: pointer; /* تغییر شکل نشانگر ماوس */
  transition: background-color 0.3s ease; /* انیمیشن تغییر رنگ */
}

.change-password-btn {
  background-color: #4CAF50; /* رنگ سبز */
}

.change-password-btn:hover {
  background-color: #45a049; /* رنگ سبز تیره‌تر هنگام hover */
}

.logout-btn-sidebar {
  background-color: #f44336; /* رنگ قرمز */
}

.logout-btn-sidebar:hover {
  background-color: #d32f2f; /* رنگ قرمز تیره‌تر هنگام hover */
}

.coordinatedImagePreviewControlMainDiv {
  position: absolute;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(255, 255, 255, 0.9);
  padding: 10px;
  border-radius: 5px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
  max-width: 80%;
  max-height: 200px;
  overflow-x: auto;
  overflow-y: hidden;
  white-space: nowrap;
  z-index: 2000; /* مقدار بالا برای قرار گرفتن روی همه عناصر */
}

.coordinatedImagePreviewControlUl {
  list-style: none;
  padding: 0;
  margin: 0;
  display: inline-flex;
  gap: 10px;
}

.coordinatedImagePreviewControlLi {
  display: inline-block;
}

.example-image {
  width: 150px;
  height: 100px;
  object-fit: cover;
}

/* تنظیم جهت باز شدن منوی دراپ‌داون به سمت راست */
.dropdown-menu-right {
  left: auto !important;
  right: -10px !important; /* فاصله منو از دکمه - بسته به نیاز تنظیم کن */
  transform: translateX(100%); /* مطمئن می‌شیم منو کاملاً به سمت راست بره */
}

/* تنظیم z-index برای نمایش روی سایدبار */
.dropdown-menu {
  min-width: 200px;
  z-index: 2000 !important; /* z-index بالا برای نمایش روی سایدبار */
  border: 1px solid #ccc;
  border-radius: 4px;
  box-shadow: 0 6px 12px rgba(0,0,0,.175);
}

/* تنظیم موقعیت دراپ‌داون */
.dropdown {
  position: relative;
}

/* بقیه استایل‌ها */
.layer-item {
  width: 100%;
  display: block;
}

.d-flex.align-items-center {
  align-items: center;
  flex-wrap: nowrap !important;
}

.form-check {
  flex-grow: 1;
  align-items: center;
  display: flex;
}

.dropdown {
  flex-shrink: 0;
  display: inline-block;
}

.dropdown-toggle {
  display: flex;
  align-items: center;
  padding: 2px 8px;
}

.dropdown-item .opacity-slider-container {
  padding: 10px;
  background-color: #fff;
}

.dropdown-item .form-range {
  width: 100%;
}

.btn-default {
  background-color: #fff;
  border: 1px solid #ccc;
}

#layer-checkboxes .d-flex {
  display: flex !important;
  align-items: center !important;
  flex-wrap: nowrap !important;
}

#layer-checkboxes .form-check {
  margin-bottom: 0 !important;
}

.dropdown-item:hover {
  background-color: #d0d0cd !important; /* رنگ پس‌زمینه آبی روشن */
  color: #ed0606 !important; /* رنگ متن سفید */
  border-radius: 4px; /* گوشه‌های گرد برای ظاهر بهتر */
  transition: background-color 0.2s ease; /* انیمیشن نرم برای تغییر رنگ */
}

.custom-label {
  background-color: transparent; /* پس‌زمینه شفاف */
  color: #151414; /* رنگ متن تیره */
  font-size: 25px; /* اندازه فونت */
  font-weight: bold; /* متن بولد */
  padding: 1px 1px; /* فاصله داخلی */
  pointer-events: none; /* جلوگیری از تداخل با کلیک */
}

#toggle-all-layers {
  margin-right: 5px;
}

#toggle-all-layers + label {
  font-weight: bold;
  color: #1a3c34;
  font-size: 16px;
}

#coordinatedImagePreviewControlOpenButton,
#coordinatedImagePreviewControlCloseButton {
    display: none;
}


.time-display {
    background: #222;
    padding: 8px 15px;
    border-radius: 4px;
    font-size: 14px;
    color: white;
    margin: 0 10px;
    display: inline-block;
    vertical-align: middle;
    line-height: 20px;
    margin-top: 5px;
    padding-top: 15px;
}


.layer-label {
  font-size: 12px;
  font-weight: 600;
  color: #111;
  background: rgba(255,255,255,0.6);
  border: 1px solid rgba(0,0,0,0.1);
  padding: 1px 1px;
  border-radius: 3px;
}

/* استایل‌های جستجو در فایل search-custom.css تعریف شده‌اند */

/* Search Container روی نقشه - حالت دسکتاپ */
.map-search-overlay {
  position: absolute !important;
  top: 65px !important; /* مناسب در دسکتاپ */
  left: 50% !important;
  transform: translateX(-50%) !important;
  z-index: 1000 !important;
  width: 320px !important; /* پیش‌فرض برای موبایل */

  /* در دسکتاپ همیشه نمایش داده شود */
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* عرض بزرگ‌تر برای دسکتاپ */
@media screen and (min-width: 1367px) {
  .map-search-overlay {
    width: 470px !important; /* عرض متناسب برای دسکتاپ */
  }
}

.search-panel {
  background: none !important;
  border: none !important;
  border-radius: 4px !important;
  padding: 0 !important;
  margin: 0 !important;
  box-shadow: none !important;
  display: flex !important;
  flex-direction: row !important; /* افقی در دسکتاپ */
  gap: 0 !important;
  align-items: stretch !important;
}

/* اطمینان از افقی بودن در دسکتاپ */
@media screen and (min-width: 1367px) {
  .search-panel {
    flex-direction: row !important; /* افقی در دسکتاپ */
    display: flex !important;
    gap: 4px !important; /* فاصله بین عناصر */
    align-items: stretch !important;
  }
}

/* اطمینان از عمودی بودن در موبایل و تبلت */
@media screen and (max-width: 1366px) {
  .search-panel {
    flex-direction: column !important; /* عمودی در موبایل و تبلت */
    display: flex !important;
  }
}

.search-panel #navbar-layer-select {
  background: #ffffff !important;
  border: 2px solid #333 !important; /* خط تیره */
  color: #333 !important;
  border-radius: 4px !important;
  padding: 6px 8px !important;
  font-size: 13px !important;
  font-family: "Nahid", Tahoma, Geneva, Verdana, sans-serif !important;
  font-weight: normal !important;
  box-shadow: none !important;
  transition: border-color 0.3s ease !important;
  width: 140px !important;
  height: 32px !important;
  margin: 0 !important;
  border-right: 2px solid #333 !important; /* اطمینان از border کامل */
  flex-shrink: 0 !important;
  box-sizing: border-box !important; /* اطمینان از محاسبه صحیح عرض */
}

.search-panel #navbar-layer-select:focus {
  border-color: #66afe9 !important;
  border-right-color: #66afe9 !important; /* اطمینان از border کامل در focus */
  box-shadow: 0 0 5px rgba(102, 175, 233, 0.5) !important;
  outline: none !important;
}

/* اطمینان از نمایش placeholder */
.search-panel #navbar-layer-select option[disabled] {
  color: #999 !important;
  font-style: italic !important;
}

.search-panel #navbar-search-container {
  width: 140px !important;
  flex-shrink: 0 !important;
}

/* اطمینان از عدم نمایش background اضافی */
.search-panel .leaflet-control-search {
  background: none !important;
  border: none !important;
  box-shadow: none !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* حذف دکمه سرچ و مربع آبی */
.search-panel .leaflet-control-search .search-button {
  display: none !important;
}

.search-panel .leaflet-control-search .search-cancel {
  display: none !important;
}

.search-panel .leaflet-control-search .search-input {
  background: #ffffff !important;
  border: 2px solid #333 !important; /* خط تیره */
  color: #333 !important;
  border-radius: 4px 4px 4px 4px !important;
  padding: 6px 8px !important;
  font-size: 13px !important;
  font-family: "Nahid", Tahoma, Geneva, Verdana, sans-serif !important;
  font-weight: normal !important;
  box-shadow: none !important;
  transition: border-color 0.3s ease !important;
  width: 140px !important;
  height: 32px !important;
  margin: 0 !important;
  border-left: 2px solid #333 !important; /* اطمینان از border کامل */
  flex-shrink: 0 !important;
  box-sizing: border-box !important; /* اطمینان از محاسبه صحیح عرض */
}

.search-panel .leaflet-control-search .search-input:focus {
  border-color: #66afe9 !important;
  border-left-color: #66afe9 !important; /* اطمینان از border کامل در focus */
  box-shadow: 0 0 5px rgba(102, 175, 233, 0.5) !important;
  outline: none !important;
}

/* Media Query جامع برای همه دستگاه‌های موبایل و تبلت */
/* iPhone SE, XR, 12 Pro, 14 Pro Max, Pixel 7, Samsung Galaxy S8+, S20 Ultra, iPad Mini/Air/Pro, Surface Pro 7, Surface Duo, Galaxy Z Fold 5, Asus Zenbook Fold, Samsung Galaxy A51/71, Nest Hub */
@media screen and (max-width: 1366px) {
  /* تنظیمات کلی ناوبار - یکسان برای همه دستگاه‌ها */
  .navbar {
    min-height: 50px !important;
    height: 50px !important;
    padding: 0 10px !important;
  }

  /* تنظیمات toolbar جدول برای موبایل و تبلت */
  #table-container [id^="toolbar-layer"] {
    width: 100% !important;
    overflow-x: auto !important;
  }

  #table-container .close-table-btn {
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
    background-color: #d9534f !important;
    border-color: #d43f3a !important;
    color: white !important;
    font-weight: bold !important;
  }

  /* اطمینان از عدم تکرار toggle */
  .navbar-toggle {
    display: block !important;
  }

  .navbar-toggle .icon-bar {
    display: none !important;
  }

  /* اطمینان از عملکرد Bootstrap collapse */
  .navbar-toggle[data-toggle="collapse"] {
    display: block !important;
    pointer-events: auto !important;
  }

  /* مخفی کردن همه آیتم‌های ناوبار شامل search box و combo */
  .navbar-nav > li {
    display: none !important;
  }

  .navbar-nav.navbar-right,
  .navbar-nav.navbar-left,
  .navbar-nav:not(.navbar-right):not(.navbar-left) {
    display: none !important;
  }


  /* اطمینان از مخفی بودن search elements قدیمی */
  .navbar-search-item,
  .navbar-form.navbar-search-item {
    display: none !important;
  }

  /* مخفی کردن search container در موبایل و تبلت */
  .map-search-overlay {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
  }

  /* نمایش search container وقتی active است */
  .map-search-overlay.active {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    animation: fadeInDown 0.3s ease !important;
    top: 80px !important; /* پایین‌تر در موبایل/تبلت */
    left: 10px !important;
    right: 10px !important;
    transform: none !important;
    width: auto !important;
  }

  /* انیمیشن برای نمایش search */
  @keyframes fadeInDown {
    from {
      opacity: 0;
      transform: translateX(-50%) translateY(-20px);
    }
    to {
      opacity: 1;
      transform: translateX(-50%) translateY(0);
    }
  }



  /* تنظیم هدر برای نمایش سه عنصر */
  .navbar-header {
    width: 100% !important;
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    margin: 0 !important;
    padding: 0 !important;
    height: 50px !important;
  }

  /* لوگو سایت - گوشه چپ */
  .navbar-brand-container {
    order: 1 !important;
    display: flex !important;
    align-items: center !important;
    height: 50px !important;
    margin: 0 !important;
    padding: 0 !important;
    flex-shrink: 0 !important;
  }

  .navbar-logo {
    height: 30px !important;
    width: auto !important;
    margin: 0 5px 0 0 !important;
  }

  /* نام سایت - وسط */
  .navbar-brand {
    order: 2 !important;
    flex: 1 !important;
    text-align: center !important;
    font-size: 16px !important;
    margin: 0 !important;
    padding: 0 10px !important;
    line-height: 50px !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
  }

  /* دکمه ذره‌بین - گوشه راست */
  .navbar-toggle {
    order: 3 !important;
    display: block !important;
    margin: 0 !important;
    padding: 10px !important;
    background-color: transparent !important;
    border: 2px solid #3498db !important;
    border-radius: 8px !important;
    flex-shrink: 0 !important;
    height: 40px !important;
    width: 40px !important;
    cursor: pointer !important;
    position: relative !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 2px 8px rgba(52, 152, 219, 0.2) !important;
    z-index: 1000 !important;
    pointer-events: auto !important;

    /* اطمینان از قابلیت کلیک */
    user-select: none !important;
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;

    /* حذف outline پیش‌فرض */
    outline: none !important;

    /* اطمینان از عدم تداخل */
    float: none !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
  }

  .navbar-toggle .icon-bar {
    display: none !important;
  }

  /* آیکون ذره‌بین واقعی */
  .navbar-toggle::before {
    content: "🔍" !important;
    font-size: 18px !important;
    line-height: 1 !important;
    color: #fff !important;
    display: block !important;
    text-align: center !important;
  }

  .navbar-toggle::after {
    display: none !important;
  }

  .navbar-toggle:hover,
  .navbar-toggle:focus {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%) !important;
    border-color: #e74c3c !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 20px rgba(231, 76, 60, 0.4) !important;
  }

  .navbar-toggle:hover::before,
  .navbar-toggle:focus::before {
    color: #fff !important;
    transform: scale(1.1) !important;
  }

  /* Active state */
  .navbar-toggle:active {
    transform: translateY(0px) !important;
    box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3) !important;
  }

  /* تنظیم body padding */
  body {
    padding-top: 50px !important;
  }

  /* تنظیمات responsive برای موبایل/تبلت portrait */
  @media (max-width: 1366px) and (orientation: portrait) {
    .map-search-overlay.active {
      top: 85px !important; /* پایین‌تر در portrait */
      left: 10px !important;
      right: 10px !important;
      transform: none !important;
      width: auto !important;
    }
  }

  /* تنظیمات responsive برای موبایل/تبلت landscape */
  @media (max-width: 1366px) and (orientation: landscape) {
    .map-search-overlay.active {
      top: 110px !important; /* بیشتر پایین‌تر در landscape */
      left: 10px !important;
      right: 10px !important;
      transform: none !important;
      width: auto !important;
    }

    .search-panel {
      flex-direction: column !important;
      gap: 0 !important;
      display: flex !important;
    }

    .search-panel #navbar-search-container {
      width: 100% !important;
    }

    .search-panel #navbar-layer-select {
      width: 100% !important;
      border-radius: 4px !important;
      border: 2px solid #333 !important;
      box-sizing: border-box !important;
      height: 36px !important;
      padding: 6px 8px !important;
      font-size: 12px !important;
    }

    .search-panel .leaflet-control-search .search-input {
      width: 100% !important;
      border-radius: 4px !important;
      border: 2px solid #333 !important;
      box-sizing: border-box !important;
      height: 36px !important;
      padding: 6px 8px !important;
      font-size: 12px !important;
    }
  }

    .search-panel {
      flex-direction: column !important; /* عمودی در تبلت */
      gap: 0 !important;
      display: flex !important;
    }

    .search-panel #navbar-search-container {
      width: 100% !important;
    }

    .search-panel #navbar-layer-select {
      width: 100% !important;
      border-radius: 4px !important;
      border: 2px solid #333 !important; /* خط تیره */
      box-sizing: border-box !important; /* اطمینان از محاسبه صحیح عرض */
      height: 36px !important;
      padding: 6px 8px !important;
      font-size: 12px !important;
    }

    .search-panel .leaflet-control-search .search-input {
      width: 100% !important;
      border-radius: 4px !important;
      border: 2px solid #333 !important; /* خط تیره */
      box-sizing: border-box !important; /* اطمینان از محاسبه صحیح عرض */
      height: 36px !important; /* ارتفاع کمتر */
      padding: 6px 8px !important;
      font-size: 12px !important;
    }
  }

  /* تنظیمات responsive برای موبایل‌های کوچک */
  @media (max-width: 480px) {
    /* تنظیمات search container برای موبایل کوچک portrait */
    .map-search-overlay.active {
      top: 85px !important; /* پایین‌تر */
      left: 10px !important;
      right: 10px !important;
      transform: none !important;
      width: auto !important;
    }

    /* تنظیمات toolbar جدول برای موبایل */
    #table-container [id^="toolbar-layer"] {
      width: 100% !important;
      overflow-x: auto !important;
      white-space: nowrap !important;
    }

    #table-container .btn-group {
      display: inline-flex !important;
      flex-wrap: nowrap !important;
    }

    #table-container .close-table-btn {
      display: inline-block !important;
      visibility: visible !important;
      opacity: 1 !important;
      min-width: 40px !important;
      padding: 6px 8px !important;
      font-size: 14px !important;
      background-color: #d9534f !important;
      border-color: #d43f3a !important;
      color: white !important;
    }
  }

  /* تنظیمات responsive برای موبایل کوچک landscape */
  @media (max-width: 480px) and (orientation: landscape) {
    .map-search-overlay.active {
      top: 100px !important; /* بیشتر پایین‌تر در landscape */
      left: 10px !important;
      right: 10px !important;
      transform: none !important;
      width: auto !important;
    }

    .search-panel {
      flex-direction: column !important; /* عمودی در موبایل */
      gap: 0 !important;
      display: flex !important;
    }

    .search-panel #navbar-search-container {
      width: 100% !important;
    }

    .search-panel #navbar-layer-select {
      width: 100% !important;
      border-radius: 4px !important;
      border: 2px solid #333 !important; /* خط تیره */
      box-sizing: border-box !important; /* اطمینان از محاسبه صحیح عرض */
      height: 36px !important;
      padding: 6px 8px !important;
      font-size: 12px !important;
    }

    .search-panel .leaflet-control-search .search-input {
      width: 100% !important;
      border-radius: 4px !important;
      border-top: 2px solid #333 !important; /* اطمینان از border کامل */
      border: 2px solid #333 !important; /* خط تیره */
      box-sizing: border-box !important; /* اطمینان از محاسبه صحیح عرض */
      height: 36px !important; /* ارتفاع کمتر */
      padding: 6px 8px !important;
      font-size: 12px !important;
    }
    .navbar {
      min-height: 45px !important;
      height: 45px !important;
    }

    .navbar-header {
      height: 45px !important;
    }

    .navbar-brand {
      font-size: 14px !important;
      line-height: 45px !important;
      padding: 0 5px !important;
    }

    .navbar-logo {
      height: 25px !important;
      margin: 10px 5px 0 0 !important;
    }

    .navbar-toggle {
      height: 35px !important;
      width: 35px !important;
      padding: 6px !important;
    }

    .navbar-toggle::before {
      font-size: 16px !important;
    }

    body {
      padding-top: 45px !important;
    }


  }



  /* مخفی کردن همه عناصر غیر جستجو در منوی باز */
  .navbar-collapse.in .time-display,
  .navbar-collapse.in #username-display,
  .navbar-collapse.in .navbar-nav > li:not(.navbar-search-item),
  .navbar-collapse.in .dropdown-menu {
    display: none !important;
  }







